class AppConfig {
  // 应用信息
  static const String adminAppName = '跨境电商管理系统 - 管理端';
  static const String appVersion = '1.0.0';
  
  // API配置
  static const String apiBaseUrl = 'http://127.0.0.1:8000/api';
  static const int apiTimeout = 30000; // 30秒
  
  // 管理员登录端点
  static const String adminLoginEndpoint = '/admin/login';
  static const String adminLogoutEndpoint = '/admin/logout';
  static const String adminProfileEndpoint = '/admin/profile';
  
  // 管理功能端点
  static const String usersEndpoint = '/admin/users';
  static const String productsEndpoint = '/admin/products';
  static const String ordersEndpoint = '/admin/orders';
  static const String customersEndpoint = '/admin/customers';
  static const String statsEndpoint = '/admin/stats';
  
  // 本地存储键
  static const String tokenKey = 'admin_auth_token';
  static const String userDataKey = 'admin_user_data';
  static const String settingsKey = 'admin_settings';
  
  // 界面配置
  static const double sidebarWidth = 280.0;
  static const double topBarHeight = 70.0;
  
  // 分页配置
  static const int defaultPageSize = 20;
  static const int maxPageSize = 100;
}
