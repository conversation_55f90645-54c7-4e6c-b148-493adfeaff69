# 跨境电商管理系统API文档

## 基础URL

```
http://localhost:8000/api
```

## 认证

当前版本的API不需要认证，但管理员功能需要通过登录获取访问令牌。

### 管理员登录

- **URL**: `/api/admin/login`
- **方法**: `POST`
- **请求体**:

```json
{
  "username": "string",
  "password": "string"
}
```

- **成功响应**:

```json
{
  "message": "登录成功",
  "token": "string",
  "user": {
    "id": "integer",
    "username": "string",
    "email": "string",
    "role": "string"
  }
}
```

- **错误响应**:

```json
{
  "error": "string"
}
```

HTTP状态码:
- `200` - 登录成功
- `400` - 请求参数错误
- `401` - 用户名或密码错误
- `403` - 权限不足
- `500` - 服务器内部错误

## 错误处理

API使用HTTP状态码来表示成功或失败:

- `200` - 请求成功
- `201` - 创建成功
- `400` - 请求参数错误
- `404` - 资源不存在
- `405` - 方法不允许
- `500` - 服务器内部错误

错误响应格式:

```json
{
  "error": "错误信息"
}
```

## 用户管理

### 获取所有用户

```
GET /users
```

响应:

```json
[
  {
    "id": 1,
    "username": "testuser",
    "email": "<EMAIL>",
    "first_name": "Test",
    "last_name": "User",
    "phone": null,
    "avatar": null,
    "role": "staff",
    "status": 1,
    "created_at": "2025-07-31 02:15:10",
    "updated_at": "2025-07-31 02:15:10"
  }
]
```

### 创建用户

- **URL**: `/api/users`
- **方法**: `POST`
- **请求体**:

```json
{
  "username": "string",
  "email": "string",
  "password_hash": "string"
}
```

- **验证规则**:
  - username: 必填，3-50个字符
  - email: 必填，有效的邮箱地址，最多100个字符
  - password_hash: 必填，至少6个字符

- **成功响应**:

```json
{
  "id": "integer",
  "username": "string",
  "email": "string",
  "password_hash": "string",
  "created_at": "datetime",
  "updated_at": "datetime"
}
```

- **错误响应**:

```json
{
  "error": "string"
}
```

### 获取用户

```
GET /users/{id}
```

响应:

```json
{
  "id": 1,
  "username": "testuser",
  "email": "<EMAIL>",
  "first_name": "Test",
  "last_name": "User",
  "phone": null,
  "avatar": null,
  "role": "staff",
  "status": 1,
  "created_at": "2025-07-31 02:15:10",
  "updated_at": "2025-07-31 02:15:10"
}
```

### 更新用户

```
PUT /users/{id}
```

请求体:

```json
{
  "first_name": "Updated",
  "last_name": "User",
  "phone": "0987654321"
}
```

响应:

```json
{
  "id": 1,
  "username": "testuser",
  "email": "<EMAIL>",
  "first_name": "Updated",
  "last_name": "User",
  "phone": "0987654321",
  "avatar": null,
  "role": "staff",
  "status": 1,
  "created_at": "2025-07-31 02:15:10",
  "updated_at": "2025-07-31 02:20:15"
}
```

### 删除用户

```
DELETE /users/{id}
```

响应:

```json
{
  "message": "用户删除成功"
}
```

## 统计数据

### 获取系统统计数据

```
GET /stats
```

响应:

```json
{
  "userCount": 42,
  "orderCount": 150,
  "productCount": 200
}
```