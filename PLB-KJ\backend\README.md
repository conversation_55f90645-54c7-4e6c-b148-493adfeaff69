# 跨境电商管理系统后端

这是一个基于PHP的跨境电商管理系统后端API项目。

## 项目结构

```
backend/
├── src/
│   ├── Controllers/
│   ├── Models/
│   ├── Helpers/
│   ├── Config/
│   └── ...
├── public/
├── routes/
├── composer.json
└── README.md
```

## 安装依赖

```bash
composer install
```

## 数据库配置

在 `src/Config/database.php` 文件中配置数据库连接信息。

## 启动服务

使用PHP内置服务器启动项目：

```bash
php -S localhost:8000 -t public
```

## 前端界面

项目包含一个简单的用户管理前端界面，可以通过访问 `http://localhost:8000/users.html` 查看。

## API接口

详细API文档请查看 [API_DOCUMENTATION.md](API_DOCUMENTATION.md) 文件。

### 用户管理

- `GET /api/users` - 获取所有用户
- `POST /api/users` - 创建用户
- `GET /api/users/{id}` - 根据ID获取用户
- `PUT /api/users/{id}` - 根据ID更新用户
- `DELETE /api/users/{id}` - 根据ID删除用户

## 测试API

可以运行测试脚本验证API是否正常工作：

```bash
php test_api.php
```