<?php

// 检查数据库中的用户数据

// 加载配置
require_once __DIR__ . '/vendor/autoload.php';

$dbConfig = require __DIR__ . '/src/Config/database.php';

try {
    // 创建数据库连接
    $dsn = "mysql:host={$dbConfig['host']};port={$dbConfig['port']};dbname={$dbConfig['database']};charset={$dbConfig['charset']}";
    $pdo = new PDO($dsn, $dbConfig['username'], $dbConfig['password'], [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
        PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
    ]);
    
    // 查询所有用户
    $stmt = $pdo->query("SELECT id, username, email, role FROM plb_kj_users");
    $users = $stmt->fetchAll();
    
    echo "数据库中的用户:\n";
    foreach ($users as $user) {
        echo "ID: {$user['id']}, Username: {$user['username']}, Email: {$user['email']}, Role: {$user['role']}\n";
    }
    
    // 检查是否有管理员用户
    $stmt = $pdo->prepare("SELECT id, username, role FROM plb_kj_users WHERE role = 'admin'");
    $stmt->execute();
    $adminUsers = $stmt->fetchAll();
    
    echo "\n管理员用户:\n";
    if (empty($adminUsers)) {
        echo "没有找到管理员用户\n";
    } else {
        foreach ($adminUsers as $admin) {
            echo "ID: {$admin['id']}, Username: {$admin['username']}\n";
        }
    }
    
} catch (PDOException $e) {
    echo "数据库连接失败: " . $e->getMessage() . "\n";
}