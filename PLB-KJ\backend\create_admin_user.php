<?php

// 创建管理员用户的脚本

// 加载配置
require_once __DIR__ . '/vendor/autoload.php';

$dbConfig = require __DIR__ . '/src/Config/database.php';

try {
    // 创建数据库连接
    $dsn = "mysql:host={$dbConfig['host']};port={$dbConfig['port']};dbname={$dbConfig['database']};charset={$dbConfig['charset']}";
    $pdo = new PDO($dsn, $dbConfig['username'], $dbConfig['password'], [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
        PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
    ]);
    
    // 检查是否已存在管理员用户
    $stmt = $pdo->prepare("SELECT id FROM plb_kj_users WHERE username = 'admin'");
    $stmt->execute();
    $existingUser = $stmt->fetch();
    
    if ($existingUser) {
        // 如果存在，更新密码
        $passwordHash = password_hash('admin123', PASSWORD_DEFAULT);
        $stmt = $pdo->prepare("UPDATE plb_kj_users SET password_hash = ? WHERE username = 'admin'");
        $stmt->execute([$passwordHash]);
        echo "管理员用户密码已更新!\n";
    } else {
        // 创建新管理员用户
        $username = 'admin';
        $email = '<EMAIL>';
        $password = 'admin123';
        $passwordHash = password_hash($password, PASSWORD_DEFAULT);
        $role = 'admin';
        
        $stmt = $pdo->prepare("INSERT INTO plb_kj_users (username, email, password_hash, role, status) VALUES (?, ?, ?, ?, 1)");
        $stmt->execute([$username, $email, $passwordHash, $role]);
        
        $userId = $pdo->lastInsertId();
        echo "管理员用户创建成功!\n";
        echo "用户ID: {$userId}\n";
        echo "用户名: {$username}\n";
        echo "邮箱: {$email}\n";
        echo "密码: {$password}\n";
        echo "角色: {$role}\n";
    }
    
} catch (PDOException $e) {
    echo "操作失败: " . $e->getMessage() . "\n";
}