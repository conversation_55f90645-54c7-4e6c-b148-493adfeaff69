[2025-07-31 02:20:58] ERROR: 未捕获的异常: call_user_func(): Argument #1 ($callback) must be a valid callback, non-static method App\Controllers\UserController::getAll() cannot be called statically in D:\PLB-Links\PLB-KJ\backend\src\Routing\Router.php on line 97
[2025-07-31 02:20:58] ERROR: 未捕获的异常: call_user_func(): Argument #1 ($callback) must be a valid callback, non-static method App\Controllers\UserController::create() cannot be called statically in D:\PLB-Links\PLB-KJ\backend\src\Routing\Router.php on line 97
[2025-07-31 02:21:15] INFO: 数据库连接成功: localhost:3306/plb_kj
[2025-07-31 02:21:15] INFO: 数据库连接成功: localhost:3306/plb_kj
[2025-07-31 02:22:19] INFO: 数据库连接成功: localhost:3306/plb_kj
[2025-07-31 02:22:19] INFO: 数据库连接成功: localhost:3306/plb_kj
[2025-07-31 02:22:31] INFO: 数据库连接成功: localhost:3306/plb_kj
[2025-07-31 02:22:31] INFO: 数据库连接成功: localhost:3306/plb_kj
[2025-07-31 02:25:01] INFO: 数据库连接成功: localhost:3306/plb_kj
[2025-07-31 02:25:01] INFO: 数据库连接成功: localhost:3306/plb_kj
[2025-07-31 02:25:17] INFO: 数据库连接成功: localhost:3306/plb_kj
[2025-07-31 02:25:17] INFO: 数据库连接成功: localhost:3306/plb_kj
[2025-07-31 02:25:17] INFO: 数据库连接成功: localhost:3306/plb_kj
[2025-07-31 02:30:11] INFO: 数据库连接成功: localhost:3306/plb_kj
[2025-07-31 02:32:12] INFO: 数据库连接成功: localhost:3306/plb_kj
[2025-07-31 02:35:03] INFO: 数据库连接成功: localhost:3306/plb_kj
[2025-07-31 02:35:03] INFO: 数据库连接成功: localhost:3306/plb_kj
[2025-07-31 02:35:03] INFO: 数据库连接成功: localhost:3306/plb_kj
[2025-07-31 02:35:38] INFO: 数据库连接成功: localhost:3306/plb_kj
[2025-07-31 02:35:38] INFO: 数据库连接成功: localhost:3306/plb_kj
[2025-07-31 02:35:38] INFO: 数据库连接成功: localhost:3306/plb_kj
[2025-07-31 02:35:53] INFO: 数据库连接成功: localhost:3306/plb_kj
[2025-07-31 02:35:56] INFO: 数据库连接成功: localhost:3306/plb_kj
[2025-07-31 02:35:56] INFO: 数据库连接成功: localhost:3306/plb_kj
[2025-07-31 02:36:36] INFO: 数据库连接成功: localhost:3306/plb_kj
[2025-07-31 02:36:36] INFO: 数据库连接成功: localhost:3306/plb_kj
[2025-07-31 02:36:36] INFO: 数据库连接成功: localhost:3306/plb_kj
[2025-07-31 02:41:34] INFO: 数据库连接成功: localhost:3306/plb_kj
[2025-07-31 02:41:34] INFO: 数据库连接成功: localhost:3306/plb_kj
[2025-07-31 02:41:34] INFO: 数据库连接成功: localhost:3306/plb_kj
[2025-07-31 02:41:48] INFO: 数据库连接成功: localhost:3306/plb_kj
[2025-07-31 02:41:48] INFO: 数据库连接成功: localhost:3306/plb_kj
[2025-07-31 02:41:48] INFO: 数据库连接成功: localhost:3306/plb_kj
[2025-07-31 02:44:01] INFO: 数据库连接成功: localhost:3306/plb_kj
[2025-07-31 02:44:03] INFO: 数据库连接成功: localhost:3306/plb_kj
[2025-07-31 02:44:03] INFO: 数据库连接成功: localhost:3306/plb_kj
[2025-07-31 02:50:28] INFO: 数据库连接成功: localhost:3306/plb_kj
[2025-07-31 02:50:29] INFO: 数据库连接成功: localhost:3306/plb_kj
[2025-07-31 02:50:31] INFO: 数据库连接成功: localhost:3306/plb_kj
[2025-07-31 02:50:31] INFO: 数据库连接成功: localhost:3306/plb_kj
[2025-07-31 02:50:41] INFO: 数据库连接成功: localhost:3306/plb_kj
[2025-07-31 02:50:41] INFO: 数据库连接成功: localhost:3306/plb_kj
[2025-07-31 02:50:56] INFO: 数据库连接成功: localhost:3306/plb_kj
[2025-07-31 02:50:56] INFO: 数据库连接成功: localhost:3306/plb_kj
[2025-07-31 03:04:17] INFO: 数据库连接成功: localhost:3306/plb_kj
[2025-07-31 03:04:22] INFO: 数据库连接成功: localhost:3306/plb_kj
[2025-07-31 09:44:16] INFO: 数据库连接成功: localhost:3306/plb_kj
[2025-07-31 09:46:01] ERROR: 未捕获的异常: Class "Router" not found in D:\PLB-Links\PLB-KJ\backend\public\index.php on line 32
[2025-07-31 09:52:59] INFO: 数据库连接成功: localhost:3306/plb_kj
[2025-07-31 14:27:59] INFO: 数据库连接成功: localhost:3306/plb_kj
[2025-07-31 14:28:13] INFO: 数据库连接成功: localhost:3306/plb_kj
[2025-07-31 22:39:40] ERROR: 未捕获的异常: Call to undefined method App\Routing\Router::use() in D:\PLB-Links\PLB-KJ\backend\routes\api.php on line 13
[2025-07-31 22:41:00] ERROR: 未捕获的异常: Call to undefined method App\Routing\Router::use() in D:\PLB-Links\PLB-KJ\backend\routes\api.php on line 13
[2025-07-31 22:43:06] ERROR: 未捕获的异常: Call to undefined method App\Routing\Router::use() in D:\PLB-Links\PLB-KJ\backend\routes\api.php on line 13
[2025-07-31 22:43:07] ERROR: 未捕获的异常: Call to undefined method App\Routing\Router::use() in D:\PLB-Links\PLB-KJ\backend\routes\api.php on line 13
[2025-07-31 22:43:08] ERROR: 未捕获的异常: Call to undefined method App\Routing\Router::use() in D:\PLB-Links\PLB-KJ\backend\routes\api.php on line 13
[2025-07-31 22:43:08] ERROR: 未捕获的异常: Call to undefined method App\Routing\Router::use() in D:\PLB-Links\PLB-KJ\backend\routes\api.php on line 13
[2025-07-31 22:43:08] ERROR: 未捕获的异常: Call to undefined method App\Routing\Router::use() in D:\PLB-Links\PLB-KJ\backend\routes\api.php on line 13
[2025-07-31 22:43:09] ERROR: 未捕获的异常: Call to undefined method App\Routing\Router::use() in D:\PLB-Links\PLB-KJ\backend\routes\api.php on line 13
[2025-07-31 22:43:09] ERROR: 未捕获的异常: Call to undefined method App\Routing\Router::use() in D:\PLB-Links\PLB-KJ\backend\routes\api.php on line 13
[2025-07-31 22:43:26] ERROR: 未捕获的异常: Call to undefined method App\Routing\Router::use() in D:\PLB-Links\PLB-KJ\backend\routes\api.php on line 13
[2025-07-31 22:46:29] ERROR: 未捕获的异常: Call to undefined method App\Routing\Router::group() in D:\PLB-Links\PLB-KJ\backend\routes\api.php on line 21
[2025-07-31 22:46:29] ERROR: 未捕获的异常: Call to undefined method App\Routing\Router::group() in D:\PLB-Links\PLB-KJ\backend\routes\api.php on line 21
[2025-07-31 22:46:30] ERROR: 未捕获的异常: Call to undefined method App\Routing\Router::group() in D:\PLB-Links\PLB-KJ\backend\routes\api.php on line 21
[2025-07-31 22:46:30] ERROR: 未捕获的异常: Call to undefined method App\Routing\Router::group() in D:\PLB-Links\PLB-KJ\backend\routes\api.php on line 21
[2025-07-31 22:46:30] ERROR: 未捕获的异常: Call to undefined method App\Routing\Router::group() in D:\PLB-Links\PLB-KJ\backend\routes\api.php on line 21
[2025-07-31 22:46:30] ERROR: 未捕获的异常: Call to undefined method App\Routing\Router::group() in D:\PLB-Links\PLB-KJ\backend\routes\api.php on line 21
[2025-07-31 22:46:30] ERROR: 未捕获的异常: Call to undefined method App\Routing\Router::group() in D:\PLB-Links\PLB-KJ\backend\routes\api.php on line 21
[2025-07-31 22:46:30] ERROR: 未捕获的异常: Call to undefined method App\Routing\Router::group() in D:\PLB-Links\PLB-KJ\backend\routes\api.php on line 21
[2025-07-31 22:46:31] ERROR: 未捕获的异常: Call to undefined method App\Routing\Router::group() in D:\PLB-Links\PLB-KJ\backend\routes\api.php on line 21
[2025-07-31 22:46:31] ERROR: 未捕获的异常: Call to undefined method App\Routing\Router::group() in D:\PLB-Links\PLB-KJ\backend\routes\api.php on line 21
[2025-07-31 22:46:31] ERROR: 未捕获的异常: Call to undefined method App\Routing\Router::group() in D:\PLB-Links\PLB-KJ\backend\routes\api.php on line 21
[2025-07-31 22:46:32] ERROR: 未捕获的异常: Call to undefined method App\Routing\Router::group() in D:\PLB-Links\PLB-KJ\backend\routes\api.php on line 21
[2025-07-31 22:46:32] ERROR: 未捕获的异常: Call to undefined method App\Routing\Router::group() in D:\PLB-Links\PLB-KJ\backend\routes\api.php on line 21
[2025-07-31 22:46:40] ERROR: 未捕获的异常: Call to undefined method App\Routing\Router::group() in D:\PLB-Links\PLB-KJ\backend\routes\api.php on line 21
[2025-07-31 23:10:20] INFO: 数据库连接成功: 127.0.0.1:3306/plb_kj
[2025-07-31 23:10:20] INFO: Admin login attempt for username: admin
[2025-07-31 23:10:20] ERROR: 未捕获的异常: Class "Logger" not found in D:\PLB-Links\PLB-KJ\backend\src\Models\User.php on line 32
[2025-07-31 23:12:01] INFO: 数据库连接成功: 127.0.0.1:3306/plb_kj
[2025-07-31 23:12:01] INFO: Admin login attempt for username: admin
[2025-07-31 23:12:01] INFO: Attempting to find user by username: admin
[2025-07-31 23:12:01] ERROR: PHP错误: Undefined property: App\Models\User::$db in D:\PLB-Links\PLB-KJ\backend\src\Models\User.php on line 33
[2025-07-31 23:12:01] ERROR: Unexpected error during admin login: Undefined property: App\Models\User::$db
[2025-07-31 23:12:01] INFO: 数据库连接成功: 127.0.0.1:3306/plb_kj
[2025-07-31 23:12:01] INFO: Admin login attempt for username: admin
[2025-07-31 23:12:01] INFO: Attempting to find user by username: admin
[2025-07-31 23:12:01] ERROR: PHP错误: Undefined property: App\Models\User::$db in D:\PLB-Links\PLB-KJ\backend\src\Models\User.php on line 33
[2025-07-31 23:12:01] ERROR: Unexpected error during admin login: Undefined property: App\Models\User::$db
[2025-07-31 23:12:58] INFO: 数据库连接成功: 127.0.0.1:3306/plb_kj
[2025-07-31 23:12:58] INFO: Admin login attempt for username: admin
[2025-07-31 23:12:58] INFO: Attempting to find user by username: admin
[2025-07-31 23:12:58] ERROR: PHP错误: Undefined property: App\Models\User::$db in D:\PLB-Links\PLB-KJ\backend\src\Models\User.php on line 33
[2025-07-31 23:12:58] ERROR: Unexpected error during admin login: Undefined property: App\Models\User::$db
[2025-08-01 01:54:21][INFO] Request: POST /api/api/admin/login
[2025-08-01 01:54:21][INFO] Registering route: POST /api/admin/login
[2025-08-01 01:54:21][INFO] Registering route: POST /api/users/login
[2025-08-01 01:54:21][INFO] Registering route: GET /api/auth/validate
[2025-08-01 01:54:21][INFO] Registering route: GET /api/users
[2025-08-01 01:54:21][INFO] Registering route: POST /api/users
[2025-08-01 01:54:21][INFO] Registering route: GET /api/users/profile
[2025-08-01 01:54:21][INFO] Registering route: GET /api/users/:id
[2025-08-01 01:54:21][INFO] Registering route: PUT /api/users/:id
[2025-08-01 01:54:21][INFO] Registering route: DELETE /api/users/:id
[2025-08-01 01:54:21][INFO] Registering route: GET /api/stats
[2025-08-01 01:54:21][INFO] Registering route: GET /api/stats/:type
[2025-08-01 01:54:21][INFO] Dispatching request: POST /api/api/admin/login
[2025-08-01 01:54:21][WARNING] Route not found: POST /api/api/admin/login
[2025-08-01 01:54:22][INFO] Request: POST /api/api/admin/login
[2025-08-01 01:54:22][INFO] Registering route: POST /api/admin/login
[2025-08-01 01:54:22][INFO] Registering route: POST /api/users/login
[2025-08-01 01:54:22][INFO] Registering route: GET /api/auth/validate
[2025-08-01 01:54:22][INFO] Registering route: GET /api/users
[2025-08-01 01:54:22][INFO] Registering route: POST /api/users
[2025-08-01 01:54:22][INFO] Registering route: GET /api/users/profile
[2025-08-01 01:54:22][INFO] Registering route: GET /api/users/:id
[2025-08-01 01:54:22][INFO] Registering route: PUT /api/users/:id
[2025-08-01 01:54:22][INFO] Registering route: DELETE /api/users/:id
[2025-08-01 01:54:22][INFO] Registering route: GET /api/stats
[2025-08-01 01:54:22][INFO] Registering route: GET /api/stats/:type
[2025-08-01 01:54:22][INFO] Dispatching request: POST /api/api/admin/login
[2025-08-01 01:54:22][WARNING] Route not found: POST /api/api/admin/login
[2025-08-01 01:59:16][INFO] Request: POST /api/admin/login
[2025-08-01 01:59:16][INFO] Registering route: POST /api/admin/login
[2025-08-01 01:59:16][INFO] Registering route: POST /api/users/login
[2025-08-01 01:59:16][INFO] Registering route: GET /api/auth/validate
[2025-08-01 01:59:16][INFO] Registering route: GET /api/users
[2025-08-01 01:59:16][INFO] Registering route: POST /api/users
[2025-08-01 01:59:16][INFO] Registering route: GET /api/users/profile
[2025-08-01 01:59:16][INFO] Registering route: GET /api/users/:id
[2025-08-01 01:59:16][INFO] Registering route: PUT /api/users/:id
[2025-08-01 01:59:16][INFO] Registering route: DELETE /api/users/:id
[2025-08-01 01:59:16][INFO] Registering route: GET /api/stats
[2025-08-01 01:59:16][INFO] Registering route: GET /api/stats/:type
[2025-08-01 01:59:16][INFO] Dispatching request: POST /api/admin/login
[2025-08-01 01:59:16][INFO] 数据库连接成功: 127.0.0.1:3306/plb_kj
[2025-08-01 01:59:16][INFO] Admin login attempt for username: admin
[2025-08-01 01:59:16][INFO] Admin user logged in successfully: admin
[2025-08-01 01:59:33][INFO] Request: POST /api/users/login
[2025-08-01 01:59:33][INFO] Registering route: POST /api/admin/login
[2025-08-01 01:59:33][INFO] Registering route: POST /api/users/login
[2025-08-01 01:59:33][INFO] Registering route: GET /api/auth/validate
[2025-08-01 01:59:33][INFO] Registering route: GET /api/users
[2025-08-01 01:59:33][INFO] Registering route: POST /api/users
[2025-08-01 01:59:33][INFO] Registering route: GET /api/users/profile
[2025-08-01 01:59:33][INFO] Registering route: GET /api/users/:id
[2025-08-01 01:59:33][INFO] Registering route: PUT /api/users/:id
[2025-08-01 01:59:33][INFO] Registering route: DELETE /api/users/:id
[2025-08-01 01:59:33][INFO] Registering route: GET /api/stats
[2025-08-01 01:59:33][INFO] Registering route: GET /api/stats/:type
[2025-08-01 01:59:33][INFO] Dispatching request: POST /api/users/login
[2025-08-01 01:59:33][INFO] 数据库连接成功: 127.0.0.1:3306/plb_kj
[2025-08-01 01:59:33][INFO] Raw request data: {"username":"admin","password":"admin123"}
[2025-08-01 02:02:11][INFO] Request: POST /api/users/login
[2025-08-01 02:02:11][INFO] Registering route: POST /api/admin/login
[2025-08-01 02:02:11][INFO] Registering route: POST /api/users/login
[2025-08-01 02:02:11][INFO] Registering route: GET /api/auth/validate
[2025-08-01 02:02:11][INFO] Registering route: GET /api/users
[2025-08-01 02:02:11][INFO] Registering route: POST /api/users
[2025-08-01 02:02:11][INFO] Registering route: GET /api/users/profile
[2025-08-01 02:02:11][INFO] Registering route: GET /api/users/:id
[2025-08-01 02:02:11][INFO] Registering route: PUT /api/users/:id
[2025-08-01 02:02:11][INFO] Registering route: DELETE /api/users/:id
[2025-08-01 02:02:11][INFO] Registering route: GET /api/stats
[2025-08-01 02:02:11][INFO] Registering route: GET /api/stats/:type
[2025-08-01 02:02:11][INFO] Dispatching request: POST /api/users/login
[2025-08-01 02:02:11][INFO] 数据库连接成功: 127.0.0.1:3306/plb_kj
[2025-08-01 02:02:25][INFO] Request: POST /api/users/login
[2025-08-01 02:02:25][INFO] Registering route: POST /api/admin/login
[2025-08-01 02:02:25][INFO] Registering route: POST /api/users/login
[2025-08-01 02:02:25][INFO] Registering route: GET /api/auth/validate
[2025-08-01 02:02:25][INFO] Registering route: GET /api/users
[2025-08-01 02:02:25][INFO] Registering route: POST /api/users
[2025-08-01 02:02:25][INFO] Registering route: GET /api/users/profile
[2025-08-01 02:02:25][INFO] Registering route: GET /api/users/:id
[2025-08-01 02:02:25][INFO] Registering route: PUT /api/users/:id
[2025-08-01 02:02:25][INFO] Registering route: DELETE /api/users/:id
[2025-08-01 02:02:25][INFO] Registering route: GET /api/stats
[2025-08-01 02:02:25][INFO] Registering route: GET /api/stats/:type
[2025-08-01 02:02:25][INFO] Dispatching request: POST /api/users/login
[2025-08-01 02:02:25][INFO] 数据库连接成功: 127.0.0.1:3306/plb_kj
[2025-08-01 02:44:06][INFO] Request: POST /api/admin/login
[2025-08-01 02:44:06][INFO] Registering route: POST /api/admin/login
[2025-08-01 02:44:06][INFO] Registering route: POST /api/users/login
[2025-08-01 02:44:06][INFO] Registering route: GET /api/auth/validate
[2025-08-01 02:44:06][INFO] Registering route: GET /api/users
[2025-08-01 02:44:06][INFO] Registering route: POST /api/users
[2025-08-01 02:44:06][INFO] Registering route: GET /api/users/profile
[2025-08-01 02:44:06][INFO] Registering route: GET /api/users/:id
[2025-08-01 02:44:06][INFO] Registering route: PUT /api/users/:id
[2025-08-01 02:44:06][INFO] Registering route: DELETE /api/users/:id
[2025-08-01 02:44:06][INFO] Registering route: GET /api/stats
[2025-08-01 02:44:06][INFO] Registering route: GET /api/stats/:type
[2025-08-01 02:44:06][INFO] Dispatching request: POST /api/admin/login
[2025-08-01 02:44:06][INFO] 数据库连接成功: 127.0.0.1:3306/plb_kj
[2025-08-01 02:44:06][INFO] Admin login attempt for username: admin
[2025-08-01 02:44:06][INFO] Admin user logged in successfully: admin
[2025-08-01 02:44:28][INFO] Request: POST /api/users/login
[2025-08-01 02:44:28][INFO] Registering route: POST /api/admin/login
[2025-08-01 02:44:28][INFO] Registering route: POST /api/users/login
[2025-08-01 02:44:28][INFO] Registering route: GET /api/auth/validate
[2025-08-01 02:44:28][INFO] Registering route: GET /api/users
[2025-08-01 02:44:28][INFO] Registering route: POST /api/users
[2025-08-01 02:44:28][INFO] Registering route: GET /api/users/profile
[2025-08-01 02:44:28][INFO] Registering route: GET /api/users/:id
[2025-08-01 02:44:28][INFO] Registering route: PUT /api/users/:id
[2025-08-01 02:44:28][INFO] Registering route: DELETE /api/users/:id
[2025-08-01 02:44:28][INFO] Registering route: GET /api/stats
[2025-08-01 02:44:28][INFO] Registering route: GET /api/stats/:type
[2025-08-01 02:44:28][INFO] Dispatching request: POST /api/users/login
[2025-08-01 02:44:28][INFO] 数据库连接成功: 127.0.0.1:3306/plb_kj
[2025-08-01 02:46:47][INFO] Request: GET /api/auth/validate
[2025-08-01 02:46:47][INFO] Registering route: POST /api/admin/login
[2025-08-01 02:46:47][INFO] Registering route: POST /api/users/login
[2025-08-01 02:46:47][INFO] Registering route: GET /api/auth/validate
[2025-08-01 02:46:47][INFO] Registering route: GET /api/users
[2025-08-01 02:46:47][INFO] Registering route: POST /api/users
[2025-08-01 02:46:47][INFO] Registering route: GET /api/users/profile
[2025-08-01 02:46:47][INFO] Registering route: GET /api/users/:id
[2025-08-01 02:46:47][INFO] Registering route: PUT /api/users/:id
[2025-08-01 02:46:47][INFO] Registering route: DELETE /api/users/:id
[2025-08-01 02:46:47][INFO] Registering route: GET /api/stats
[2025-08-01 02:46:47][INFO] Registering route: GET /api/stats/:type
[2025-08-01 02:46:47][INFO] Dispatching request: GET /api/auth/validate
[2025-08-01 02:46:47][INFO] 数据库连接成功: 127.0.0.1:3306/plb_kj
[2025-08-01 02:46:47][WARNING] Token validation failed: 未提供有效的认证token
[2025-08-01 02:46:55][INFO] Request: POST /api/admin/login
[2025-08-01 02:46:55][INFO] Registering route: POST /api/admin/login
[2025-08-01 02:46:55][INFO] Registering route: POST /api/users/login
[2025-08-01 02:46:55][INFO] Registering route: GET /api/auth/validate
[2025-08-01 02:46:55][INFO] Registering route: GET /api/users
[2025-08-01 02:46:55][INFO] Registering route: POST /api/users
[2025-08-01 02:46:55][INFO] Registering route: GET /api/users/profile
[2025-08-01 02:46:55][INFO] Registering route: GET /api/users/:id
[2025-08-01 02:46:55][INFO] Registering route: PUT /api/users/:id
[2025-08-01 02:46:55][INFO] Registering route: DELETE /api/users/:id
[2025-08-01 02:46:55][INFO] Registering route: GET /api/stats
[2025-08-01 02:46:55][INFO] Registering route: GET /api/stats/:type
[2025-08-01 02:46:55][INFO] Dispatching request: POST /api/admin/login
[2025-08-01 02:46:55][INFO] 数据库连接成功: 127.0.0.1:3306/plb_kj
[2025-08-01 02:46:55][INFO] Admin login attempt for username: admin
[2025-08-01 02:46:55][INFO] Admin user logged in successfully: admin
[2025-08-01 02:47:36][INFO] Request: GET /api/auth/validate
[2025-08-01 02:47:36][INFO] Registering route: POST /api/admin/login
[2025-08-01 02:47:36][INFO] Registering route: POST /api/users/login
[2025-08-01 02:47:36][INFO] Registering route: GET /api/auth/validate
[2025-08-01 02:47:36][INFO] Registering route: GET /api/users
[2025-08-01 02:47:36][INFO] Registering route: POST /api/users
[2025-08-01 02:47:36][INFO] Registering route: GET /api/users/profile
[2025-08-01 02:47:36][INFO] Registering route: GET /api/users/:id
[2025-08-01 02:47:36][INFO] Registering route: PUT /api/users/:id
[2025-08-01 02:47:36][INFO] Registering route: DELETE /api/users/:id
[2025-08-01 02:47:36][INFO] Registering route: GET /api/stats
[2025-08-01 02:47:36][INFO] Registering route: GET /api/stats/:type
[2025-08-01 02:47:36][INFO] Dispatching request: GET /api/auth/validate
[2025-08-01 02:47:36][INFO] 数据库连接成功: 127.0.0.1:3306/plb_kj
[2025-08-01 02:47:36][WARNING] Token validation failed: 未提供有效的认证token
[2025-08-01 03:13:50][INFO] Request: POST /api/admin/login
[2025-08-01 03:13:50][INFO] Registering route: POST /api/admin/login
[2025-08-01 03:13:50][INFO] Registering route: POST /api/users/login
[2025-08-01 03:13:50][INFO] Registering route: GET /api/auth/validate
[2025-08-01 03:13:50][INFO] Registering route: GET /api/users
[2025-08-01 03:13:50][INFO] Registering route: POST /api/users
[2025-08-01 03:13:50][INFO] Registering route: GET /api/users/profile
[2025-08-01 03:13:50][INFO] Registering route: GET /api/users/:id
[2025-08-01 03:13:50][INFO] Registering route: PUT /api/users/:id
[2025-08-01 03:13:50][INFO] Registering route: DELETE /api/users/:id
[2025-08-01 03:13:50][INFO] Registering route: GET /api/stats
[2025-08-01 03:13:50][INFO] Registering route: GET /api/stats/:type
[2025-08-01 03:13:50][INFO] Dispatching request: POST /api/admin/login
[2025-08-01 03:13:50][INFO] 数据库连接成功: 127.0.0.1:3306/plb_kj
[2025-08-01 03:13:50][INFO] Admin login attempt for username: admin
[2025-08-01 03:13:50][INFO] Admin user logged in successfully: admin
[2025-08-01 03:19:02][INFO] Request: GET /?ide_webview_request_time=1753989542502
[2025-08-01 03:19:02][INFO] Registering route: POST /api/admin/login
[2025-08-01 03:19:02][INFO] Registering route: POST /api/users/login
[2025-08-01 03:19:02][INFO] Registering route: GET /api/auth/validate
[2025-08-01 03:19:02][INFO] Registering route: GET /api/users
[2025-08-01 03:19:02][INFO] Registering route: POST /api/users
[2025-08-01 03:19:02][INFO] Registering route: GET /api/users/profile
[2025-08-01 03:19:02][INFO] Registering route: GET /api/users/:id
[2025-08-01 03:19:02][INFO] Registering route: PUT /api/users/:id
[2025-08-01 03:19:02][INFO] Registering route: DELETE /api/users/:id
[2025-08-01 03:19:02][INFO] Registering route: GET /api/stats
[2025-08-01 03:19:02][INFO] Registering route: GET /api/stats/:type
[2025-08-01 03:19:02][INFO] Dispatching request: GET /
[2025-08-01 03:19:02][WARNING] Route not found: GET /
[2025-08-01 03:19:02][INFO] Request: GET /@vite/client
[2025-08-01 03:19:02][INFO] Registering route: POST /api/admin/login
[2025-08-01 03:19:02][INFO] Registering route: POST /api/users/login
[2025-08-01 03:19:02][INFO] Registering route: GET /api/auth/validate
[2025-08-01 03:19:02][INFO] Registering route: GET /api/users
[2025-08-01 03:19:02][INFO] Registering route: POST /api/users
[2025-08-01 03:19:02][INFO] Registering route: GET /api/users/profile
[2025-08-01 03:19:02][INFO] Registering route: GET /api/users/:id
[2025-08-01 03:19:02][INFO] Registering route: PUT /api/users/:id
[2025-08-01 03:19:02][INFO] Registering route: DELETE /api/users/:id
[2025-08-01 03:19:02][INFO] Registering route: GET /api/stats
[2025-08-01 03:19:02][INFO] Registering route: GET /api/stats/:type
[2025-08-01 03:19:02][INFO] Dispatching request: GET /@vite/client
[2025-08-01 03:19:02][WARNING] Route not found: GET /@vite/client
[2025-08-01 03:21:11][INFO] Request: GET /?ide_webview_request_time=1753989542502
[2025-08-01 03:21:11][INFO] Registering route: POST /api/admin/login
[2025-08-01 03:21:11][INFO] Registering route: POST /api/users/login
[2025-08-01 03:21:11][INFO] Registering route: GET /api/auth/validate
[2025-08-01 03:21:11][INFO] Registering route: GET /api/users
[2025-08-01 03:21:11][INFO] Registering route: POST /api/users
[2025-08-01 03:21:11][INFO] Registering route: GET /api/users/profile
[2025-08-01 03:21:11][INFO] Registering route: GET /api/users/:id
[2025-08-01 03:21:11][INFO] Registering route: PUT /api/users/:id
[2025-08-01 03:21:11][INFO] Registering route: DELETE /api/users/:id
[2025-08-01 03:21:11][INFO] Registering route: GET /api/stats
[2025-08-01 03:21:11][INFO] Registering route: GET /api/stats/:type
[2025-08-01 03:21:11][INFO] Dispatching request: GET /
[2025-08-01 03:21:11][WARNING] Route not found: GET /
[2025-08-01 03:21:11][INFO] Request: GET /@vite/client
[2025-08-01 03:21:11][INFO] Registering route: POST /api/admin/login
[2025-08-01 03:21:11][INFO] Registering route: POST /api/users/login
[2025-08-01 03:21:11][INFO] Registering route: GET /api/auth/validate
[2025-08-01 03:21:11][INFO] Registering route: GET /api/users
[2025-08-01 03:21:11][INFO] Registering route: POST /api/users
[2025-08-01 03:21:11][INFO] Registering route: GET /api/users/profile
[2025-08-01 03:21:11][INFO] Registering route: GET /api/users/:id
[2025-08-01 03:21:11][INFO] Registering route: PUT /api/users/:id
[2025-08-01 03:21:11][INFO] Registering route: DELETE /api/users/:id
[2025-08-01 03:21:11][INFO] Registering route: GET /api/stats
[2025-08-01 03:21:11][INFO] Registering route: GET /api/stats/:type
[2025-08-01 03:21:11][INFO] Dispatching request: GET /@vite/client
[2025-08-01 03:21:11][WARNING] Route not found: GET /@vite/client
[2025-08-01 03:21:13][INFO] Request: GET /?ide_webview_request_time=1753989542502
[2025-08-01 03:21:13][INFO] Registering route: POST /api/admin/login
[2025-08-01 03:21:13][INFO] Registering route: POST /api/users/login
[2025-08-01 03:21:13][INFO] Registering route: GET /api/auth/validate
[2025-08-01 03:21:13][INFO] Registering route: GET /api/users
[2025-08-01 03:21:13][INFO] Registering route: POST /api/users
[2025-08-01 03:21:13][INFO] Registering route: GET /api/users/profile
[2025-08-01 03:21:13][INFO] Registering route: GET /api/users/:id
[2025-08-01 03:21:13][INFO] Registering route: PUT /api/users/:id
[2025-08-01 03:21:13][INFO] Registering route: DELETE /api/users/:id
[2025-08-01 03:21:13][INFO] Registering route: GET /api/stats
[2025-08-01 03:21:13][INFO] Registering route: GET /api/stats/:type
[2025-08-01 03:21:13][INFO] Dispatching request: GET /
[2025-08-01 03:21:13][WARNING] Route not found: GET /
[2025-08-01 03:21:13][INFO] Request: GET /@vite/client
[2025-08-01 03:21:13][INFO] Registering route: POST /api/admin/login
[2025-08-01 03:21:13][INFO] Registering route: POST /api/users/login
[2025-08-01 03:21:13][INFO] Registering route: GET /api/auth/validate
[2025-08-01 03:21:13][INFO] Registering route: GET /api/users
[2025-08-01 03:21:13][INFO] Registering route: POST /api/users
[2025-08-01 03:21:13][INFO] Registering route: GET /api/users/profile
[2025-08-01 03:21:13][INFO] Registering route: GET /api/users/:id
[2025-08-01 03:21:13][INFO] Registering route: PUT /api/users/:id
[2025-08-01 03:21:13][INFO] Registering route: DELETE /api/users/:id
[2025-08-01 03:21:13][INFO] Registering route: GET /api/stats
[2025-08-01 03:21:13][INFO] Registering route: GET /api/stats/:type
[2025-08-01 03:21:13][INFO] Dispatching request: GET /@vite/client
[2025-08-01 03:21:13][WARNING] Route not found: GET /@vite/client
[2025-08-01 03:21:15][INFO] Request: GET /?ide_webview_request_time=1753989542502
[2025-08-01 03:21:15][INFO] Registering route: POST /api/admin/login
[2025-08-01 03:21:15][INFO] Registering route: POST /api/users/login
[2025-08-01 03:21:15][INFO] Registering route: GET /api/auth/validate
[2025-08-01 03:21:15][INFO] Registering route: GET /api/users
[2025-08-01 03:21:15][INFO] Registering route: POST /api/users
[2025-08-01 03:21:15][INFO] Registering route: GET /api/users/profile
[2025-08-01 03:21:15][INFO] Registering route: GET /api/users/:id
[2025-08-01 03:21:15][INFO] Registering route: PUT /api/users/:id
[2025-08-01 03:21:15][INFO] Registering route: DELETE /api/users/:id
[2025-08-01 03:21:15][INFO] Registering route: GET /api/stats
[2025-08-01 03:21:15][INFO] Registering route: GET /api/stats/:type
[2025-08-01 03:21:15][INFO] Dispatching request: GET /
[2025-08-01 03:21:15][WARNING] Route not found: GET /
[2025-08-01 03:21:15][INFO] Request: GET /@vite/client
[2025-08-01 03:21:15][INFO] Registering route: POST /api/admin/login
[2025-08-01 03:21:15][INFO] Registering route: POST /api/users/login
[2025-08-01 03:21:15][INFO] Registering route: GET /api/auth/validate
[2025-08-01 03:21:15][INFO] Registering route: GET /api/users
[2025-08-01 03:21:15][INFO] Registering route: POST /api/users
[2025-08-01 03:21:15][INFO] Registering route: GET /api/users/profile
[2025-08-01 03:21:15][INFO] Registering route: GET /api/users/:id
[2025-08-01 03:21:15][INFO] Registering route: PUT /api/users/:id
[2025-08-01 03:21:15][INFO] Registering route: DELETE /api/users/:id
[2025-08-01 03:21:15][INFO] Registering route: GET /api/stats
[2025-08-01 03:21:15][INFO] Registering route: GET /api/stats/:type
[2025-08-01 03:21:15][INFO] Dispatching request: GET /@vite/client
[2025-08-01 03:21:15][WARNING] Route not found: GET /@vite/client
[2025-08-01 09:45:33][INFO] Request: GET /
[2025-08-01 09:45:33][INFO] Registering route: POST /api/admin/login
[2025-08-01 09:45:33][INFO] Registering route: POST /api/users/login
[2025-08-01 09:45:33][INFO] Registering route: GET /api/auth/validate
[2025-08-01 09:45:33][INFO] Registering route: GET /api/users
[2025-08-01 09:45:33][INFO] Registering route: POST /api/users
[2025-08-01 09:45:33][INFO] Registering route: GET /api/users/profile
[2025-08-01 09:45:33][INFO] Registering route: GET /api/users/:id
[2025-08-01 09:45:33][INFO] Registering route: PUT /api/users/:id
[2025-08-01 09:45:33][INFO] Registering route: DELETE /api/users/:id
[2025-08-01 09:45:33][INFO] Registering route: GET /api/stats
[2025-08-01 09:45:33][INFO] Registering route: GET /api/stats/:type
[2025-08-01 09:45:33][INFO] Dispatching request: GET /
[2025-08-01 09:45:33][WARNING] Route not found: GET /
[2025-08-01 09:46:03][INFO] Request: GET /api/auth/validate
[2025-08-01 09:46:03][INFO] Registering route: POST /api/admin/login
[2025-08-01 09:46:03][INFO] Registering route: POST /api/users/login
[2025-08-01 09:46:03][INFO] Registering route: GET /api/auth/validate
[2025-08-01 09:46:03][INFO] Registering route: GET /api/users
[2025-08-01 09:46:03][INFO] Registering route: POST /api/users
[2025-08-01 09:46:03][INFO] Registering route: GET /api/users/profile
[2025-08-01 09:46:03][INFO] Registering route: GET /api/users/:id
[2025-08-01 09:46:03][INFO] Registering route: PUT /api/users/:id
[2025-08-01 09:46:03][INFO] Registering route: DELETE /api/users/:id
[2025-08-01 09:46:03][INFO] Registering route: GET /api/stats
[2025-08-01 09:46:03][INFO] Registering route: GET /api/stats/:type
[2025-08-01 09:46:03][INFO] Dispatching request: GET /api/auth/validate
[2025-08-01 09:46:03][INFO] 数据库连接成功: 127.0.0.1:3306/plb_kj
[2025-08-01 09:46:03][WARNING] Token validation failed: 未提供有效的认证token
[2025-08-01 09:46:12][INFO] Request: POST /api/admin/login
[2025-08-01 09:46:12][INFO] Registering route: POST /api/admin/login
[2025-08-01 09:46:12][INFO] Registering route: POST /api/users/login
[2025-08-01 09:46:12][INFO] Registering route: GET /api/auth/validate
[2025-08-01 09:46:12][INFO] Registering route: GET /api/users
[2025-08-01 09:46:12][INFO] Registering route: POST /api/users
[2025-08-01 09:46:12][INFO] Registering route: GET /api/users/profile
[2025-08-01 09:46:12][INFO] Registering route: GET /api/users/:id
[2025-08-01 09:46:12][INFO] Registering route: PUT /api/users/:id
[2025-08-01 09:46:12][INFO] Registering route: DELETE /api/users/:id
[2025-08-01 09:46:12][INFO] Registering route: GET /api/stats
[2025-08-01 09:46:12][INFO] Registering route: GET /api/stats/:type
[2025-08-01 09:46:12][INFO] Dispatching request: POST /api/admin/login
[2025-08-01 09:46:12][INFO] 数据库连接成功: 127.0.0.1:3306/plb_kj
[2025-08-01 09:46:12][INFO] Admin login attempt for username: admin
[2025-08-01 09:46:12][INFO] Admin user logged in successfully: admin
[2025-08-01 09:46:29][INFO] Request: GET /
[2025-08-01 09:46:29][INFO] Registering route: POST /api/admin/login
[2025-08-01 09:46:29][INFO] Registering route: POST /api/users/login
[2025-08-01 09:46:29][INFO] Registering route: GET /api/auth/validate
[2025-08-01 09:46:29][INFO] Registering route: GET /api/users
[2025-08-01 09:46:29][INFO] Registering route: POST /api/users
[2025-08-01 09:46:29][INFO] Registering route: GET /api/users/profile
[2025-08-01 09:46:29][INFO] Registering route: GET /api/users/:id
[2025-08-01 09:46:29][INFO] Registering route: PUT /api/users/:id
[2025-08-01 09:46:29][INFO] Registering route: DELETE /api/users/:id
[2025-08-01 09:46:29][INFO] Registering route: GET /api/stats
[2025-08-01 09:46:29][INFO] Registering route: GET /api/stats/:type
[2025-08-01 09:46:29][INFO] Dispatching request: GET /
[2025-08-01 09:46:29][WARNING] Route not found: GET /
[2025-08-01 11:07:53][INFO] Request: GET /api/auth/validate
[2025-08-01 11:07:53][INFO] Registering route: POST /api/admin/login
[2025-08-01 11:07:53][INFO] Registering route: POST /api/users/login
[2025-08-01 11:07:53][INFO] Registering route: GET /api/auth/validate
[2025-08-01 11:07:53][INFO] Registering route: GET /api/users
[2025-08-01 11:07:53][INFO] Registering route: POST /api/users
[2025-08-01 11:07:53][INFO] Registering route: GET /api/users/profile
[2025-08-01 11:07:53][INFO] Registering route: GET /api/users/:id
[2025-08-01 11:07:53][INFO] Registering route: PUT /api/users/:id
[2025-08-01 11:07:53][INFO] Registering route: DELETE /api/users/:id
[2025-08-01 11:07:53][INFO] Registering route: GET /api/stats
[2025-08-01 11:07:53][INFO] Registering route: GET /api/stats/:type
[2025-08-01 11:07:53][INFO] Dispatching request: GET /api/auth/validate
[2025-08-01 11:07:53][INFO] 数据库连接成功: 127.0.0.1:3306/plb_kj
[2025-08-01 11:07:53][WARNING] Token validation failed: 未提供有效的认证token
[2025-08-01 11:08:12][INFO] Request: POST /api/admin/login
[2025-08-01 11:08:13][INFO] Registering route: POST /api/admin/login
[2025-08-01 11:08:13][INFO] Registering route: POST /api/users/login
[2025-08-01 11:08:13][INFO] Registering route: GET /api/auth/validate
[2025-08-01 11:08:13][INFO] Registering route: GET /api/users
[2025-08-01 11:08:13][INFO] Registering route: POST /api/users
[2025-08-01 11:08:13][INFO] Registering route: GET /api/users/profile
[2025-08-01 11:08:13][INFO] Registering route: GET /api/users/:id
[2025-08-01 11:08:13][INFO] Registering route: PUT /api/users/:id
[2025-08-01 11:08:13][INFO] Registering route: DELETE /api/users/:id
[2025-08-01 11:08:13][INFO] Registering route: GET /api/stats
[2025-08-01 11:08:13][INFO] Registering route: GET /api/stats/:type
[2025-08-01 11:08:13][INFO] Dispatching request: POST /api/admin/login
[2025-08-01 11:08:13][INFO] 数据库连接成功: 127.0.0.1:3306/plb_kj
[2025-08-01 11:08:13][INFO] Admin login attempt for username: admin
[2025-08-01 11:08:13][INFO] Admin user logged in successfully: admin
[2025-08-01 11:15:41][INFO] Request: GET /api/auth/captcha
[2025-08-01 11:15:41][INFO] Registering route: POST /api/admin/login
[2025-08-01 11:15:41][INFO] Registering route: POST /api/users/login
[2025-08-01 11:15:41][INFO] Registering route: GET /api/auth/captcha
[2025-08-01 11:15:41][INFO] Registering route: GET /api/auth/validate
[2025-08-01 11:15:41][INFO] Registering route: GET /api/users
[2025-08-01 11:15:41][INFO] Registering route: POST /api/users
[2025-08-01 11:15:41][INFO] Registering route: GET /api/users/profile
[2025-08-01 11:15:41][INFO] Registering route: GET /api/users/:id
[2025-08-01 11:15:41][INFO] Registering route: PUT /api/users/:id
[2025-08-01 11:15:41][INFO] Registering route: DELETE /api/users/:id
[2025-08-01 11:15:41][INFO] Registering route: GET /api/stats
[2025-08-01 11:15:41][INFO] Registering route: GET /api/stats/:type
[2025-08-01 11:15:41][INFO] Dispatching request: GET /api/auth/captcha
[2025-08-01 11:15:41][INFO] 数据库连接成功: 127.0.0.1:3306/plb_kj
[2025-08-01 11:23:53][INFO] Request: GET /api/auth/validate
[2025-08-01 11:23:53][INFO] Registering route: POST /api/admin/login
[2025-08-01 11:23:53][INFO] Registering route: POST /api/users/login
[2025-08-01 11:23:53][INFO] Registering route: GET /api/auth/captcha
[2025-08-01 11:23:53][INFO] Registering route: GET /api/auth/validate
[2025-08-01 11:23:53][INFO] Registering route: GET /api/users
[2025-08-01 11:23:53][INFO] Registering route: POST /api/users
[2025-08-01 11:23:53][INFO] Registering route: GET /api/users/profile
[2025-08-01 11:23:53][INFO] Registering route: GET /api/users/:id
[2025-08-01 11:23:53][INFO] Registering route: PUT /api/users/:id
[2025-08-01 11:23:53][INFO] Registering route: DELETE /api/users/:id
[2025-08-01 11:23:53][INFO] Registering route: GET /api/stats
[2025-08-01 11:23:53][INFO] Registering route: GET /api/stats/:type
[2025-08-01 11:23:53][INFO] Dispatching request: GET /api/auth/validate
[2025-08-01 11:23:53][INFO] 数据库连接成功: 127.0.0.1:3306/plb_kj
[2025-08-01 11:23:53][ERROR] Token validation error: 无效的认证令牌
[2025-08-01 11:23:57][INFO] Request: GET /api/auth/captcha
[2025-08-01 11:23:57][INFO] Registering route: POST /api/admin/login
[2025-08-01 11:23:57][INFO] Registering route: POST /api/users/login
[2025-08-01 11:23:57][INFO] Registering route: GET /api/auth/captcha
[2025-08-01 11:23:57][INFO] Registering route: GET /api/auth/validate
[2025-08-01 11:23:57][INFO] Registering route: GET /api/users
[2025-08-01 11:23:57][INFO] Registering route: POST /api/users
[2025-08-01 11:23:57][INFO] Registering route: GET /api/users/profile
[2025-08-01 11:23:57][INFO] Registering route: GET /api/users/:id
[2025-08-01 11:23:57][INFO] Registering route: PUT /api/users/:id
[2025-08-01 11:23:57][INFO] Registering route: DELETE /api/users/:id
[2025-08-01 11:23:57][INFO] Registering route: GET /api/stats
[2025-08-01 11:23:57][INFO] Registering route: GET /api/stats/:type
[2025-08-01 11:23:57][INFO] Dispatching request: GET /api/auth/captcha
[2025-08-01 11:23:57][INFO] 数据库连接成功: 127.0.0.1:3306/plb_kj
[2025-08-01 11:24:00][INFO] Request: GET /api/auth/captcha
[2025-08-01 11:24:00][INFO] Registering route: POST /api/admin/login
[2025-08-01 11:24:00][INFO] Registering route: POST /api/users/login
[2025-08-01 11:24:00][INFO] Registering route: GET /api/auth/captcha
[2025-08-01 11:24:00][INFO] Registering route: GET /api/auth/validate
[2025-08-01 11:24:00][INFO] Registering route: GET /api/users
[2025-08-01 11:24:00][INFO] Registering route: POST /api/users
[2025-08-01 11:24:00][INFO] Registering route: GET /api/users/profile
[2025-08-01 11:24:00][INFO] Registering route: GET /api/users/:id
[2025-08-01 11:24:00][INFO] Registering route: PUT /api/users/:id
[2025-08-01 11:24:00][INFO] Registering route: DELETE /api/users/:id
[2025-08-01 11:24:00][INFO] Registering route: GET /api/stats
[2025-08-01 11:24:00][INFO] Registering route: GET /api/stats/:type
[2025-08-01 11:24:00][INFO] Dispatching request: GET /api/auth/captcha
[2025-08-01 11:24:00][INFO] 数据库连接成功: 127.0.0.1:3306/plb_kj
[2025-08-01 11:25:22][INFO] Request: GET /api/auth/captcha
[2025-08-01 11:25:22][INFO] Registering route: POST /api/admin/login
[2025-08-01 11:25:22][INFO] Registering route: POST /api/users/login
[2025-08-01 11:25:22][INFO] Registering route: GET /api/auth/captcha
[2025-08-01 11:25:22][INFO] Registering route: GET /api/auth/validate
[2025-08-01 11:25:22][INFO] Registering route: GET /api/users
[2025-08-01 11:25:22][INFO] Registering route: POST /api/users
[2025-08-01 11:25:22][INFO] Registering route: GET /api/users/profile
[2025-08-01 11:25:22][INFO] Registering route: GET /api/users/:id
[2025-08-01 11:25:22][INFO] Registering route: PUT /api/users/:id
[2025-08-01 11:25:22][INFO] Registering route: DELETE /api/users/:id
[2025-08-01 11:25:22][INFO] Registering route: GET /api/stats
[2025-08-01 11:25:22][INFO] Registering route: GET /api/stats/:type
[2025-08-01 11:25:22][INFO] Dispatching request: GET /api/auth/captcha
[2025-08-01 11:25:22][INFO] 数据库连接成功: 127.0.0.1:3306/plb_kj
[2025-08-01 11:29:52][INFO] Request: GET /api/auth/captcha
[2025-08-01 11:29:52][INFO] Registering route: POST /api/admin/login
[2025-08-01 11:29:52][INFO] Registering route: POST /api/users/login
[2025-08-01 11:29:52][INFO] Registering route: GET /api/auth/captcha
[2025-08-01 11:29:52][INFO] Registering route: GET /api/auth/validate
[2025-08-01 11:29:52][INFO] Registering route: GET /api/users
[2025-08-01 11:29:52][INFO] Registering route: POST /api/users
[2025-08-01 11:29:52][INFO] Registering route: GET /api/users/profile
[2025-08-01 11:29:52][INFO] Registering route: GET /api/users/:id
[2025-08-01 11:29:52][INFO] Registering route: PUT /api/users/:id
[2025-08-01 11:29:52][INFO] Registering route: DELETE /api/users/:id
[2025-08-01 11:29:52][INFO] Registering route: GET /api/stats
[2025-08-01 11:29:52][INFO] Registering route: GET /api/stats/:type
[2025-08-01 11:29:52][INFO] Dispatching request: GET /api/auth/captcha
[2025-08-01 11:29:52][INFO] 数据库连接成功: 127.0.0.1:3306/plb_kj
[2025-08-01 11:30:10][INFO] Request: POST /api/admin/login
[2025-08-01 11:30:10][INFO] Registering route: POST /api/admin/login
[2025-08-01 11:30:10][INFO] Registering route: POST /api/users/login
[2025-08-01 11:30:10][INFO] Registering route: GET /api/auth/captcha
[2025-08-01 11:30:10][INFO] Registering route: GET /api/auth/validate
[2025-08-01 11:30:10][INFO] Registering route: GET /api/users
[2025-08-01 11:30:10][INFO] Registering route: POST /api/users
[2025-08-01 11:30:10][INFO] Registering route: GET /api/users/profile
[2025-08-01 11:30:10][INFO] Registering route: GET /api/users/:id
[2025-08-01 11:30:10][INFO] Registering route: PUT /api/users/:id
[2025-08-01 11:30:10][INFO] Registering route: DELETE /api/users/:id
[2025-08-01 11:30:10][INFO] Registering route: GET /api/stats
[2025-08-01 11:30:10][INFO] Registering route: GET /api/stats/:type
[2025-08-01 11:30:10][INFO] Dispatching request: POST /api/admin/login
[2025-08-01 11:30:10][INFO] 数据库连接成功: 127.0.0.1:3306/plb_kj
[2025-08-01 11:30:10][INFO] Admin login attempt for username: admin
[2025-08-01 11:30:10][WARNING] Invalid captcha attempt for user: admin
[2025-08-01 11:30:10][ERROR] Admin login error: 验证码错误
[2025-08-01 11:30:10][INFO] Request: GET /api/auth/captcha
[2025-08-01 11:30:10][INFO] Registering route: POST /api/admin/login
[2025-08-01 11:30:10][INFO] Registering route: POST /api/users/login
[2025-08-01 11:30:10][INFO] Registering route: GET /api/auth/captcha
[2025-08-01 11:30:10][INFO] Registering route: GET /api/auth/validate
[2025-08-01 11:30:10][INFO] Registering route: GET /api/users
[2025-08-01 11:30:10][INFO] Registering route: POST /api/users
[2025-08-01 11:30:10][INFO] Registering route: GET /api/users/profile
[2025-08-01 11:30:10][INFO] Registering route: GET /api/users/:id
[2025-08-01 11:30:10][INFO] Registering route: PUT /api/users/:id
[2025-08-01 11:30:10][INFO] Registering route: DELETE /api/users/:id
[2025-08-01 11:30:10][INFO] Registering route: GET /api/stats
[2025-08-01 11:30:10][INFO] Registering route: GET /api/stats/:type
[2025-08-01 11:30:10][INFO] Dispatching request: GET /api/auth/captcha
[2025-08-01 11:30:10][INFO] 数据库连接成功: 127.0.0.1:3306/plb_kj
[2025-08-01 11:36:06][INFO] Request: GET /api/auth/captcha
[2025-08-01 11:36:06][INFO] Registering route: POST /api/admin/login
[2025-08-01 11:36:06][INFO] Registering route: POST /api/users/login
[2025-08-01 11:36:06][INFO] Registering route: GET /api/auth/captcha
[2025-08-01 11:36:06][INFO] Registering route: GET /api/auth/validate
[2025-08-01 11:36:06][INFO] Registering route: GET /api/users
[2025-08-01 11:36:06][INFO] Registering route: POST /api/users
[2025-08-01 11:36:06][INFO] Registering route: GET /api/users/profile
[2025-08-01 11:36:06][INFO] Registering route: GET /api/users/:id
[2025-08-01 11:36:06][INFO] Registering route: PUT /api/users/:id
[2025-08-01 11:36:06][INFO] Registering route: DELETE /api/users/:id
[2025-08-01 11:36:06][INFO] Registering route: GET /api/stats
[2025-08-01 11:36:06][INFO] Registering route: GET /api/stats/:type
[2025-08-01 11:36:06][INFO] Dispatching request: GET /api/auth/captcha
[2025-08-01 11:36:06][INFO] 数据库连接成功: 127.0.0.1:3306/plb_kj
[2025-08-01 11:36:22][INFO] Request: POST /api/admin/login
[2025-08-01 11:36:22][INFO] Registering route: POST /api/admin/login
[2025-08-01 11:36:22][INFO] Registering route: POST /api/users/login
[2025-08-01 11:36:22][INFO] Registering route: GET /api/auth/captcha
[2025-08-01 11:36:22][INFO] Registering route: GET /api/auth/validate
[2025-08-01 11:36:22][INFO] Registering route: GET /api/users
[2025-08-01 11:36:22][INFO] Registering route: POST /api/users
[2025-08-01 11:36:22][INFO] Registering route: GET /api/users/profile
[2025-08-01 11:36:22][INFO] Registering route: GET /api/users/:id
[2025-08-01 11:36:23][INFO] Registering route: PUT /api/users/:id
[2025-08-01 11:36:23][INFO] Registering route: DELETE /api/users/:id
[2025-08-01 11:36:23][INFO] Registering route: GET /api/stats
[2025-08-01 11:36:23][INFO] Registering route: GET /api/stats/:type
[2025-08-01 11:36:23][INFO] Dispatching request: POST /api/admin/login
[2025-08-01 11:36:23][INFO] 数据库连接成功: 127.0.0.1:3306/plb_kj
[2025-08-01 11:36:23][INFO] Admin login attempt for username: admin
[2025-08-01 11:36:23][INFO] Admin user logged in successfully: admin
[2025-08-01 11:36:25][INFO] Request: GET /api/auth/captcha
[2025-08-01 11:36:25][INFO] Registering route: POST /api/admin/login
[2025-08-01 11:36:25][INFO] Registering route: POST /api/users/login
[2025-08-01 11:36:25][INFO] Registering route: GET /api/auth/captcha
[2025-08-01 11:36:25][INFO] Registering route: GET /api/auth/validate
[2025-08-01 11:36:25][INFO] Registering route: GET /api/users
[2025-08-01 11:36:25][INFO] Registering route: POST /api/users
[2025-08-01 11:36:25][INFO] Registering route: GET /api/users/profile
[2025-08-01 11:36:25][INFO] Registering route: GET /api/users/:id
[2025-08-01 11:36:25][INFO] Registering route: PUT /api/users/:id
[2025-08-01 11:36:25][INFO] Registering route: DELETE /api/users/:id
[2025-08-01 11:36:25][INFO] Registering route: GET /api/stats
[2025-08-01 11:36:25][INFO] Registering route: GET /api/stats/:type
[2025-08-01 11:36:25][INFO] Dispatching request: GET /api/auth/captcha
[2025-08-01 11:36:26][INFO] 数据库连接成功: 127.0.0.1:3306/plb_kj
[2025-08-01 11:36:39][INFO] Request: POST /api/admin/login
[2025-08-01 11:36:39][INFO] Registering route: POST /api/admin/login
[2025-08-01 11:36:39][INFO] Registering route: POST /api/users/login
[2025-08-01 11:36:39][INFO] Registering route: GET /api/auth/captcha
[2025-08-01 11:36:39][INFO] Registering route: GET /api/auth/validate
[2025-08-01 11:36:39][INFO] Registering route: GET /api/users
[2025-08-01 11:36:39][INFO] Registering route: POST /api/users
[2025-08-01 11:36:39][INFO] Registering route: GET /api/users/profile
[2025-08-01 11:36:39][INFO] Registering route: GET /api/users/:id
[2025-08-01 11:36:39][INFO] Registering route: PUT /api/users/:id
[2025-08-01 11:36:39][INFO] Registering route: DELETE /api/users/:id
[2025-08-01 11:36:39][INFO] Registering route: GET /api/stats
[2025-08-01 11:36:39][INFO] Registering route: GET /api/stats/:type
[2025-08-01 11:36:39][INFO] Dispatching request: POST /api/admin/login
[2025-08-01 11:36:39][INFO] 数据库连接成功: 127.0.0.1:3306/plb_kj
[2025-08-01 11:36:39][INFO] Admin login attempt for username: admin
[2025-08-01 11:36:39][WARNING] Invalid captcha attempt for user: admin
[2025-08-01 11:36:39][ERROR] Admin login error: 验证码错误
[2025-08-01 11:36:39][INFO] Request: GET /api/auth/captcha
[2025-08-01 11:36:39][INFO] Registering route: POST /api/admin/login
[2025-08-01 11:36:39][INFO] Registering route: POST /api/users/login
[2025-08-01 11:36:39][INFO] Registering route: GET /api/auth/captcha
[2025-08-01 11:36:39][INFO] Registering route: GET /api/auth/validate
[2025-08-01 11:36:39][INFO] Registering route: GET /api/users
[2025-08-01 11:36:39][INFO] Registering route: POST /api/users
[2025-08-01 11:36:39][INFO] Registering route: GET /api/users/profile
[2025-08-01 11:36:39][INFO] Registering route: GET /api/users/:id
[2025-08-01 11:36:39][INFO] Registering route: PUT /api/users/:id
[2025-08-01 11:36:39][INFO] Registering route: DELETE /api/users/:id
[2025-08-01 11:36:39][INFO] Registering route: GET /api/stats
[2025-08-01 11:36:39][INFO] Registering route: GET /api/stats/:type
[2025-08-01 11:36:39][INFO] Dispatching request: GET /api/auth/captcha
[2025-08-01 11:36:39][INFO] 数据库连接成功: 127.0.0.1:3306/plb_kj
[2025-08-01 11:36:48][INFO] Request: POST /api/admin/login
[2025-08-01 11:36:48][INFO] Registering route: POST /api/admin/login
[2025-08-01 11:36:48][INFO] Registering route: POST /api/users/login
[2025-08-01 11:36:48][INFO] Registering route: GET /api/auth/captcha
[2025-08-01 11:36:48][INFO] Registering route: GET /api/auth/validate
[2025-08-01 11:36:48][INFO] Registering route: GET /api/users
[2025-08-01 11:36:48][INFO] Registering route: POST /api/users
[2025-08-01 11:36:48][INFO] Registering route: GET /api/users/profile
[2025-08-01 11:36:48][INFO] Registering route: GET /api/users/:id
[2025-08-01 11:36:48][INFO] Registering route: PUT /api/users/:id
[2025-08-01 11:36:48][INFO] Registering route: DELETE /api/users/:id
[2025-08-01 11:36:48][INFO] Registering route: GET /api/stats
[2025-08-01 11:36:48][INFO] Registering route: GET /api/stats/:type
[2025-08-01 11:36:48][INFO] Dispatching request: POST /api/admin/login
[2025-08-01 11:36:48][INFO] 数据库连接成功: 127.0.0.1:3306/plb_kj
[2025-08-01 11:36:48][INFO] Admin login attempt for username: admin
[2025-08-01 11:36:48][INFO] Admin user logged in successfully: admin
[2025-08-01 11:36:54][INFO] Request: GET /api/auth/captcha
[2025-08-01 11:36:54][INFO] Registering route: POST /api/admin/login
[2025-08-01 11:36:54][INFO] Registering route: POST /api/users/login
[2025-08-01 11:36:54][INFO] Registering route: GET /api/auth/captcha
[2025-08-01 11:36:54][INFO] Registering route: GET /api/auth/validate
[2025-08-01 11:36:54][INFO] Registering route: GET /api/users
[2025-08-01 11:36:54][INFO] Registering route: POST /api/users
[2025-08-01 11:36:54][INFO] Registering route: GET /api/users/profile
[2025-08-01 11:36:54][INFO] Registering route: GET /api/users/:id
[2025-08-01 11:36:54][INFO] Registering route: PUT /api/users/:id
[2025-08-01 11:36:54][INFO] Registering route: DELETE /api/users/:id
[2025-08-01 11:36:54][INFO] Registering route: GET /api/stats
[2025-08-01 11:36:54][INFO] Registering route: GET /api/stats/:type
[2025-08-01 11:36:54][INFO] Dispatching request: GET /api/auth/captcha
[2025-08-01 11:36:54][INFO] 数据库连接成功: 127.0.0.1:3306/plb_kj
[2025-08-01 11:40:32][INFO] Request: GET /api/auth/captcha
[2025-08-01 11:40:32][INFO] Registering route: POST /api/admin/login
[2025-08-01 11:40:32][INFO] Registering route: POST /api/users/login
[2025-08-01 11:40:32][INFO] Registering route: GET /api/auth/captcha
[2025-08-01 11:40:32][INFO] Registering route: GET /api/auth/validate
[2025-08-01 11:40:32][INFO] Registering route: GET /api/users
[2025-08-01 11:40:32][INFO] Registering route: POST /api/users
[2025-08-01 11:40:32][INFO] Registering route: GET /api/users/profile
[2025-08-01 11:40:32][INFO] Registering route: GET /api/users/:id
[2025-08-01 11:40:32][INFO] Registering route: PUT /api/users/:id
[2025-08-01 11:40:32][INFO] Registering route: DELETE /api/users/:id
[2025-08-01 11:40:32][INFO] Registering route: GET /api/stats
[2025-08-01 11:40:32][INFO] Registering route: GET /api/stats/:type
[2025-08-01 11:40:32][INFO] Dispatching request: GET /api/auth/captcha
[2025-08-01 11:40:32][INFO] 数据库连接成功: 127.0.0.1:3306/plb_kj
[2025-08-01 11:54:18][INFO] Request: GET /api/auth/validate
[2025-08-01 11:54:18][INFO] Registering route: POST /api/admin/login
[2025-08-01 11:54:18][INFO] Registering route: POST /api/users/login
[2025-08-01 11:54:18][INFO] Registering route: GET /api/auth/captcha
[2025-08-01 11:54:18][INFO] Registering route: GET /api/auth/validate
[2025-08-01 11:54:18][INFO] Registering route: GET /api/users
[2025-08-01 11:54:18][INFO] Registering route: POST /api/users
[2025-08-01 11:54:18][INFO] Registering route: GET /api/users/profile
[2025-08-01 11:54:18][INFO] Registering route: GET /api/users/:id
[2025-08-01 11:54:18][INFO] Registering route: PUT /api/users/:id
[2025-08-01 11:54:18][INFO] Registering route: DELETE /api/users/:id
[2025-08-01 11:54:18][INFO] Registering route: GET /api/stats
[2025-08-01 11:54:18][INFO] Registering route: GET /api/stats/:type
[2025-08-01 11:54:18][INFO] Dispatching request: GET /api/auth/validate
[2025-08-01 11:54:18][INFO] 数据库连接成功: 127.0.0.1:3306/plb_kj
[2025-08-01 11:54:18][ERROR] Token validation error: 无效的认证令牌
[2025-08-01 11:54:18][INFO] Request: GET /api/auth/validate
[2025-08-01 11:54:18][INFO] Registering route: POST /api/admin/login
[2025-08-01 11:54:18][INFO] Registering route: POST /api/users/login
[2025-08-01 11:54:18][INFO] Registering route: GET /api/auth/captcha
[2025-08-01 11:54:18][INFO] Registering route: GET /api/auth/validate
[2025-08-01 11:54:18][INFO] Registering route: GET /api/users
[2025-08-01 11:54:18][INFO] Registering route: POST /api/users
[2025-08-01 11:54:18][INFO] Registering route: GET /api/users/profile
[2025-08-01 11:54:18][INFO] Registering route: GET /api/users/:id
[2025-08-01 11:54:18][INFO] Registering route: PUT /api/users/:id
[2025-08-01 11:54:18][INFO] Registering route: DELETE /api/users/:id
[2025-08-01 11:54:18][INFO] Registering route: GET /api/stats
[2025-08-01 11:54:18][INFO] Registering route: GET /api/stats/:type
[2025-08-01 11:54:18][INFO] Dispatching request: GET /api/auth/validate
[2025-08-01 11:54:18][INFO] 数据库连接成功: 127.0.0.1:3306/plb_kj
[2025-08-01 11:54:18][ERROR] Token validation error: 无效的认证令牌
[2025-08-01 11:54:26][INFO] Request: GET /api/auth/captcha
[2025-08-01 11:54:26][INFO] Registering route: POST /api/admin/login
[2025-08-01 11:54:26][INFO] Registering route: POST /api/users/login
[2025-08-01 11:54:26][INFO] Registering route: GET /api/auth/captcha
[2025-08-01 11:54:26][INFO] Registering route: GET /api/auth/validate
[2025-08-01 11:54:26][INFO] Registering route: GET /api/users
[2025-08-01 11:54:26][INFO] Registering route: POST /api/users
[2025-08-01 11:54:26][INFO] Registering route: GET /api/users/profile
[2025-08-01 11:54:26][INFO] Registering route: GET /api/users/:id
[2025-08-01 11:54:26][INFO] Registering route: PUT /api/users/:id
[2025-08-01 11:54:26][INFO] Registering route: DELETE /api/users/:id
[2025-08-01 11:54:26][INFO] Registering route: GET /api/stats
[2025-08-01 11:54:26][INFO] Registering route: GET /api/stats/:type
[2025-08-01 11:54:26][INFO] Dispatching request: GET /api/auth/captcha
[2025-08-01 11:54:26][INFO] 数据库连接成功: 127.0.0.1:3306/plb_kj
[2025-08-01 12:46:24][INFO] Request: GET /api/auth/captcha
[2025-08-01 12:46:24][INFO] Registering route: POST /api/admin/login
[2025-08-01 12:46:24][INFO] Registering route: POST /api/users/login
[2025-08-01 12:46:24][INFO] Registering route: GET /api/auth/captcha
[2025-08-01 12:46:24][INFO] Registering route: GET /api/auth/validate
[2025-08-01 12:46:24][INFO] Registering route: GET /api/users
[2025-08-01 12:46:24][INFO] Registering route: POST /api/users
[2025-08-01 12:46:24][INFO] Registering route: GET /api/users/profile
[2025-08-01 12:46:24][INFO] Registering route: GET /api/users/:id
[2025-08-01 12:46:24][INFO] Registering route: PUT /api/users/:id
[2025-08-01 12:46:24][INFO] Registering route: DELETE /api/users/:id
[2025-08-01 12:46:24][INFO] Registering route: GET /api/stats
[2025-08-01 12:46:24][INFO] Registering route: GET /api/stats/:type
[2025-08-01 12:46:24][INFO] Dispatching request: GET /api/auth/captcha
[2025-08-01 12:46:24][INFO] 数据库连接成功: 127.0.0.1:3306/plb_kj
[2025-08-01 12:46:24][INFO] Request: GET /api/auth/captcha
[2025-08-01 12:46:24][INFO] Registering route: POST /api/admin/login
[2025-08-01 12:46:24][INFO] Registering route: POST /api/users/login
[2025-08-01 12:46:24][INFO] Registering route: GET /api/auth/captcha
[2025-08-01 12:46:24][INFO] Registering route: GET /api/auth/validate
[2025-08-01 12:46:24][INFO] Registering route: GET /api/users
[2025-08-01 12:46:24][INFO] Registering route: POST /api/users
[2025-08-01 12:46:24][INFO] Registering route: GET /api/users/profile
[2025-08-01 12:46:24][INFO] Registering route: GET /api/users/:id
[2025-08-01 12:46:24][INFO] Registering route: PUT /api/users/:id
[2025-08-01 12:46:24][INFO] Registering route: DELETE /api/users/:id
[2025-08-01 12:46:24][INFO] Registering route: GET /api/stats
[2025-08-01 12:46:24][INFO] Registering route: GET /api/stats/:type
[2025-08-01 12:46:24][INFO] Dispatching request: GET /api/auth/captcha
[2025-08-01 12:46:24][INFO] 数据库连接成功: 127.0.0.1:3306/plb_kj
[2025-08-01 12:46:42][INFO] Request: POST /api/admin/login
[2025-08-01 12:46:42][INFO] Registering route: POST /api/admin/login
[2025-08-01 12:46:42][INFO] Registering route: POST /api/users/login
[2025-08-01 12:46:42][INFO] Registering route: GET /api/auth/captcha
[2025-08-01 12:46:42][INFO] Registering route: GET /api/auth/validate
[2025-08-01 12:46:42][INFO] Registering route: GET /api/users
[2025-08-01 12:46:42][INFO] Registering route: POST /api/users
[2025-08-01 12:46:42][INFO] Registering route: GET /api/users/profile
[2025-08-01 12:46:42][INFO] Registering route: GET /api/users/:id
[2025-08-01 12:46:42][INFO] Registering route: PUT /api/users/:id
[2025-08-01 12:46:42][INFO] Registering route: DELETE /api/users/:id
[2025-08-01 12:46:42][INFO] Registering route: GET /api/stats
[2025-08-01 12:46:42][INFO] Registering route: GET /api/stats/:type
[2025-08-01 12:46:42][INFO] Dispatching request: POST /api/admin/login
[2025-08-01 12:46:42][INFO] 数据库连接成功: 127.0.0.1:3306/plb_kj
[2025-08-01 12:46:42][INFO] Admin login attempt for username: admin
[2025-08-01 12:46:42][WARNING] Invalid captcha attempt for user: admin
[2025-08-01 12:46:42][ERROR] Admin login error: 验证码错误
[2025-08-01 12:46:42][INFO] Request: GET /api/auth/captcha
[2025-08-01 12:46:42][INFO] Registering route: POST /api/admin/login
[2025-08-01 12:46:42][INFO] Registering route: POST /api/users/login
[2025-08-01 12:46:42][INFO] Registering route: GET /api/auth/captcha
[2025-08-01 12:46:42][INFO] Registering route: GET /api/auth/validate
[2025-08-01 12:46:42][INFO] Registering route: GET /api/users
[2025-08-01 12:46:42][INFO] Registering route: POST /api/users
[2025-08-01 12:46:42][INFO] Registering route: GET /api/users/profile
[2025-08-01 12:46:42][INFO] Registering route: GET /api/users/:id
[2025-08-01 12:46:42][INFO] Registering route: PUT /api/users/:id
[2025-08-01 12:46:42][INFO] Registering route: DELETE /api/users/:id
[2025-08-01 12:46:42][INFO] Registering route: GET /api/stats
[2025-08-01 12:46:42][INFO] Registering route: GET /api/stats/:type
[2025-08-01 12:46:42][INFO] Dispatching request: GET /api/auth/captcha
[2025-08-01 12:46:42][INFO] 数据库连接成功: 127.0.0.1:3306/plb_kj
[2025-08-01 12:46:57][INFO] Request: GET /api/auth/captcha
[2025-08-01 12:46:57][INFO] Registering route: POST /api/admin/login
[2025-08-01 12:46:57][INFO] Registering route: POST /api/users/login
[2025-08-01 12:46:57][INFO] Registering route: GET /api/auth/captcha
[2025-08-01 12:46:57][INFO] Registering route: GET /api/auth/validate
[2025-08-01 12:46:57][INFO] Registering route: GET /api/users
[2025-08-01 12:46:57][INFO] Registering route: POST /api/users
[2025-08-01 12:46:57][INFO] Registering route: GET /api/users/profile
[2025-08-01 12:46:57][INFO] Registering route: GET /api/users/:id
[2025-08-01 12:46:57][INFO] Registering route: PUT /api/users/:id
[2025-08-01 12:46:57][INFO] Registering route: DELETE /api/users/:id
[2025-08-01 12:46:57][INFO] Registering route: GET /api/stats
[2025-08-01 12:46:57][INFO] Registering route: GET /api/stats/:type
[2025-08-01 12:46:57][INFO] Dispatching request: GET /api/auth/captcha
[2025-08-01 12:46:57][INFO] 数据库连接成功: 127.0.0.1:3306/plb_kj
[2025-08-01 12:47:01][INFO] Request: POST /api/admin/login
[2025-08-01 12:47:01][INFO] Registering route: POST /api/admin/login
[2025-08-01 12:47:01][INFO] Registering route: POST /api/users/login
[2025-08-01 12:47:01][INFO] Registering route: GET /api/auth/captcha
[2025-08-01 12:47:01][INFO] Registering route: GET /api/auth/validate
[2025-08-01 12:47:01][INFO] Registering route: GET /api/users
[2025-08-01 12:47:01][INFO] Registering route: POST /api/users
[2025-08-01 12:47:01][INFO] Registering route: GET /api/users/profile
[2025-08-01 12:47:01][INFO] Registering route: GET /api/users/:id
[2025-08-01 12:47:01][INFO] Registering route: PUT /api/users/:id
[2025-08-01 12:47:01][INFO] Registering route: DELETE /api/users/:id
[2025-08-01 12:47:01][INFO] Registering route: GET /api/stats
[2025-08-01 12:47:01][INFO] Registering route: GET /api/stats/:type
[2025-08-01 12:47:01][INFO] Dispatching request: POST /api/admin/login
[2025-08-01 12:47:01][INFO] 数据库连接成功: 127.0.0.1:3306/plb_kj
[2025-08-01 12:47:01][INFO] Admin login attempt for username: admin
[2025-08-01 12:47:01][WARNING] Invalid captcha attempt for user: admin
[2025-08-01 12:47:01][ERROR] Admin login error: 验证码错误
[2025-08-01 12:47:01][INFO] Request: GET /api/auth/captcha
[2025-08-01 12:47:01][INFO] Registering route: POST /api/admin/login
[2025-08-01 12:47:01][INFO] Registering route: POST /api/users/login
[2025-08-01 12:47:01][INFO] Registering route: GET /api/auth/captcha
[2025-08-01 12:47:01][INFO] Registering route: GET /api/auth/validate
[2025-08-01 12:47:01][INFO] Registering route: GET /api/users
[2025-08-01 12:47:01][INFO] Registering route: POST /api/users
[2025-08-01 12:47:01][INFO] Registering route: GET /api/users/profile
[2025-08-01 12:47:01][INFO] Registering route: GET /api/users/:id
[2025-08-01 12:47:01][INFO] Registering route: PUT /api/users/:id
[2025-08-01 12:47:01][INFO] Registering route: DELETE /api/users/:id
[2025-08-01 12:47:01][INFO] Registering route: GET /api/stats
[2025-08-01 12:47:01][INFO] Registering route: GET /api/stats/:type
[2025-08-01 12:47:01][INFO] Dispatching request: GET /api/auth/captcha
[2025-08-01 12:47:01][INFO] 数据库连接成功: 127.0.0.1:3306/plb_kj
[2025-08-01 12:47:10][INFO] Request: POST /api/admin/login
[2025-08-01 12:47:10][INFO] Registering route: POST /api/admin/login
[2025-08-01 12:47:10][INFO] Registering route: POST /api/users/login
[2025-08-01 12:47:10][INFO] Registering route: GET /api/auth/captcha
[2025-08-01 12:47:10][INFO] Registering route: GET /api/auth/validate
[2025-08-01 12:47:10][INFO] Registering route: GET /api/users
[2025-08-01 12:47:10][INFO] Registering route: POST /api/users
[2025-08-01 12:47:10][INFO] Registering route: GET /api/users/profile
[2025-08-01 12:47:10][INFO] Registering route: GET /api/users/:id
[2025-08-01 12:47:10][INFO] Registering route: PUT /api/users/:id
[2025-08-01 12:47:10][INFO] Registering route: DELETE /api/users/:id
[2025-08-01 12:47:10][INFO] Registering route: GET /api/stats
[2025-08-01 12:47:10][INFO] Registering route: GET /api/stats/:type
[2025-08-01 12:47:10][INFO] Dispatching request: POST /api/admin/login
[2025-08-01 12:47:10][INFO] 数据库连接成功: 127.0.0.1:3306/plb_kj
[2025-08-01 12:47:10][INFO] Admin login attempt for username: admin
[2025-08-01 12:47:11][INFO] Admin user logged in successfully: admin
[2025-08-01 13:00:06][INFO] Request: GET /api/auth/validate
[2025-08-01 13:00:06][INFO] Registering route: POST /api/admin/login
[2025-08-01 13:00:06][INFO] Registering route: POST /api/users/login
[2025-08-01 13:00:06][INFO] Registering route: GET /api/auth/captcha
[2025-08-01 13:00:06][INFO] Registering route: GET /api/auth/validate
[2025-08-01 13:00:06][INFO] Registering route: GET /api/users
[2025-08-01 13:00:06][INFO] Registering route: POST /api/users
[2025-08-01 13:00:06][INFO] Registering route: GET /api/users/profile
[2025-08-01 13:00:06][INFO] Registering route: GET /api/users/:id
[2025-08-01 13:00:06][INFO] Registering route: PUT /api/users/:id
[2025-08-01 13:00:06][INFO] Registering route: DELETE /api/users/:id
[2025-08-01 13:00:06][INFO] Registering route: GET /api/stats
[2025-08-01 13:00:06][INFO] Registering route: GET /api/stats/:type
[2025-08-01 13:00:06][INFO] Dispatching request: GET /api/auth/validate
[2025-08-01 13:00:06][INFO] 数据库连接成功: 127.0.0.1:3306/plb_kj
[2025-08-01 13:00:06][ERROR] Token validation error: 无效的认证令牌
[2025-08-01 13:00:06][INFO] Request: GET /api/auth/captcha
[2025-08-01 13:00:06][INFO] Registering route: POST /api/admin/login
[2025-08-01 13:00:06][INFO] Registering route: POST /api/users/login
[2025-08-01 13:00:06][INFO] Registering route: GET /api/auth/captcha
[2025-08-01 13:00:06][INFO] Registering route: GET /api/auth/validate
[2025-08-01 13:00:06][INFO] Registering route: GET /api/users
[2025-08-01 13:00:06][INFO] Registering route: POST /api/users
[2025-08-01 13:00:06][INFO] Registering route: GET /api/users/profile
[2025-08-01 13:00:06][INFO] Registering route: GET /api/users/:id
[2025-08-01 13:00:06][INFO] Registering route: PUT /api/users/:id
[2025-08-01 13:00:06][INFO] Registering route: DELETE /api/users/:id
[2025-08-01 13:00:06][INFO] Registering route: GET /api/stats
[2025-08-01 13:00:06][INFO] Registering route: GET /api/stats/:type
[2025-08-01 13:00:06][INFO] Dispatching request: GET /api/auth/captcha
[2025-08-01 13:00:06][INFO] 数据库连接成功: 127.0.0.1:3306/plb_kj
[2025-08-01 13:00:06][INFO] Request: GET /api/auth/captcha
[2025-08-01 13:00:06][INFO] Registering route: POST /api/admin/login
[2025-08-01 13:00:06][INFO] Registering route: POST /api/users/login
[2025-08-01 13:00:06][INFO] Registering route: GET /api/auth/captcha
[2025-08-01 13:00:06][INFO] Registering route: GET /api/auth/validate
[2025-08-01 13:00:06][INFO] Registering route: GET /api/users
[2025-08-01 13:00:06][INFO] Registering route: POST /api/users
[2025-08-01 13:00:06][INFO] Registering route: GET /api/users/profile
[2025-08-01 13:00:06][INFO] Registering route: GET /api/users/:id
[2025-08-01 13:00:06][INFO] Registering route: PUT /api/users/:id
[2025-08-01 13:00:06][INFO] Registering route: DELETE /api/users/:id
[2025-08-01 13:00:06][INFO] Registering route: GET /api/stats
[2025-08-01 13:00:06][INFO] Registering route: GET /api/stats/:type
[2025-08-01 13:00:06][INFO] Dispatching request: GET /api/auth/captcha
[2025-08-01 13:00:06][INFO] 数据库连接成功: 127.0.0.1:3306/plb_kj
[2025-08-01 13:00:23][INFO] Request: POST /api/admin/login
[2025-08-01 13:00:23][INFO] Registering route: POST /api/admin/login
[2025-08-01 13:00:23][INFO] Registering route: POST /api/users/login
[2025-08-01 13:00:23][INFO] Registering route: GET /api/auth/captcha
[2025-08-01 13:00:23][INFO] Registering route: GET /api/auth/validate
[2025-08-01 13:00:23][INFO] Registering route: GET /api/users
[2025-08-01 13:00:23][INFO] Registering route: POST /api/users
[2025-08-01 13:00:23][INFO] Registering route: GET /api/users/profile
[2025-08-01 13:00:23][INFO] Registering route: GET /api/users/:id
[2025-08-01 13:00:23][INFO] Registering route: PUT /api/users/:id
[2025-08-01 13:00:23][INFO] Registering route: DELETE /api/users/:id
[2025-08-01 13:00:23][INFO] Registering route: GET /api/stats
[2025-08-01 13:00:23][INFO] Registering route: GET /api/stats/:type
[2025-08-01 13:00:23][INFO] Dispatching request: POST /api/admin/login
[2025-08-01 13:00:23][INFO] 数据库连接成功: 127.0.0.1:3306/plb_kj
[2025-08-01 13:00:23][INFO] Admin login attempt for username: admin
[2025-08-01 13:00:23][WARNING] Invalid captcha attempt for user: admin
[2025-08-01 13:00:23][ERROR] Admin login error: 验证码错误
[2025-08-01 13:00:23][INFO] Request: GET /api/auth/captcha
[2025-08-01 13:00:23][INFO] Registering route: POST /api/admin/login
[2025-08-01 13:00:23][INFO] Registering route: POST /api/users/login
[2025-08-01 13:00:23][INFO] Registering route: GET /api/auth/captcha
[2025-08-01 13:00:23][INFO] Registering route: GET /api/auth/validate
[2025-08-01 13:00:23][INFO] Registering route: GET /api/users
[2025-08-01 13:00:23][INFO] Registering route: POST /api/users
[2025-08-01 13:00:23][INFO] Registering route: GET /api/users/profile
[2025-08-01 13:00:23][INFO] Registering route: GET /api/users/:id
[2025-08-01 13:00:23][INFO] Registering route: PUT /api/users/:id
[2025-08-01 13:00:23][INFO] Registering route: DELETE /api/users/:id
[2025-08-01 13:00:23][INFO] Registering route: GET /api/stats
[2025-08-01 13:00:23][INFO] Registering route: GET /api/stats/:type
[2025-08-01 13:00:23][INFO] Dispatching request: GET /api/auth/captcha
[2025-08-01 13:00:23][INFO] 数据库连接成功: 127.0.0.1:3306/plb_kj
[2025-08-01 13:00:30][INFO] Request: POST /api/admin/login
[2025-08-01 13:00:30][INFO] Registering route: POST /api/admin/login
[2025-08-01 13:00:30][INFO] Registering route: POST /api/users/login
[2025-08-01 13:00:30][INFO] Registering route: GET /api/auth/captcha
[2025-08-01 13:00:30][INFO] Registering route: GET /api/auth/validate
[2025-08-01 13:00:30][INFO] Registering route: GET /api/users
[2025-08-01 13:00:30][INFO] Registering route: POST /api/users
[2025-08-01 13:00:30][INFO] Registering route: GET /api/users/profile
[2025-08-01 13:00:30][INFO] Registering route: GET /api/users/:id
[2025-08-01 13:00:30][INFO] Registering route: PUT /api/users/:id
[2025-08-01 13:00:30][INFO] Registering route: DELETE /api/users/:id
[2025-08-01 13:00:30][INFO] Registering route: GET /api/stats
[2025-08-01 13:00:30][INFO] Registering route: GET /api/stats/:type
[2025-08-01 13:00:30][INFO] Dispatching request: POST /api/admin/login
[2025-08-01 13:00:30][INFO] 数据库连接成功: 127.0.0.1:3306/plb_kj
[2025-08-01 13:00:30][INFO] Admin login attempt for username: admin
[2025-08-01 13:00:31][INFO] Admin user logged in successfully: admin
[2025-08-01 13:06:51][INFO] Request: GET /api/auth/validate
[2025-08-01 13:06:51][INFO] Registering route: POST /api/admin/login
[2025-08-01 13:06:51][INFO] Registering route: POST /api/users/login
[2025-08-01 13:06:51][INFO] Registering route: GET /api/auth/captcha
[2025-08-01 13:06:51][INFO] Registering route: GET /api/auth/validate
[2025-08-01 13:06:51][INFO] Registering route: GET /api/users
[2025-08-01 13:06:51][INFO] Registering route: POST /api/users
[2025-08-01 13:06:51][INFO] Registering route: GET /api/users/profile
[2025-08-01 13:06:51][INFO] Registering route: GET /api/users/:id
[2025-08-01 13:06:51][INFO] Registering route: PUT /api/users/:id
[2025-08-01 13:06:51][INFO] Registering route: DELETE /api/users/:id
[2025-08-01 13:06:51][INFO] Registering route: GET /api/stats
[2025-08-01 13:06:51][INFO] Registering route: GET /api/stats/:type
[2025-08-01 13:06:51][INFO] Dispatching request: GET /api/auth/validate
[2025-08-01 13:06:51][INFO] 数据库连接成功: 127.0.0.1:3306/plb_kj
[2025-08-01 13:06:51][ERROR] Token validation error: 无效的认证令牌
[2025-08-01 13:06:51][INFO] Request: GET /api/auth/captcha
[2025-08-01 13:06:51][INFO] Registering route: POST /api/admin/login
[2025-08-01 13:06:51][INFO] Registering route: POST /api/users/login
[2025-08-01 13:06:51][INFO] Registering route: GET /api/auth/captcha
[2025-08-01 13:06:51][INFO] Registering route: GET /api/auth/validate
[2025-08-01 13:06:51][INFO] Registering route: GET /api/users
[2025-08-01 13:06:51][INFO] Registering route: POST /api/users
[2025-08-01 13:06:51][INFO] Registering route: GET /api/users/profile
[2025-08-01 13:06:51][INFO] Registering route: GET /api/users/:id
[2025-08-01 13:06:51][INFO] Registering route: PUT /api/users/:id
[2025-08-01 13:06:51][INFO] Registering route: DELETE /api/users/:id
[2025-08-01 13:06:51][INFO] Registering route: GET /api/stats
[2025-08-01 13:06:51][INFO] Registering route: GET /api/stats/:type
[2025-08-01 13:06:51][INFO] Dispatching request: GET /api/auth/captcha
[2025-08-01 13:06:51][INFO] 数据库连接成功: 127.0.0.1:3306/plb_kj
[2025-08-01 13:06:51][INFO] Request: GET /api/auth/captcha
[2025-08-01 13:06:51][INFO] Registering route: POST /api/admin/login
[2025-08-01 13:06:51][INFO] Registering route: POST /api/users/login
[2025-08-01 13:06:51][INFO] Registering route: GET /api/auth/captcha
[2025-08-01 13:06:51][INFO] Registering route: GET /api/auth/validate
[2025-08-01 13:06:51][INFO] Registering route: GET /api/users
[2025-08-01 13:06:51][INFO] Registering route: POST /api/users
[2025-08-01 13:06:51][INFO] Registering route: GET /api/users/profile
[2025-08-01 13:06:51][INFO] Registering route: GET /api/users/:id
[2025-08-01 13:06:51][INFO] Registering route: PUT /api/users/:id
[2025-08-01 13:06:51][INFO] Registering route: DELETE /api/users/:id
[2025-08-01 13:06:51][INFO] Registering route: GET /api/stats
[2025-08-01 13:06:51][INFO] Registering route: GET /api/stats/:type
[2025-08-01 13:06:51][INFO] Dispatching request: GET /api/auth/captcha
[2025-08-01 13:06:51][INFO] 数据库连接成功: 127.0.0.1:3306/plb_kj
[2025-08-01 13:07:10][INFO] Request: POST /api/admin/login
[2025-08-01 13:07:10][INFO] Registering route: POST /api/admin/login
[2025-08-01 13:07:10][INFO] Registering route: POST /api/users/login
[2025-08-01 13:07:10][INFO] Registering route: GET /api/auth/captcha
[2025-08-01 13:07:10][INFO] Registering route: GET /api/auth/validate
[2025-08-01 13:07:10][INFO] Registering route: GET /api/users
[2025-08-01 13:07:10][INFO] Registering route: POST /api/users
[2025-08-01 13:07:10][INFO] Registering route: GET /api/users/profile
[2025-08-01 13:07:10][INFO] Registering route: GET /api/users/:id
[2025-08-01 13:07:10][INFO] Registering route: PUT /api/users/:id
[2025-08-01 13:07:10][INFO] Registering route: DELETE /api/users/:id
[2025-08-01 13:07:10][INFO] Registering route: GET /api/stats
[2025-08-01 13:07:10][INFO] Registering route: GET /api/stats/:type
[2025-08-01 13:07:10][INFO] Dispatching request: POST /api/admin/login
[2025-08-01 13:07:10][INFO] 数据库连接成功: 127.0.0.1:3306/plb_kj
[2025-08-01 13:07:10][INFO] Admin login attempt for username: admin
[2025-08-01 13:07:10][WARNING] Invalid captcha attempt for user: admin
[2025-08-01 13:07:10][ERROR] Admin login error: 验证码错误
[2025-08-01 13:07:10][INFO] Request: GET /api/auth/captcha
[2025-08-01 13:07:10][INFO] Registering route: POST /api/admin/login
[2025-08-01 13:07:10][INFO] Registering route: POST /api/users/login
[2025-08-01 13:07:10][INFO] Registering route: GET /api/auth/captcha
[2025-08-01 13:07:10][INFO] Registering route: GET /api/auth/validate
[2025-08-01 13:07:10][INFO] Registering route: GET /api/users
[2025-08-01 13:07:10][INFO] Registering route: POST /api/users
[2025-08-01 13:07:10][INFO] Registering route: GET /api/users/profile
[2025-08-01 13:07:10][INFO] Registering route: GET /api/users/:id
[2025-08-01 13:07:10][INFO] Registering route: PUT /api/users/:id
[2025-08-01 13:07:10][INFO] Registering route: DELETE /api/users/:id
[2025-08-01 13:07:10][INFO] Registering route: GET /api/stats
[2025-08-01 13:07:10][INFO] Registering route: GET /api/stats/:type
[2025-08-01 13:07:10][INFO] Dispatching request: GET /api/auth/captcha
[2025-08-01 13:07:10][INFO] 数据库连接成功: 127.0.0.1:3306/plb_kj
[2025-08-01 13:07:16][INFO] Request: POST /api/admin/login
[2025-08-01 13:07:16][INFO] Registering route: POST /api/admin/login
[2025-08-01 13:07:16][INFO] Registering route: POST /api/users/login
[2025-08-01 13:07:16][INFO] Registering route: GET /api/auth/captcha
[2025-08-01 13:07:16][INFO] Registering route: GET /api/auth/validate
[2025-08-01 13:07:16][INFO] Registering route: GET /api/users
[2025-08-01 13:07:16][INFO] Registering route: POST /api/users
[2025-08-01 13:07:16][INFO] Registering route: GET /api/users/profile
[2025-08-01 13:07:16][INFO] Registering route: GET /api/users/:id
[2025-08-01 13:07:16][INFO] Registering route: PUT /api/users/:id
[2025-08-01 13:07:16][INFO] Registering route: DELETE /api/users/:id
[2025-08-01 13:07:16][INFO] Registering route: GET /api/stats
[2025-08-01 13:07:16][INFO] Registering route: GET /api/stats/:type
[2025-08-01 13:07:16][INFO] Dispatching request: POST /api/admin/login
[2025-08-01 13:07:16][INFO] 数据库连接成功: 127.0.0.1:3306/plb_kj
[2025-08-01 13:07:16][INFO] Admin login attempt for username: admin
[2025-08-01 13:07:16][WARNING] Invalid captcha attempt for user: admin
[2025-08-01 13:07:16][ERROR] Admin login error: 验证码错误
[2025-08-01 13:07:16][INFO] Request: GET /api/auth/captcha
[2025-08-01 13:07:16][INFO] Registering route: POST /api/admin/login
[2025-08-01 13:07:16][INFO] Registering route: POST /api/users/login
[2025-08-01 13:07:16][INFO] Registering route: GET /api/auth/captcha
[2025-08-01 13:07:16][INFO] Registering route: GET /api/auth/validate
[2025-08-01 13:07:16][INFO] Registering route: GET /api/users
[2025-08-01 13:07:16][INFO] Registering route: POST /api/users
[2025-08-01 13:07:16][INFO] Registering route: GET /api/users/profile
[2025-08-01 13:07:16][INFO] Registering route: GET /api/users/:id
[2025-08-01 13:07:16][INFO] Registering route: PUT /api/users/:id
[2025-08-01 13:07:16][INFO] Registering route: DELETE /api/users/:id
[2025-08-01 13:07:16][INFO] Registering route: GET /api/stats
[2025-08-01 13:07:16][INFO] Registering route: GET /api/stats/:type
[2025-08-01 13:07:16][INFO] Dispatching request: GET /api/auth/captcha
[2025-08-01 13:07:16][INFO] 数据库连接成功: 127.0.0.1:3306/plb_kj
[2025-08-01 13:07:22][INFO] Request: POST /api/admin/login
[2025-08-01 13:07:22][INFO] Registering route: POST /api/admin/login
[2025-08-01 13:07:22][INFO] Registering route: POST /api/users/login
[2025-08-01 13:07:22][INFO] Registering route: GET /api/auth/captcha
[2025-08-01 13:07:22][INFO] Registering route: GET /api/auth/validate
[2025-08-01 13:07:22][INFO] Registering route: GET /api/users
[2025-08-01 13:07:22][INFO] Registering route: POST /api/users
[2025-08-01 13:07:22][INFO] Registering route: GET /api/users/profile
[2025-08-01 13:07:22][INFO] Registering route: GET /api/users/:id
[2025-08-01 13:07:22][INFO] Registering route: PUT /api/users/:id
[2025-08-01 13:07:22][INFO] Registering route: DELETE /api/users/:id
[2025-08-01 13:07:22][INFO] Registering route: GET /api/stats
[2025-08-01 13:07:22][INFO] Registering route: GET /api/stats/:type
[2025-08-01 13:07:22][INFO] Dispatching request: POST /api/admin/login
[2025-08-01 13:07:22][INFO] 数据库连接成功: 127.0.0.1:3306/plb_kj
[2025-08-01 13:07:22][INFO] Admin login attempt for username: admin
[2025-08-01 13:07:22][INFO] Admin user logged in successfully: admin
[2025-08-01 13:09:33][INFO] Request: GET /api/auth/captcha
[2025-08-01 13:09:33][INFO] Registering route: POST /api/admin/login
[2025-08-01 13:09:33][INFO] Registering route: POST /api/users/login
[2025-08-01 13:09:33][INFO] Registering route: GET /api/auth/captcha
[2025-08-01 13:09:33][INFO] Registering route: GET /api/auth/validate
[2025-08-01 13:09:33][INFO] Registering route: GET /api/users
[2025-08-01 13:09:33][INFO] Registering route: POST /api/users
[2025-08-01 13:09:33][INFO] Registering route: GET /api/users/profile
[2025-08-01 13:09:33][INFO] Registering route: GET /api/users/:id
[2025-08-01 13:09:33][INFO] Registering route: PUT /api/users/:id
[2025-08-01 13:09:33][INFO] Registering route: DELETE /api/users/:id
[2025-08-01 13:09:33][INFO] Registering route: GET /api/stats
[2025-08-01 13:09:33][INFO] Registering route: GET /api/stats/:type
[2025-08-01 13:09:33][INFO] Dispatching request: GET /api/auth/captcha
[2025-08-01 13:09:33][INFO] 数据库连接成功: 127.0.0.1:3306/plb_kj
[2025-08-01 13:09:33][INFO] Request: GET /api/auth/captcha
[2025-08-01 13:09:33][INFO] Registering route: POST /api/admin/login
[2025-08-01 13:09:33][INFO] Registering route: POST /api/users/login
[2025-08-01 13:09:33][INFO] Registering route: GET /api/auth/captcha
[2025-08-01 13:09:33][INFO] Registering route: GET /api/auth/validate
[2025-08-01 13:09:33][INFO] Registering route: GET /api/users
[2025-08-01 13:09:33][INFO] Registering route: POST /api/users
[2025-08-01 13:09:33][INFO] Registering route: GET /api/users/profile
[2025-08-01 13:09:33][INFO] Registering route: GET /api/users/:id
[2025-08-01 13:09:33][INFO] Registering route: PUT /api/users/:id
[2025-08-01 13:09:33][INFO] Registering route: DELETE /api/users/:id
[2025-08-01 13:09:33][INFO] Registering route: GET /api/stats
[2025-08-01 13:09:33][INFO] Registering route: GET /api/stats/:type
[2025-08-01 13:09:33][INFO] Dispatching request: GET /api/auth/captcha
[2025-08-01 13:09:33][INFO] 数据库连接成功: 127.0.0.1:3306/plb_kj
[2025-08-01 13:32:24][INFO] Request: POST /api/admin/login
[2025-08-01 13:32:24][INFO] Registering route: POST /api/admin/login
[2025-08-01 13:32:24][INFO] Registering route: POST /api/users/login
[2025-08-01 13:32:24][INFO] Registering route: GET /api/auth/captcha
[2025-08-01 13:32:24][INFO] Registering route: GET /api/auth/validate
[2025-08-01 13:32:24][INFO] Registering route: GET /api/users
[2025-08-01 13:32:24][INFO] Registering route: POST /api/users
[2025-08-01 13:32:24][INFO] Registering route: GET /api/users/profile
[2025-08-01 13:32:24][INFO] Registering route: GET /api/users/:id
[2025-08-01 13:32:24][INFO] Registering route: PUT /api/users/:id
[2025-08-01 13:32:24][INFO] Registering route: DELETE /api/users/:id
[2025-08-01 13:32:24][INFO] Registering route: GET /api/stats
[2025-08-01 13:32:24][INFO] Registering route: GET /api/stats/:type
[2025-08-01 13:32:24][INFO] Dispatching request: POST /api/admin/login
[2025-08-01 13:32:24][INFO] 数据库连接成功: 127.0.0.1:3306/plb_kj
[2025-08-01 13:32:24][INFO] Admin login attempt for username: admin
[2025-08-01 13:32:24][WARNING] Captcha expired for user: admin
[2025-08-01 13:32:24][ERROR] Admin login error: 验证码已过期，请刷新获取新的验证码
[2025-08-01 13:32:24][INFO] Request: GET /api/auth/captcha
[2025-08-01 13:32:24][INFO] Registering route: POST /api/admin/login
[2025-08-01 13:32:24][INFO] Registering route: POST /api/users/login
[2025-08-01 13:32:24][INFO] Registering route: GET /api/auth/captcha
[2025-08-01 13:32:24][INFO] Registering route: GET /api/auth/validate
[2025-08-01 13:32:24][INFO] Registering route: GET /api/users
[2025-08-01 13:32:24][INFO] Registering route: POST /api/users
[2025-08-01 13:32:24][INFO] Registering route: GET /api/users/profile
[2025-08-01 13:32:24][INFO] Registering route: GET /api/users/:id
[2025-08-01 13:32:24][INFO] Registering route: PUT /api/users/:id
[2025-08-01 13:32:24][INFO] Registering route: DELETE /api/users/:id
[2025-08-01 13:32:24][INFO] Registering route: GET /api/stats
[2025-08-01 13:32:24][INFO] Registering route: GET /api/stats/:type
[2025-08-01 13:32:24][INFO] Dispatching request: GET /api/auth/captcha
[2025-08-01 13:32:24][INFO] 数据库连接成功: 127.0.0.1:3306/plb_kj
[2025-08-01 13:32:32][INFO] Request: POST /api/admin/login
[2025-08-01 13:32:32][INFO] Registering route: POST /api/admin/login
[2025-08-01 13:32:32][INFO] Registering route: POST /api/users/login
[2025-08-01 13:32:32][INFO] Registering route: GET /api/auth/captcha
[2025-08-01 13:32:32][INFO] Registering route: GET /api/auth/validate
[2025-08-01 13:32:32][INFO] Registering route: GET /api/users
[2025-08-01 13:32:32][INFO] Registering route: POST /api/users
[2025-08-01 13:32:32][INFO] Registering route: GET /api/users/profile
[2025-08-01 13:32:32][INFO] Registering route: GET /api/users/:id
[2025-08-01 13:32:32][INFO] Registering route: PUT /api/users/:id
[2025-08-01 13:32:32][INFO] Registering route: DELETE /api/users/:id
[2025-08-01 13:32:32][INFO] Registering route: GET /api/stats
[2025-08-01 13:32:32][INFO] Registering route: GET /api/stats/:type
[2025-08-01 13:32:32][INFO] Dispatching request: POST /api/admin/login
[2025-08-01 13:32:32][INFO] 数据库连接成功: 127.0.0.1:3306/plb_kj
[2025-08-01 13:32:32][INFO] Admin login attempt for username: admin
[2025-08-01 13:32:32][INFO] Admin user logged in successfully: admin
[2025-08-01 13:32:41][INFO] Request: GET /api/auth/captcha
[2025-08-01 13:32:41][INFO] Registering route: POST /api/admin/login
[2025-08-01 13:32:41][INFO] Registering route: POST /api/users/login
[2025-08-01 13:32:41][INFO] Registering route: GET /api/auth/captcha
[2025-08-01 13:32:41][INFO] Registering route: GET /api/auth/validate
[2025-08-01 13:32:41][INFO] Registering route: GET /api/users
[2025-08-01 13:32:41][INFO] Registering route: POST /api/users
[2025-08-01 13:32:41][INFO] Registering route: GET /api/users/profile
[2025-08-01 13:32:41][INFO] Registering route: GET /api/users/:id
[2025-08-01 13:32:41][INFO] Registering route: PUT /api/users/:id
[2025-08-01 13:32:41][INFO] Registering route: DELETE /api/users/:id
[2025-08-01 13:32:41][INFO] Registering route: GET /api/stats
[2025-08-01 13:32:41][INFO] Registering route: GET /api/stats/:type
[2025-08-01 13:32:41][INFO] Dispatching request: GET /api/auth/captcha
[2025-08-01 13:32:41][INFO] 数据库连接成功: 127.0.0.1:3306/plb_kj
[2025-08-01 13:32:41][INFO] Request: GET /api/auth/captcha
[2025-08-01 13:32:41][INFO] Registering route: POST /api/admin/login
[2025-08-01 13:32:41][INFO] Registering route: POST /api/users/login
[2025-08-01 13:32:41][INFO] Registering route: GET /api/auth/captcha
[2025-08-01 13:32:41][INFO] Registering route: GET /api/auth/validate
[2025-08-01 13:32:41][INFO] Registering route: GET /api/users
[2025-08-01 13:32:41][INFO] Registering route: POST /api/users
[2025-08-01 13:32:41][INFO] Registering route: GET /api/users/profile
[2025-08-01 13:32:41][INFO] Registering route: GET /api/users/:id
[2025-08-01 13:32:41][INFO] Registering route: PUT /api/users/:id
[2025-08-01 13:32:41][INFO] Registering route: DELETE /api/users/:id
[2025-08-01 13:32:41][INFO] Registering route: GET /api/stats
[2025-08-01 13:32:41][INFO] Registering route: GET /api/stats/:type
[2025-08-01 13:32:41][INFO] Dispatching request: GET /api/auth/captcha
[2025-08-01 13:32:41][INFO] 数据库连接成功: 127.0.0.1:3306/plb_kj
[2025-08-01 13:32:58][INFO] Request: POST /api/users/login
[2025-08-01 13:32:58][INFO] Registering route: POST /api/admin/login
[2025-08-01 13:32:58][INFO] Registering route: POST /api/users/login
[2025-08-01 13:32:58][INFO] Registering route: GET /api/auth/captcha
[2025-08-01 13:32:58][INFO] Registering route: GET /api/auth/validate
[2025-08-01 13:32:58][INFO] Registering route: GET /api/users
[2025-08-01 13:32:58][INFO] Registering route: POST /api/users
[2025-08-01 13:32:58][INFO] Registering route: GET /api/users/profile
[2025-08-01 13:32:58][INFO] Registering route: GET /api/users/:id
[2025-08-01 13:32:58][INFO] Registering route: PUT /api/users/:id
[2025-08-01 13:32:58][INFO] Registering route: DELETE /api/users/:id
[2025-08-01 13:32:58][INFO] Registering route: GET /api/stats
[2025-08-01 13:32:58][INFO] Registering route: GET /api/stats/:type
[2025-08-01 13:32:58][INFO] Dispatching request: POST /api/users/login
[2025-08-01 13:32:58][INFO] 数据库连接成功: 127.0.0.1:3306/plb_kj
[2025-08-01 14:10:59][INFO] Request: GET /api/auth/validate
[2025-08-01 14:10:59][INFO] Registering route: POST /api/admin/login
[2025-08-01 14:10:59][INFO] Registering route: POST /api/users/login
[2025-08-01 14:10:59][INFO] Registering route: GET /api/auth/captcha
[2025-08-01 14:10:59][INFO] Registering route: GET /api/auth/validate
[2025-08-01 14:10:59][INFO] Registering route: GET /api/users
[2025-08-01 14:10:59][INFO] Registering route: POST /api/users
[2025-08-01 14:10:59][INFO] Registering route: GET /api/users/profile
[2025-08-01 14:10:59][INFO] Registering route: GET /api/users/:id
[2025-08-01 14:10:59][INFO] Registering route: PUT /api/users/:id
[2025-08-01 14:10:59][INFO] Registering route: DELETE /api/users/:id
[2025-08-01 14:10:59][INFO] Registering route: GET /api/stats
[2025-08-01 14:10:59][INFO] Registering route: GET /api/stats/:type
[2025-08-01 14:10:59][INFO] Dispatching request: GET /api/auth/validate
[2025-08-01 14:10:59][INFO] 数据库连接成功: 127.0.0.1:3306/plb_kj
[2025-08-01 14:10:59][ERROR] Token validation error: 无效的认证令牌
[2025-08-01 14:10:59][INFO] Request: GET /api/auth/captcha
[2025-08-01 14:10:59][INFO] Registering route: POST /api/admin/login
[2025-08-01 14:10:59][INFO] Registering route: POST /api/users/login
[2025-08-01 14:10:59][INFO] Registering route: GET /api/auth/captcha
[2025-08-01 14:10:59][INFO] Registering route: GET /api/auth/validate
[2025-08-01 14:10:59][INFO] Registering route: GET /api/users
[2025-08-01 14:10:59][INFO] Registering route: POST /api/users
[2025-08-01 14:10:59][INFO] Registering route: GET /api/users/profile
[2025-08-01 14:10:59][INFO] Registering route: GET /api/users/:id
[2025-08-01 14:10:59][INFO] Registering route: PUT /api/users/:id
[2025-08-01 14:10:59][INFO] Registering route: DELETE /api/users/:id
[2025-08-01 14:10:59][INFO] Registering route: GET /api/stats
[2025-08-01 14:10:59][INFO] Registering route: GET /api/stats/:type
[2025-08-01 14:10:59][INFO] Dispatching request: GET /api/auth/captcha
[2025-08-01 14:10:59][INFO] 数据库连接成功: 127.0.0.1:3306/plb_kj
[2025-08-01 14:10:59][INFO] Request: GET /api/auth/captcha
[2025-08-01 14:10:59][INFO] Registering route: POST /api/admin/login
[2025-08-01 14:10:59][INFO] Registering route: POST /api/users/login
[2025-08-01 14:10:59][INFO] Registering route: GET /api/auth/captcha
[2025-08-01 14:10:59][INFO] Registering route: GET /api/auth/validate
[2025-08-01 14:10:59][INFO] Registering route: GET /api/users
[2025-08-01 14:10:59][INFO] Registering route: POST /api/users
[2025-08-01 14:10:59][INFO] Registering route: GET /api/users/profile
[2025-08-01 14:10:59][INFO] Registering route: GET /api/users/:id
[2025-08-01 14:10:59][INFO] Registering route: PUT /api/users/:id
[2025-08-01 14:10:59][INFO] Registering route: DELETE /api/users/:id
[2025-08-01 14:10:59][INFO] Registering route: GET /api/stats
[2025-08-01 14:10:59][INFO] Registering route: GET /api/stats/:type
[2025-08-01 14:10:59][INFO] Dispatching request: GET /api/auth/captcha
[2025-08-01 14:10:59][INFO] 数据库连接成功: 127.0.0.1:3306/plb_kj
[2025-08-01 14:11:19][INFO] Request: POST /api/admin/login
[2025-08-01 14:11:19][INFO] Registering route: POST /api/admin/login
[2025-08-01 14:11:19][INFO] Registering route: POST /api/users/login
[2025-08-01 14:11:19][INFO] Registering route: GET /api/auth/captcha
[2025-08-01 14:11:19][INFO] Registering route: GET /api/auth/validate
[2025-08-01 14:11:19][INFO] Registering route: GET /api/users
[2025-08-01 14:11:19][INFO] Registering route: POST /api/users
[2025-08-01 14:11:19][INFO] Registering route: GET /api/users/profile
[2025-08-01 14:11:19][INFO] Registering route: GET /api/users/:id
[2025-08-01 14:11:19][INFO] Registering route: PUT /api/users/:id
[2025-08-01 14:11:19][INFO] Registering route: DELETE /api/users/:id
[2025-08-01 14:11:19][INFO] Registering route: GET /api/stats
[2025-08-01 14:11:19][INFO] Registering route: GET /api/stats/:type
[2025-08-01 14:11:19][INFO] Dispatching request: POST /api/admin/login
[2025-08-01 14:11:19][INFO] 数据库连接成功: 127.0.0.1:3306/plb_kj
[2025-08-01 14:11:19][INFO] Admin login attempt for username: admin
[2025-08-01 14:11:19][WARNING] Invalid captcha attempt for user: admin
[2025-08-01 14:11:19][ERROR] Admin login error: 验证码错误
[2025-08-01 14:11:19][INFO] Request: GET /api/auth/captcha
[2025-08-01 14:11:19][INFO] Registering route: POST /api/admin/login
[2025-08-01 14:11:19][INFO] Registering route: POST /api/users/login
[2025-08-01 14:11:19][INFO] Registering route: GET /api/auth/captcha
[2025-08-01 14:11:19][INFO] Registering route: GET /api/auth/validate
[2025-08-01 14:11:19][INFO] Registering route: GET /api/users
[2025-08-01 14:11:19][INFO] Registering route: POST /api/users
[2025-08-01 14:11:19][INFO] Registering route: GET /api/users/profile
[2025-08-01 14:11:19][INFO] Registering route: GET /api/users/:id
[2025-08-01 14:11:19][INFO] Registering route: PUT /api/users/:id
[2025-08-01 14:11:19][INFO] Registering route: DELETE /api/users/:id
[2025-08-01 14:11:19][INFO] Registering route: GET /api/stats
[2025-08-01 14:11:19][INFO] Registering route: GET /api/stats/:type
[2025-08-01 14:11:19][INFO] Dispatching request: GET /api/auth/captcha
[2025-08-01 14:11:19][INFO] 数据库连接成功: 127.0.0.1:3306/plb_kj
[2025-08-01 14:11:25][INFO] Request: POST /api/admin/login
[2025-08-01 14:11:25][INFO] Registering route: POST /api/admin/login
[2025-08-01 14:11:25][INFO] Registering route: POST /api/users/login
[2025-08-01 14:11:25][INFO] Registering route: GET /api/auth/captcha
[2025-08-01 14:11:25][INFO] Registering route: GET /api/auth/validate
[2025-08-01 14:11:25][INFO] Registering route: GET /api/users
[2025-08-01 14:11:25][INFO] Registering route: POST /api/users
[2025-08-01 14:11:25][INFO] Registering route: GET /api/users/profile
[2025-08-01 14:11:25][INFO] Registering route: GET /api/users/:id
[2025-08-01 14:11:25][INFO] Registering route: PUT /api/users/:id
[2025-08-01 14:11:25][INFO] Registering route: DELETE /api/users/:id
[2025-08-01 14:11:25][INFO] Registering route: GET /api/stats
[2025-08-01 14:11:25][INFO] Registering route: GET /api/stats/:type
[2025-08-01 14:11:25][INFO] Dispatching request: POST /api/admin/login
[2025-08-01 14:11:25][INFO] 数据库连接成功: 127.0.0.1:3306/plb_kj
[2025-08-01 14:11:25][INFO] Admin login attempt for username: admin
[2025-08-01 14:11:25][INFO] Admin user logged in successfully: admin
[2025-08-01 14:11:57][INFO] Request: GET /api/auth/validate
[2025-08-01 14:11:57][INFO] Registering route: POST /api/admin/login
[2025-08-01 14:11:57][INFO] Registering route: POST /api/users/login
[2025-08-01 14:11:57][INFO] Registering route: GET /api/auth/captcha
[2025-08-01 14:11:57][INFO] Registering route: GET /api/auth/validate
[2025-08-01 14:11:57][INFO] Registering route: GET /api/users
[2025-08-01 14:11:57][INFO] Registering route: POST /api/users
[2025-08-01 14:11:57][INFO] Registering route: GET /api/users/profile
[2025-08-01 14:11:57][INFO] Registering route: GET /api/users/:id
[2025-08-01 14:11:57][INFO] Registering route: PUT /api/users/:id
[2025-08-01 14:11:57][INFO] Registering route: DELETE /api/users/:id
[2025-08-01 14:11:57][INFO] Registering route: GET /api/stats
[2025-08-01 14:11:57][INFO] Registering route: GET /api/stats/:type
[2025-08-01 14:11:57][INFO] Dispatching request: GET /api/auth/validate
[2025-08-01 14:11:57][INFO] 数据库连接成功: 127.0.0.1:3306/plb_kj
[2025-08-01 14:11:57][ERROR] Token validation error: 无效的认证令牌
[2025-08-01 14:11:57][INFO] Request: GET /api/auth/captcha
[2025-08-01 14:11:57][INFO] Registering route: POST /api/admin/login
[2025-08-01 14:11:57][INFO] Registering route: POST /api/users/login
[2025-08-01 14:11:57][INFO] Registering route: GET /api/auth/captcha
[2025-08-01 14:11:57][INFO] Registering route: GET /api/auth/validate
[2025-08-01 14:11:57][INFO] Registering route: GET /api/users
[2025-08-01 14:11:57][INFO] Registering route: POST /api/users
[2025-08-01 14:11:57][INFO] Registering route: GET /api/users/profile
[2025-08-01 14:11:57][INFO] Registering route: GET /api/users/:id
[2025-08-01 14:11:57][INFO] Registering route: PUT /api/users/:id
[2025-08-01 14:11:57][INFO] Registering route: DELETE /api/users/:id
[2025-08-01 14:11:57][INFO] Registering route: GET /api/stats
[2025-08-01 14:11:57][INFO] Registering route: GET /api/stats/:type
[2025-08-01 14:11:57][INFO] Dispatching request: GET /api/auth/captcha
[2025-08-01 14:11:57][INFO] 数据库连接成功: 127.0.0.1:3306/plb_kj
[2025-08-01 14:11:57][INFO] Request: GET /api/auth/captcha
[2025-08-01 14:11:57][INFO] Registering route: POST /api/admin/login
[2025-08-01 14:11:57][INFO] Registering route: POST /api/users/login
[2025-08-01 14:11:57][INFO] Registering route: GET /api/auth/captcha
[2025-08-01 14:11:57][INFO] Registering route: GET /api/auth/validate
[2025-08-01 14:11:57][INFO] Registering route: GET /api/users
[2025-08-01 14:11:57][INFO] Registering route: POST /api/users
[2025-08-01 14:11:57][INFO] Registering route: GET /api/users/profile
[2025-08-01 14:11:57][INFO] Registering route: GET /api/users/:id
[2025-08-01 14:11:57][INFO] Registering route: PUT /api/users/:id
[2025-08-01 14:11:57][INFO] Registering route: DELETE /api/users/:id
[2025-08-01 14:11:57][INFO] Registering route: GET /api/stats
[2025-08-01 14:11:57][INFO] Registering route: GET /api/stats/:type
[2025-08-01 14:11:57][INFO] Dispatching request: GET /api/auth/captcha
[2025-08-01 14:11:57][INFO] 数据库连接成功: 127.0.0.1:3306/plb_kj
[2025-08-01 14:14:17][INFO] Request: GET /
[2025-08-01 14:14:17][INFO] Registering route: POST /api/admin/login
[2025-08-01 14:14:17][INFO] Registering route: POST /api/users/login
[2025-08-01 14:14:17][INFO] Registering route: GET /api/auth/captcha
[2025-08-01 14:14:17][INFO] Registering route: GET /api/auth/validate
[2025-08-01 14:14:17][INFO] Registering route: GET /api/users
[2025-08-01 14:14:17][INFO] Registering route: POST /api/users
[2025-08-01 14:14:17][INFO] Registering route: GET /api/users/profile
[2025-08-01 14:14:17][INFO] Registering route: GET /api/users/:id
[2025-08-01 14:14:17][INFO] Registering route: PUT /api/users/:id
[2025-08-01 14:14:17][INFO] Registering route: DELETE /api/users/:id
[2025-08-01 14:14:17][INFO] Registering route: GET /api/stats
[2025-08-01 14:14:17][INFO] Registering route: GET /api/stats/:type
[2025-08-01 14:14:17][INFO] Dispatching request: GET /
[2025-08-01 14:14:17][WARNING] Route not found: GET /
[2025-08-01 14:17:10][INFO] Request: GET /api/auth/validate
[2025-08-01 14:17:10][INFO] Registering route: POST /api/admin/login
[2025-08-01 14:17:10][INFO] Registering route: POST /api/users/login
[2025-08-01 14:17:10][INFO] Registering route: GET /api/auth/captcha
[2025-08-01 14:17:10][INFO] Registering route: GET /api/auth/validate
[2025-08-01 14:17:10][INFO] Registering route: GET /api/users
[2025-08-01 14:17:10][INFO] Registering route: POST /api/users
[2025-08-01 14:17:10][INFO] Registering route: GET /api/users/profile
[2025-08-01 14:17:10][INFO] Registering route: GET /api/users/:id
[2025-08-01 14:17:10][INFO] Registering route: PUT /api/users/:id
[2025-08-01 14:17:10][INFO] Registering route: DELETE /api/users/:id
[2025-08-01 14:17:10][INFO] Registering route: GET /api/stats
[2025-08-01 14:17:10][INFO] Registering route: GET /api/stats/:type
[2025-08-01 14:17:10][INFO] Dispatching request: GET /api/auth/validate
[2025-08-01 14:17:10][INFO] 数据库连接成功: 127.0.0.1:3306/plb_kj
[2025-08-01 14:17:10][ERROR] Token validation error: 无效的认证令牌
[2025-08-01 14:17:10][INFO] Request: GET /api/auth/captcha
[2025-08-01 14:17:10][INFO] Registering route: POST /api/admin/login
[2025-08-01 14:17:10][INFO] Registering route: POST /api/users/login
[2025-08-01 14:17:10][INFO] Registering route: GET /api/auth/captcha
[2025-08-01 14:17:10][INFO] Registering route: GET /api/auth/validate
[2025-08-01 14:17:10][INFO] Registering route: GET /api/users
[2025-08-01 14:17:10][INFO] Registering route: POST /api/users
[2025-08-01 14:17:10][INFO] Registering route: GET /api/users/profile
[2025-08-01 14:17:10][INFO] Registering route: GET /api/users/:id
[2025-08-01 14:17:10][INFO] Registering route: PUT /api/users/:id
[2025-08-01 14:17:10][INFO] Registering route: DELETE /api/users/:id
[2025-08-01 14:17:10][INFO] Registering route: GET /api/stats
[2025-08-01 14:17:10][INFO] Registering route: GET /api/stats/:type
[2025-08-01 14:17:10][INFO] Dispatching request: GET /api/auth/captcha
[2025-08-01 14:17:10][INFO] 数据库连接成功: 127.0.0.1:3306/plb_kj
[2025-08-01 14:17:10][INFO] Request: GET /api/auth/captcha
[2025-08-01 14:17:10][INFO] Registering route: POST /api/admin/login
[2025-08-01 14:17:10][INFO] Registering route: POST /api/users/login
[2025-08-01 14:17:10][INFO] Registering route: GET /api/auth/captcha
[2025-08-01 14:17:10][INFO] Registering route: GET /api/auth/validate
[2025-08-01 14:17:10][INFO] Registering route: GET /api/users
[2025-08-01 14:17:10][INFO] Registering route: POST /api/users
[2025-08-01 14:17:10][INFO] Registering route: GET /api/users/profile
[2025-08-01 14:17:10][INFO] Registering route: GET /api/users/:id
[2025-08-01 14:17:10][INFO] Registering route: PUT /api/users/:id
[2025-08-01 14:17:10][INFO] Registering route: DELETE /api/users/:id
[2025-08-01 14:17:10][INFO] Registering route: GET /api/stats
[2025-08-01 14:17:10][INFO] Registering route: GET /api/stats/:type
[2025-08-01 14:17:10][INFO] Dispatching request: GET /api/auth/captcha
[2025-08-01 14:17:10][INFO] 数据库连接成功: 127.0.0.1:3306/plb_kj
[2025-08-01 14:17:29][INFO] Request: POST /api/admin/login
[2025-08-01 14:17:29][INFO] Registering route: POST /api/admin/login
[2025-08-01 14:17:29][INFO] Registering route: POST /api/users/login
[2025-08-01 14:17:29][INFO] Registering route: GET /api/auth/captcha
[2025-08-01 14:17:29][INFO] Registering route: GET /api/auth/validate
[2025-08-01 14:17:29][INFO] Registering route: GET /api/users
[2025-08-01 14:17:29][INFO] Registering route: POST /api/users
[2025-08-01 14:17:29][INFO] Registering route: GET /api/users/profile
[2025-08-01 14:17:29][INFO] Registering route: GET /api/users/:id
[2025-08-01 14:17:29][INFO] Registering route: PUT /api/users/:id
[2025-08-01 14:17:29][INFO] Registering route: DELETE /api/users/:id
[2025-08-01 14:17:29][INFO] Registering route: GET /api/stats
[2025-08-01 14:17:29][INFO] Registering route: GET /api/stats/:type
[2025-08-01 14:17:29][INFO] Dispatching request: POST /api/admin/login
[2025-08-01 14:17:29][INFO] 数据库连接成功: 127.0.0.1:3306/plb_kj
[2025-08-01 14:17:29][INFO] Admin login attempt for username: admin
[2025-08-01 14:17:29][WARNING] Invalid captcha attempt for user: admin
[2025-08-01 14:17:29][ERROR] Admin login error: 验证码错误
[2025-08-01 14:17:29][INFO] Request: GET /api/auth/captcha
[2025-08-01 14:17:29][INFO] Registering route: POST /api/admin/login
[2025-08-01 14:17:29][INFO] Registering route: POST /api/users/login
[2025-08-01 14:17:29][INFO] Registering route: GET /api/auth/captcha
[2025-08-01 14:17:29][INFO] Registering route: GET /api/auth/validate
[2025-08-01 14:17:29][INFO] Registering route: GET /api/users
[2025-08-01 14:17:29][INFO] Registering route: POST /api/users
[2025-08-01 14:17:29][INFO] Registering route: GET /api/users/profile
[2025-08-01 14:17:29][INFO] Registering route: GET /api/users/:id
[2025-08-01 14:17:29][INFO] Registering route: PUT /api/users/:id
[2025-08-01 14:17:29][INFO] Registering route: DELETE /api/users/:id
[2025-08-01 14:17:29][INFO] Registering route: GET /api/stats
[2025-08-01 14:17:29][INFO] Registering route: GET /api/stats/:type
[2025-08-01 14:17:29][INFO] Dispatching request: GET /api/auth/captcha
[2025-08-01 14:17:29][INFO] 数据库连接成功: 127.0.0.1:3306/plb_kj
[2025-08-01 14:17:34][INFO] Request: POST /api/admin/login
[2025-08-01 14:17:34][INFO] Registering route: POST /api/admin/login
[2025-08-01 14:17:34][INFO] Registering route: POST /api/users/login
[2025-08-01 14:17:34][INFO] Registering route: GET /api/auth/captcha
[2025-08-01 14:17:34][INFO] Registering route: GET /api/auth/validate
[2025-08-01 14:17:34][INFO] Registering route: GET /api/users
[2025-08-01 14:17:34][INFO] Registering route: POST /api/users
[2025-08-01 14:17:34][INFO] Registering route: GET /api/users/profile
[2025-08-01 14:17:34][INFO] Registering route: GET /api/users/:id
[2025-08-01 14:17:34][INFO] Registering route: PUT /api/users/:id
[2025-08-01 14:17:34][INFO] Registering route: DELETE /api/users/:id
[2025-08-01 14:17:34][INFO] Registering route: GET /api/stats
[2025-08-01 14:17:34][INFO] Registering route: GET /api/stats/:type
[2025-08-01 14:17:34][INFO] Dispatching request: POST /api/admin/login
[2025-08-01 14:17:34][INFO] 数据库连接成功: 127.0.0.1:3306/plb_kj
[2025-08-01 14:17:34][INFO] Admin login attempt for username: admin
[2025-08-01 14:17:34][WARNING] Invalid captcha attempt for user: admin
[2025-08-01 14:17:34][ERROR] Admin login error: 验证码错误
[2025-08-01 14:17:34][INFO] Request: GET /api/auth/captcha
[2025-08-01 14:17:34][INFO] Registering route: POST /api/admin/login
[2025-08-01 14:17:34][INFO] Registering route: POST /api/users/login
[2025-08-01 14:17:34][INFO] Registering route: GET /api/auth/captcha
[2025-08-01 14:17:34][INFO] Registering route: GET /api/auth/validate
[2025-08-01 14:17:34][INFO] Registering route: GET /api/users
[2025-08-01 14:17:34][INFO] Registering route: POST /api/users
[2025-08-01 14:17:34][INFO] Registering route: GET /api/users/profile
[2025-08-01 14:17:34][INFO] Registering route: GET /api/users/:id
[2025-08-01 14:17:34][INFO] Registering route: PUT /api/users/:id
[2025-08-01 14:17:34][INFO] Registering route: DELETE /api/users/:id
[2025-08-01 14:17:34][INFO] Registering route: GET /api/stats
[2025-08-01 14:17:34][INFO] Registering route: GET /api/stats/:type
[2025-08-01 14:17:34][INFO] Dispatching request: GET /api/auth/captcha
[2025-08-01 14:17:34][INFO] 数据库连接成功: 127.0.0.1:3306/plb_kj
[2025-08-01 14:17:41][INFO] Request: POST /api/admin/login
[2025-08-01 14:17:41][INFO] Registering route: POST /api/admin/login
[2025-08-01 14:17:41][INFO] Registering route: POST /api/users/login
[2025-08-01 14:17:41][INFO] Registering route: GET /api/auth/captcha
[2025-08-01 14:17:41][INFO] Registering route: GET /api/auth/validate
[2025-08-01 14:17:41][INFO] Registering route: GET /api/users
[2025-08-01 14:17:41][INFO] Registering route: POST /api/users
[2025-08-01 14:17:41][INFO] Registering route: GET /api/users/profile
[2025-08-01 14:17:41][INFO] Registering route: GET /api/users/:id
[2025-08-01 14:17:41][INFO] Registering route: PUT /api/users/:id
[2025-08-01 14:17:41][INFO] Registering route: DELETE /api/users/:id
[2025-08-01 14:17:41][INFO] Registering route: GET /api/stats
[2025-08-01 14:17:41][INFO] Registering route: GET /api/stats/:type
[2025-08-01 14:17:41][INFO] Dispatching request: POST /api/admin/login
[2025-08-01 14:17:41][INFO] 数据库连接成功: 127.0.0.1:3306/plb_kj
[2025-08-01 14:17:41][INFO] Admin login attempt for username: admin
[2025-08-01 14:17:41][INFO] Admin user logged in successfully: admin
[2025-08-01 14:25:46][INFO] Request: GET /api/auth/validate
[2025-08-01 14:25:46][INFO] Registering route: POST /api/admin/login
[2025-08-01 14:25:46][INFO] Registering route: POST /api/users/login
[2025-08-01 14:25:46][INFO] Registering route: GET /api/auth/captcha
[2025-08-01 14:25:46][INFO] Registering route: GET /api/auth/validate
[2025-08-01 14:25:46][INFO] Registering route: GET /api/users
[2025-08-01 14:25:46][INFO] Registering route: POST /api/users
[2025-08-01 14:25:46][INFO] Registering route: GET /api/users/profile
[2025-08-01 14:25:46][INFO] Registering route: GET /api/users/:id
[2025-08-01 14:25:46][INFO] Registering route: PUT /api/users/:id
[2025-08-01 14:25:46][INFO] Registering route: DELETE /api/users/:id
[2025-08-01 14:25:46][INFO] Registering route: GET /api/stats
[2025-08-01 14:25:46][INFO] Registering route: GET /api/stats/:type
[2025-08-01 14:25:46][INFO] Dispatching request: GET /api/auth/validate
[2025-08-01 14:25:46][INFO] 数据库连接成功: 127.0.0.1:3306/plb_kj
[2025-08-01 14:25:46][ERROR] Token validation error: 无效的认证令牌
[2025-08-01 14:25:46][INFO] Request: GET /api/auth/captcha
[2025-08-01 14:25:46][INFO] Registering route: POST /api/admin/login
[2025-08-01 14:25:46][INFO] Registering route: POST /api/users/login
[2025-08-01 14:25:46][INFO] Registering route: GET /api/auth/captcha
[2025-08-01 14:25:46][INFO] Registering route: GET /api/auth/validate
[2025-08-01 14:25:46][INFO] Registering route: GET /api/users
[2025-08-01 14:25:46][INFO] Registering route: POST /api/users
[2025-08-01 14:25:46][INFO] Registering route: GET /api/users/profile
[2025-08-01 14:25:46][INFO] Registering route: GET /api/users/:id
[2025-08-01 14:25:46][INFO] Registering route: PUT /api/users/:id
[2025-08-01 14:25:46][INFO] Registering route: DELETE /api/users/:id
[2025-08-01 14:25:46][INFO] Registering route: GET /api/stats
[2025-08-01 14:25:46][INFO] Registering route: GET /api/stats/:type
[2025-08-01 14:25:46][INFO] Dispatching request: GET /api/auth/captcha
[2025-08-01 14:25:46][INFO] 数据库连接成功: 127.0.0.1:3306/plb_kj
[2025-08-01 14:25:46][INFO] Request: GET /api/auth/captcha
[2025-08-01 14:25:46][INFO] Registering route: POST /api/admin/login
[2025-08-01 14:25:46][INFO] Registering route: POST /api/users/login
[2025-08-01 14:25:46][INFO] Registering route: GET /api/auth/captcha
[2025-08-01 14:25:46][INFO] Registering route: GET /api/auth/validate
[2025-08-01 14:25:46][INFO] Registering route: GET /api/users
[2025-08-01 14:25:46][INFO] Registering route: POST /api/users
[2025-08-01 14:25:46][INFO] Registering route: GET /api/users/profile
[2025-08-01 14:25:46][INFO] Registering route: GET /api/users/:id
[2025-08-01 14:25:46][INFO] Registering route: PUT /api/users/:id
[2025-08-01 14:25:46][INFO] Registering route: DELETE /api/users/:id
[2025-08-01 14:25:46][INFO] Registering route: GET /api/stats
[2025-08-01 14:25:46][INFO] Registering route: GET /api/stats/:type
[2025-08-01 14:25:46][INFO] Dispatching request: GET /api/auth/captcha
[2025-08-01 14:25:46][INFO] 数据库连接成功: 127.0.0.1:3306/plb_kj
[2025-08-01 14:39:51][INFO] Request: GET /api/auth/validate
[2025-08-01 14:39:51][INFO] Registering route: POST /api/admin/login
[2025-08-01 14:39:51][INFO] Registering route: POST /api/users/login
[2025-08-01 14:39:51][INFO] Registering route: GET /api/auth/captcha
[2025-08-01 14:39:51][INFO] Registering route: GET /api/auth/validate
[2025-08-01 14:39:51][INFO] Registering route: GET /api/users
[2025-08-01 14:39:51][INFO] Registering route: POST /api/users
[2025-08-01 14:39:51][INFO] Registering route: GET /api/users/profile
[2025-08-01 14:39:51][INFO] Registering route: GET /api/users/:id
[2025-08-01 14:39:51][INFO] Registering route: PUT /api/users/:id
[2025-08-01 14:39:51][INFO] Registering route: DELETE /api/users/:id
[2025-08-01 14:39:51][INFO] Registering route: GET /api/stats
[2025-08-01 14:39:51][INFO] Registering route: GET /api/stats/:type
[2025-08-01 14:39:51][INFO] Dispatching request: GET /api/auth/validate
[2025-08-01 14:39:51][INFO] 数据库连接成功: 127.0.0.1:3306/plb_kj
[2025-08-01 14:39:51][ERROR] Token validation error: 无效的认证令牌
[2025-08-01 14:39:51][INFO] Request: GET /api/auth/captcha
[2025-08-01 14:39:51][INFO] Registering route: POST /api/admin/login
[2025-08-01 14:39:51][INFO] Registering route: POST /api/users/login
[2025-08-01 14:39:51][INFO] Registering route: GET /api/auth/captcha
[2025-08-01 14:39:51][INFO] Registering route: GET /api/auth/validate
[2025-08-01 14:39:51][INFO] Registering route: GET /api/users
[2025-08-01 14:39:51][INFO] Registering route: POST /api/users
[2025-08-01 14:39:51][INFO] Registering route: GET /api/users/profile
[2025-08-01 14:39:51][INFO] Registering route: GET /api/users/:id
[2025-08-01 14:39:51][INFO] Registering route: PUT /api/users/:id
[2025-08-01 14:39:51][INFO] Registering route: DELETE /api/users/:id
[2025-08-01 14:39:51][INFO] Registering route: GET /api/stats
[2025-08-01 14:39:51][INFO] Registering route: GET /api/stats/:type
[2025-08-01 14:39:51][INFO] Dispatching request: GET /api/auth/captcha
[2025-08-01 14:39:51][INFO] 数据库连接成功: 127.0.0.1:3306/plb_kj
[2025-08-01 14:39:51][INFO] Request: GET /api/auth/captcha
[2025-08-01 14:39:51][INFO] Registering route: POST /api/admin/login
[2025-08-01 14:39:51][INFO] Registering route: POST /api/users/login
[2025-08-01 14:39:51][INFO] Registering route: GET /api/auth/captcha
[2025-08-01 14:39:51][INFO] Registering route: GET /api/auth/validate
[2025-08-01 14:39:51][INFO] Registering route: GET /api/users
[2025-08-01 14:39:51][INFO] Registering route: POST /api/users
[2025-08-01 14:39:51][INFO] Registering route: GET /api/users/profile
[2025-08-01 14:39:51][INFO] Registering route: GET /api/users/:id
[2025-08-01 14:39:51][INFO] Registering route: PUT /api/users/:id
[2025-08-01 14:39:51][INFO] Registering route: DELETE /api/users/:id
[2025-08-01 14:39:51][INFO] Registering route: GET /api/stats
[2025-08-01 14:39:51][INFO] Registering route: GET /api/stats/:type
[2025-08-01 14:39:51][INFO] Dispatching request: GET /api/auth/captcha
[2025-08-01 14:39:51][INFO] 数据库连接成功: 127.0.0.1:3306/plb_kj
[2025-08-01 15:21:38][INFO] Request: GET /api/auth/validate
[2025-08-01 15:21:38][INFO] Registering route: POST /api/admin/login
[2025-08-01 15:21:38][INFO] Registering route: POST /api/users/login
[2025-08-01 15:21:38][INFO] Registering route: GET /api/auth/captcha
[2025-08-01 15:21:38][INFO] Registering route: GET /api/auth/validate
[2025-08-01 15:21:38][INFO] Registering route: GET /api/users
[2025-08-01 15:21:38][INFO] Registering route: POST /api/users
[2025-08-01 15:21:38][INFO] Registering route: GET /api/users/profile
[2025-08-01 15:21:38][INFO] Registering route: GET /api/users/:id
[2025-08-01 15:21:38][INFO] Registering route: PUT /api/users/:id
[2025-08-01 15:21:38][INFO] Registering route: DELETE /api/users/:id
[2025-08-01 15:21:38][INFO] Registering route: GET /api/stats
[2025-08-01 15:21:38][INFO] Registering route: GET /api/stats/:type
[2025-08-01 15:21:38][INFO] Dispatching request: GET /api/auth/validate
[2025-08-01 15:21:38][INFO] 数据库连接成功: 127.0.0.1:3306/plb_kj
[2025-08-01 15:21:38][ERROR] Token validation error: 无效的认证令牌
[2025-08-01 15:21:38][INFO] Request: GET /api/auth/captcha
[2025-08-01 15:21:38][INFO] Registering route: POST /api/admin/login
[2025-08-01 15:21:38][INFO] Registering route: POST /api/users/login
[2025-08-01 15:21:38][INFO] Registering route: GET /api/auth/captcha
[2025-08-01 15:21:38][INFO] Registering route: GET /api/auth/validate
[2025-08-01 15:21:38][INFO] Registering route: GET /api/users
[2025-08-01 15:21:38][INFO] Registering route: POST /api/users
[2025-08-01 15:21:38][INFO] Registering route: GET /api/users/profile
[2025-08-01 15:21:38][INFO] Registering route: GET /api/users/:id
[2025-08-01 15:21:38][INFO] Registering route: PUT /api/users/:id
[2025-08-01 15:21:38][INFO] Registering route: DELETE /api/users/:id
[2025-08-01 15:21:38][INFO] Registering route: GET /api/stats
[2025-08-01 15:21:38][INFO] Registering route: GET /api/stats/:type
[2025-08-01 15:21:38][INFO] Dispatching request: GET /api/auth/captcha
[2025-08-01 15:21:38][INFO] 数据库连接成功: 127.0.0.1:3306/plb_kj
[2025-08-01 15:21:38][INFO] Request: GET /api/auth/captcha
[2025-08-01 15:21:38][INFO] Registering route: POST /api/admin/login
[2025-08-01 15:21:38][INFO] Registering route: POST /api/users/login
[2025-08-01 15:21:38][INFO] Registering route: GET /api/auth/captcha
[2025-08-01 15:21:38][INFO] Registering route: GET /api/auth/validate
[2025-08-01 15:21:38][INFO] Registering route: GET /api/users
[2025-08-01 15:21:38][INFO] Registering route: POST /api/users
[2025-08-01 15:21:38][INFO] Registering route: GET /api/users/profile
[2025-08-01 15:21:38][INFO] Registering route: GET /api/users/:id
[2025-08-01 15:21:38][INFO] Registering route: PUT /api/users/:id
[2025-08-01 15:21:38][INFO] Registering route: DELETE /api/users/:id
[2025-08-01 15:21:38][INFO] Registering route: GET /api/stats
[2025-08-01 15:21:38][INFO] Registering route: GET /api/stats/:type
[2025-08-01 15:21:38][INFO] Dispatching request: GET /api/auth/captcha
[2025-08-01 15:21:38][INFO] 数据库连接成功: 127.0.0.1:3306/plb_kj
[2025-08-01 15:21:56][INFO] Request: POST /api/admin/login
[2025-08-01 15:21:56][INFO] Registering route: POST /api/admin/login
[2025-08-01 15:21:56][INFO] Registering route: POST /api/users/login
[2025-08-01 15:21:56][INFO] Registering route: GET /api/auth/captcha
[2025-08-01 15:21:56][INFO] Registering route: GET /api/auth/validate
[2025-08-01 15:21:56][INFO] Registering route: GET /api/users
[2025-08-01 15:21:56][INFO] Registering route: POST /api/users
[2025-08-01 15:21:56][INFO] Registering route: GET /api/users/profile
[2025-08-01 15:21:56][INFO] Registering route: GET /api/users/:id
[2025-08-01 15:21:56][INFO] Registering route: PUT /api/users/:id
[2025-08-01 15:21:56][INFO] Registering route: DELETE /api/users/:id
[2025-08-01 15:21:56][INFO] Registering route: GET /api/stats
[2025-08-01 15:21:56][INFO] Registering route: GET /api/stats/:type
[2025-08-01 15:21:56][INFO] Dispatching request: POST /api/admin/login
[2025-08-01 15:21:56][INFO] 数据库连接成功: 127.0.0.1:3306/plb_kj
[2025-08-01 15:21:56][INFO] Admin login attempt for username: admin
[2025-08-01 15:21:56][WARNING] Invalid captcha attempt for user: admin
[2025-08-01 15:21:56][ERROR] Admin login error: 验证码错误
[2025-08-01 15:21:56][INFO] Request: GET /api/auth/captcha
[2025-08-01 15:21:56][INFO] Registering route: POST /api/admin/login
[2025-08-01 15:21:56][INFO] Registering route: POST /api/users/login
[2025-08-01 15:21:56][INFO] Registering route: GET /api/auth/captcha
[2025-08-01 15:21:56][INFO] Registering route: GET /api/auth/validate
[2025-08-01 15:21:56][INFO] Registering route: GET /api/users
[2025-08-01 15:21:56][INFO] Registering route: POST /api/users
[2025-08-01 15:21:56][INFO] Registering route: GET /api/users/profile
[2025-08-01 15:21:56][INFO] Registering route: GET /api/users/:id
[2025-08-01 15:21:56][INFO] Registering route: PUT /api/users/:id
[2025-08-01 15:21:56][INFO] Registering route: DELETE /api/users/:id
[2025-08-01 15:21:56][INFO] Registering route: GET /api/stats
[2025-08-01 15:21:56][INFO] Registering route: GET /api/stats/:type
[2025-08-01 15:21:56][INFO] Dispatching request: GET /api/auth/captcha
[2025-08-01 15:21:56][INFO] 数据库连接成功: 127.0.0.1:3306/plb_kj
[2025-08-01 15:22:06][INFO] Request: POST /api/admin/login
[2025-08-01 15:22:06][INFO] Registering route: POST /api/admin/login
[2025-08-01 15:22:06][INFO] Registering route: POST /api/users/login
[2025-08-01 15:22:06][INFO] Registering route: GET /api/auth/captcha
[2025-08-01 15:22:06][INFO] Registering route: GET /api/auth/validate
[2025-08-01 15:22:06][INFO] Registering route: GET /api/users
[2025-08-01 15:22:06][INFO] Registering route: POST /api/users
[2025-08-01 15:22:06][INFO] Registering route: GET /api/users/profile
[2025-08-01 15:22:06][INFO] Registering route: GET /api/users/:id
[2025-08-01 15:22:06][INFO] Registering route: PUT /api/users/:id
[2025-08-01 15:22:06][INFO] Registering route: DELETE /api/users/:id
[2025-08-01 15:22:06][INFO] Registering route: GET /api/stats
[2025-08-01 15:22:06][INFO] Registering route: GET /api/stats/:type
[2025-08-01 15:22:06][INFO] Dispatching request: POST /api/admin/login
[2025-08-01 15:22:06][INFO] 数据库连接成功: 127.0.0.1:3306/plb_kj
[2025-08-01 15:22:06][INFO] Admin login attempt for username: admin
[2025-08-01 15:22:06][INFO] Admin user logged in successfully: admin
[2025-08-01 15:29:13][INFO] Request: GET /api/auth/validate
[2025-08-01 15:29:13][INFO] Registering route: POST /api/admin/login
[2025-08-01 15:29:13][INFO] Registering route: POST /api/users/login
[2025-08-01 15:29:13][INFO] Registering route: GET /api/auth/captcha
[2025-08-01 15:29:13][INFO] Registering route: GET /api/auth/validate
[2025-08-01 15:29:13][INFO] Registering route: GET /api/users
[2025-08-01 15:29:13][INFO] Registering route: POST /api/users
[2025-08-01 15:29:13][INFO] Registering route: GET /api/users/profile
[2025-08-01 15:29:13][INFO] Registering route: GET /api/users/:id
[2025-08-01 15:29:13][INFO] Registering route: PUT /api/users/:id
[2025-08-01 15:29:13][INFO] Registering route: DELETE /api/users/:id
[2025-08-01 15:29:13][INFO] Registering route: GET /api/stats
[2025-08-01 15:29:13][INFO] Registering route: GET /api/stats/:type
[2025-08-01 15:29:13][INFO] Dispatching request: GET /api/auth/validate
[2025-08-01 15:29:13][INFO] 数据库连接成功: 127.0.0.1:3306/plb_kj
[2025-08-01 15:29:13][ERROR] Token validation error: 无效的认证令牌
[2025-08-01 15:29:13][INFO] Request: GET /api/auth/captcha
[2025-08-01 15:29:13][INFO] Registering route: POST /api/admin/login
[2025-08-01 15:29:13][INFO] Registering route: POST /api/users/login
[2025-08-01 15:29:13][INFO] Registering route: GET /api/auth/captcha
[2025-08-01 15:29:13][INFO] Registering route: GET /api/auth/validate
[2025-08-01 15:29:13][INFO] Registering route: GET /api/users
[2025-08-01 15:29:13][INFO] Registering route: POST /api/users
[2025-08-01 15:29:13][INFO] Registering route: GET /api/users/profile
[2025-08-01 15:29:13][INFO] Registering route: GET /api/users/:id
[2025-08-01 15:29:13][INFO] Registering route: PUT /api/users/:id
[2025-08-01 15:29:13][INFO] Registering route: DELETE /api/users/:id
[2025-08-01 15:29:13][INFO] Registering route: GET /api/stats
[2025-08-01 15:29:13][INFO] Registering route: GET /api/stats/:type
[2025-08-01 15:29:13][INFO] Dispatching request: GET /api/auth/captcha
[2025-08-01 15:29:13][INFO] 数据库连接成功: 127.0.0.1:3306/plb_kj
[2025-08-01 15:29:13][INFO] Request: GET /api/auth/captcha
[2025-08-01 15:29:13][INFO] Registering route: POST /api/admin/login
[2025-08-01 15:29:13][INFO] Registering route: POST /api/users/login
[2025-08-01 15:29:13][INFO] Registering route: GET /api/auth/captcha
[2025-08-01 15:29:13][INFO] Registering route: GET /api/auth/validate
[2025-08-01 15:29:13][INFO] Registering route: GET /api/users
[2025-08-01 15:29:13][INFO] Registering route: POST /api/users
[2025-08-01 15:29:13][INFO] Registering route: GET /api/users/profile
[2025-08-01 15:29:13][INFO] Registering route: GET /api/users/:id
[2025-08-01 15:29:13][INFO] Registering route: PUT /api/users/:id
[2025-08-01 15:29:13][INFO] Registering route: DELETE /api/users/:id
[2025-08-01 15:29:13][INFO] Registering route: GET /api/stats
[2025-08-01 15:29:13][INFO] Registering route: GET /api/stats/:type
[2025-08-01 15:29:13][INFO] Dispatching request: GET /api/auth/captcha
[2025-08-01 15:29:13][INFO] 数据库连接成功: 127.0.0.1:3306/plb_kj
[2025-08-01 15:29:31][INFO] Request: GET /api/auth/validate
[2025-08-01 15:29:31][INFO] Registering route: POST /api/admin/login
[2025-08-01 15:29:31][INFO] Registering route: POST /api/users/login
[2025-08-01 15:29:31][INFO] Registering route: GET /api/auth/captcha
[2025-08-01 15:29:31][INFO] Registering route: GET /api/auth/validate
[2025-08-01 15:29:31][INFO] Registering route: GET /api/users
[2025-08-01 15:29:31][INFO] Registering route: POST /api/users
[2025-08-01 15:29:31][INFO] Registering route: GET /api/users/profile
[2025-08-01 15:29:31][INFO] Registering route: GET /api/users/:id
[2025-08-01 15:29:31][INFO] Registering route: PUT /api/users/:id
[2025-08-01 15:29:31][INFO] Registering route: DELETE /api/users/:id
[2025-08-01 15:29:31][INFO] Registering route: GET /api/stats
[2025-08-01 15:29:31][INFO] Registering route: GET /api/stats/:type
[2025-08-01 15:29:31][INFO] Dispatching request: GET /api/auth/validate
[2025-08-01 15:29:31][INFO] 数据库连接成功: 127.0.0.1:3306/plb_kj
[2025-08-01 15:29:31][ERROR] Token validation error: 无效的认证令牌
[2025-08-01 15:29:31][INFO] Request: GET /api/auth/captcha
[2025-08-01 15:29:31][INFO] Registering route: POST /api/admin/login
[2025-08-01 15:29:31][INFO] Registering route: POST /api/users/login
[2025-08-01 15:29:31][INFO] Registering route: GET /api/auth/captcha
[2025-08-01 15:29:31][INFO] Registering route: GET /api/auth/validate
[2025-08-01 15:29:31][INFO] Registering route: GET /api/users
[2025-08-01 15:29:31][INFO] Registering route: POST /api/users
[2025-08-01 15:29:31][INFO] Registering route: GET /api/users/profile
[2025-08-01 15:29:31][INFO] Registering route: GET /api/users/:id
[2025-08-01 15:29:31][INFO] Registering route: PUT /api/users/:id
[2025-08-01 15:29:31][INFO] Registering route: DELETE /api/users/:id
[2025-08-01 15:29:31][INFO] Registering route: GET /api/stats
[2025-08-01 15:29:31][INFO] Registering route: GET /api/stats/:type
[2025-08-01 15:29:31][INFO] Dispatching request: GET /api/auth/captcha
[2025-08-01 15:29:31][INFO] 数据库连接成功: 127.0.0.1:3306/plb_kj
[2025-08-01 15:29:31][INFO] Request: GET /api/auth/captcha
[2025-08-01 15:29:31][INFO] Registering route: POST /api/admin/login
[2025-08-01 15:29:31][INFO] Registering route: POST /api/users/login
[2025-08-01 15:29:31][INFO] Registering route: GET /api/auth/captcha
[2025-08-01 15:29:31][INFO] Registering route: GET /api/auth/validate
[2025-08-01 15:29:31][INFO] Registering route: GET /api/users
[2025-08-01 15:29:31][INFO] Registering route: POST /api/users
[2025-08-01 15:29:31][INFO] Registering route: GET /api/users/profile
[2025-08-01 15:29:31][INFO] Registering route: GET /api/users/:id
[2025-08-01 15:29:31][INFO] Registering route: PUT /api/users/:id
[2025-08-01 15:29:31][INFO] Registering route: DELETE /api/users/:id
[2025-08-01 15:29:31][INFO] Registering route: GET /api/stats
[2025-08-01 15:29:31][INFO] Registering route: GET /api/stats/:type
[2025-08-01 15:29:31][INFO] Dispatching request: GET /api/auth/captcha
[2025-08-01 15:29:31][INFO] 数据库连接成功: 127.0.0.1:3306/plb_kj
[2025-08-01 16:32:24][INFO] Request: GET /api/auth/captcha
[2025-08-01 16:32:24][INFO] Registering route: POST /api/admin/login
[2025-08-01 16:32:24][INFO] Registering route: POST /api/users/login
[2025-08-01 16:32:24][INFO] Registering route: GET /api/auth/captcha
[2025-08-01 16:32:24][INFO] Registering route: GET /api/auth/validate
[2025-08-01 16:32:24][INFO] Registering route: GET /api/users
[2025-08-01 16:32:24][INFO] Registering route: POST /api/users
[2025-08-01 16:32:24][INFO] Registering route: GET /api/users/profile
[2025-08-01 16:32:24][INFO] Registering route: GET /api/users/:id
[2025-08-01 16:32:24][INFO] Registering route: PUT /api/users/:id
[2025-08-01 16:32:24][INFO] Registering route: DELETE /api/users/:id
[2025-08-01 16:32:24][INFO] Registering route: GET /api/stats
[2025-08-01 16:32:24][INFO] Registering route: GET /api/stats/:type
[2025-08-01 16:32:24][INFO] Dispatching request: GET /api/auth/captcha
[2025-08-01 16:32:24][INFO] 数据库连接成功: 127.0.0.1:3306/plb_kj
[2025-08-01 16:32:24][INFO] Request: GET /api/auth/captcha
[2025-08-01 16:32:24][INFO] Registering route: POST /api/admin/login
[2025-08-01 16:32:24][INFO] Registering route: POST /api/users/login
[2025-08-01 16:32:24][INFO] Registering route: GET /api/auth/captcha
[2025-08-01 16:32:24][INFO] Registering route: GET /api/auth/validate
[2025-08-01 16:32:24][INFO] Registering route: GET /api/users
[2025-08-01 16:32:24][INFO] Registering route: POST /api/users
[2025-08-01 16:32:24][INFO] Registering route: GET /api/users/profile
[2025-08-01 16:32:24][INFO] Registering route: GET /api/users/:id
[2025-08-01 16:32:24][INFO] Registering route: PUT /api/users/:id
[2025-08-01 16:32:24][INFO] Registering route: DELETE /api/users/:id
[2025-08-01 16:32:24][INFO] Registering route: GET /api/stats
[2025-08-01 16:32:24][INFO] Registering route: GET /api/stats/:type
[2025-08-01 16:32:24][INFO] Dispatching request: GET /api/auth/captcha
[2025-08-01 16:32:24][INFO] 数据库连接成功: 127.0.0.1:3306/plb_kj
[2025-08-01 16:32:26][INFO] Request: GET /api/auth/validate
[2025-08-01 16:32:26][INFO] Registering route: POST /api/admin/login
[2025-08-01 16:32:26][INFO] Registering route: POST /api/users/login
[2025-08-01 16:32:26][INFO] Registering route: GET /api/auth/captcha
[2025-08-01 16:32:26][INFO] Registering route: GET /api/auth/validate
[2025-08-01 16:32:26][INFO] Registering route: GET /api/users
[2025-08-01 16:32:26][INFO] Registering route: POST /api/users
[2025-08-01 16:32:26][INFO] Registering route: GET /api/users/profile
[2025-08-01 16:32:26][INFO] Registering route: GET /api/users/:id
[2025-08-01 16:32:26][INFO] Registering route: PUT /api/users/:id
[2025-08-01 16:32:26][INFO] Registering route: DELETE /api/users/:id
[2025-08-01 16:32:26][INFO] Registering route: GET /api/stats
[2025-08-01 16:32:26][INFO] Registering route: GET /api/stats/:type
[2025-08-01 16:32:26][INFO] Dispatching request: GET /api/auth/validate
[2025-08-01 16:32:26][INFO] 数据库连接成功: 127.0.0.1:3306/plb_kj
[2025-08-01 16:32:26][ERROR] Token validation error: 无效的认证令牌
[2025-08-01 16:32:26][INFO] Request: GET /api/auth/captcha
[2025-08-01 16:32:26][INFO] Registering route: POST /api/admin/login
[2025-08-01 16:32:26][INFO] Registering route: POST /api/users/login
[2025-08-01 16:32:26][INFO] Registering route: GET /api/auth/captcha
[2025-08-01 16:32:26][INFO] Registering route: GET /api/auth/validate
[2025-08-01 16:32:26][INFO] Registering route: GET /api/users
[2025-08-01 16:32:26][INFO] Registering route: POST /api/users
[2025-08-01 16:32:26][INFO] Registering route: GET /api/users/profile
[2025-08-01 16:32:26][INFO] Registering route: GET /api/users/:id
[2025-08-01 16:32:26][INFO] Registering route: PUT /api/users/:id
[2025-08-01 16:32:26][INFO] Registering route: DELETE /api/users/:id
[2025-08-01 16:32:26][INFO] Registering route: GET /api/stats
[2025-08-01 16:32:26][INFO] Registering route: GET /api/stats/:type
[2025-08-01 16:32:26][INFO] Dispatching request: GET /api/auth/captcha
[2025-08-01 16:32:26][INFO] 数据库连接成功: 127.0.0.1:3306/plb_kj
[2025-08-01 16:32:26][INFO] Request: GET /api/auth/captcha
[2025-08-01 16:32:26][INFO] Registering route: POST /api/admin/login
[2025-08-01 16:32:26][INFO] Registering route: POST /api/users/login
[2025-08-01 16:32:26][INFO] Registering route: GET /api/auth/captcha
[2025-08-01 16:32:26][INFO] Registering route: GET /api/auth/validate
[2025-08-01 16:32:26][INFO] Registering route: GET /api/users
[2025-08-01 16:32:26][INFO] Registering route: POST /api/users
[2025-08-01 16:32:26][INFO] Registering route: GET /api/users/profile
[2025-08-01 16:32:26][INFO] Registering route: GET /api/users/:id
[2025-08-01 16:32:26][INFO] Registering route: PUT /api/users/:id
[2025-08-01 16:32:26][INFO] Registering route: DELETE /api/users/:id
[2025-08-01 16:32:26][INFO] Registering route: GET /api/stats
[2025-08-01 16:32:26][INFO] Registering route: GET /api/stats/:type
[2025-08-01 16:32:26][INFO] Dispatching request: GET /api/auth/captcha
[2025-08-01 16:32:26][INFO] 数据库连接成功: 127.0.0.1:3306/plb_kj
[2025-08-01 16:32:54][INFO] Request: POST /api/admin/login
[2025-08-01 16:32:54][INFO] Registering route: POST /api/admin/login
[2025-08-01 16:32:54][INFO] Registering route: POST /api/users/login
[2025-08-01 16:32:54][INFO] Registering route: GET /api/auth/captcha
[2025-08-01 16:32:54][INFO] Registering route: GET /api/auth/validate
[2025-08-01 16:32:54][INFO] Registering route: GET /api/users
[2025-08-01 16:32:54][INFO] Registering route: POST /api/users
[2025-08-01 16:32:54][INFO] Registering route: GET /api/users/profile
[2025-08-01 16:32:54][INFO] Registering route: GET /api/users/:id
[2025-08-01 16:32:54][INFO] Registering route: PUT /api/users/:id
[2025-08-01 16:32:54][INFO] Registering route: DELETE /api/users/:id
[2025-08-01 16:32:54][INFO] Registering route: GET /api/stats
[2025-08-01 16:32:54][INFO] Registering route: GET /api/stats/:type
[2025-08-01 16:32:54][INFO] Dispatching request: POST /api/admin/login
[2025-08-01 16:32:54][INFO] 数据库连接成功: 127.0.0.1:3306/plb_kj
[2025-08-01 16:32:54][INFO] Admin login attempt for username: admin
[2025-08-01 16:32:54][WARNING] Invalid captcha attempt for user: admin
[2025-08-01 16:32:54][ERROR] Admin login error: 验证码错误
[2025-08-01 16:32:54][INFO] Request: GET /api/auth/captcha
[2025-08-01 16:32:54][INFO] Registering route: POST /api/admin/login
[2025-08-01 16:32:54][INFO] Registering route: POST /api/users/login
[2025-08-01 16:32:54][INFO] Registering route: GET /api/auth/captcha
[2025-08-01 16:32:54][INFO] Registering route: GET /api/auth/validate
[2025-08-01 16:32:54][INFO] Registering route: GET /api/users
[2025-08-01 16:32:54][INFO] Registering route: POST /api/users
[2025-08-01 16:32:54][INFO] Registering route: GET /api/users/profile
[2025-08-01 16:32:54][INFO] Registering route: GET /api/users/:id
[2025-08-01 16:32:54][INFO] Registering route: PUT /api/users/:id
[2025-08-01 16:32:54][INFO] Registering route: DELETE /api/users/:id
[2025-08-01 16:32:54][INFO] Registering route: GET /api/stats
[2025-08-01 16:32:54][INFO] Registering route: GET /api/stats/:type
[2025-08-01 16:32:54][INFO] Dispatching request: GET /api/auth/captcha
[2025-08-01 16:32:54][INFO] 数据库连接成功: 127.0.0.1:3306/plb_kj
[2025-08-01 16:32:58][INFO] Request: POST /api/admin/login
[2025-08-01 16:32:58][INFO] Registering route: POST /api/admin/login
[2025-08-01 16:32:58][INFO] Registering route: POST /api/users/login
[2025-08-01 16:32:58][INFO] Registering route: GET /api/auth/captcha
[2025-08-01 16:32:58][INFO] Registering route: GET /api/auth/validate
[2025-08-01 16:32:58][INFO] Registering route: GET /api/users
[2025-08-01 16:32:58][INFO] Registering route: POST /api/users
[2025-08-01 16:32:58][INFO] Registering route: GET /api/users/profile
[2025-08-01 16:32:58][INFO] Registering route: GET /api/users/:id
[2025-08-01 16:32:58][INFO] Registering route: PUT /api/users/:id
[2025-08-01 16:32:58][INFO] Registering route: DELETE /api/users/:id
[2025-08-01 16:32:58][INFO] Registering route: GET /api/stats
[2025-08-01 16:32:58][INFO] Registering route: GET /api/stats/:type
[2025-08-01 16:32:58][INFO] Dispatching request: POST /api/admin/login
[2025-08-01 16:32:58][INFO] 数据库连接成功: 127.0.0.1:3306/plb_kj
[2025-08-01 16:32:58][INFO] Admin login attempt for username: admin
[2025-08-01 16:32:58][INFO] Admin user logged in successfully: admin
[2025-08-01 16:51:27][INFO] Request: GET /api/auth/captcha
[2025-08-01 16:51:27][INFO] Registering route: POST /api/admin/login
[2025-08-01 16:51:27][INFO] Registering route: POST /api/users/login
[2025-08-01 16:51:27][INFO] Registering route: GET /api/auth/captcha
[2025-08-01 16:51:27][INFO] Registering route: GET /api/auth/validate
[2025-08-01 16:51:27][INFO] Registering route: GET /api/users
[2025-08-01 16:51:27][INFO] Registering route: POST /api/users
[2025-08-01 16:51:27][INFO] Registering route: GET /api/users/profile
[2025-08-01 16:51:27][INFO] Registering route: GET /api/users/:id
[2025-08-01 16:51:27][INFO] Registering route: PUT /api/users/:id
[2025-08-01 16:51:27][INFO] Registering route: DELETE /api/users/:id
[2025-08-01 16:51:27][INFO] Registering route: GET /api/stats
[2025-08-01 16:51:27][INFO] Registering route: GET /api/stats/:type
[2025-08-01 16:51:27][INFO] Dispatching request: GET /api/auth/captcha
[2025-08-01 16:51:27][INFO] 数据库连接成功: 127.0.0.1:3306/plb_kj
[2025-08-01 16:51:27][INFO] Request: GET /api/auth/captcha
[2025-08-01 16:51:27][INFO] Registering route: POST /api/admin/login
[2025-08-01 16:51:27][INFO] Registering route: POST /api/users/login
[2025-08-01 16:51:27][INFO] Registering route: GET /api/auth/captcha
[2025-08-01 16:51:27][INFO] Registering route: GET /api/auth/validate
[2025-08-01 16:51:27][INFO] Registering route: GET /api/users
[2025-08-01 16:51:27][INFO] Registering route: POST /api/users
[2025-08-01 16:51:27][INFO] Registering route: GET /api/users/profile
[2025-08-01 16:51:27][INFO] Registering route: GET /api/users/:id
[2025-08-01 16:51:27][INFO] Registering route: PUT /api/users/:id
[2025-08-01 16:51:27][INFO] Registering route: DELETE /api/users/:id
[2025-08-01 16:51:27][INFO] Registering route: GET /api/stats
[2025-08-01 16:51:27][INFO] Registering route: GET /api/stats/:type
[2025-08-01 16:51:27][INFO] Dispatching request: GET /api/auth/captcha
[2025-08-01 16:51:27][INFO] 数据库连接成功: 127.0.0.1:3306/plb_kj
[2025-08-01 16:51:29][INFO] Request: GET /api/auth/validate
[2025-08-01 16:51:29][INFO] Registering route: POST /api/admin/login
[2025-08-01 16:51:29][INFO] Registering route: POST /api/users/login
[2025-08-01 16:51:29][INFO] Registering route: GET /api/auth/captcha
[2025-08-01 16:51:29][INFO] Registering route: GET /api/auth/validate
[2025-08-01 16:51:29][INFO] Registering route: GET /api/users
[2025-08-01 16:51:29][INFO] Registering route: POST /api/users
[2025-08-01 16:51:29][INFO] Registering route: GET /api/users/profile
[2025-08-01 16:51:29][INFO] Registering route: GET /api/users/:id
[2025-08-01 16:51:29][INFO] Registering route: PUT /api/users/:id
[2025-08-01 16:51:29][INFO] Registering route: DELETE /api/users/:id
[2025-08-01 16:51:29][INFO] Registering route: GET /api/stats
[2025-08-01 16:51:29][INFO] Registering route: GET /api/stats/:type
[2025-08-01 16:51:29][INFO] Dispatching request: GET /api/auth/validate
[2025-08-01 16:51:29][INFO] 数据库连接成功: 127.0.0.1:3306/plb_kj
[2025-08-01 16:51:29][ERROR] Token validation error: 无效的认证令牌
[2025-08-01 16:51:29][INFO] Request: GET /api/auth/captcha
[2025-08-01 16:51:29][INFO] Registering route: POST /api/admin/login
[2025-08-01 16:51:29][INFO] Registering route: POST /api/users/login
[2025-08-01 16:51:29][INFO] Registering route: GET /api/auth/captcha
[2025-08-01 16:51:29][INFO] Registering route: GET /api/auth/validate
[2025-08-01 16:51:29][INFO] Registering route: GET /api/users
[2025-08-01 16:51:29][INFO] Registering route: POST /api/users
[2025-08-01 16:51:29][INFO] Registering route: GET /api/users/profile
[2025-08-01 16:51:29][INFO] Registering route: GET /api/users/:id
[2025-08-01 16:51:29][INFO] Registering route: PUT /api/users/:id
[2025-08-01 16:51:29][INFO] Registering route: DELETE /api/users/:id
[2025-08-01 16:51:29][INFO] Registering route: GET /api/stats
[2025-08-01 16:51:29][INFO] Registering route: GET /api/stats/:type
[2025-08-01 16:51:29][INFO] Dispatching request: GET /api/auth/captcha
[2025-08-01 16:51:29][INFO] 数据库连接成功: 127.0.0.1:3306/plb_kj
[2025-08-01 16:51:29][INFO] Request: GET /api/auth/captcha
[2025-08-01 16:51:29][INFO] Registering route: POST /api/admin/login
[2025-08-01 16:51:29][INFO] Registering route: POST /api/users/login
[2025-08-01 16:51:29][INFO] Registering route: GET /api/auth/captcha
[2025-08-01 16:51:29][INFO] Registering route: GET /api/auth/validate
[2025-08-01 16:51:29][INFO] Registering route: GET /api/users
[2025-08-01 16:51:29][INFO] Registering route: POST /api/users
[2025-08-01 16:51:29][INFO] Registering route: GET /api/users/profile
[2025-08-01 16:51:29][INFO] Registering route: GET /api/users/:id
[2025-08-01 16:51:29][INFO] Registering route: PUT /api/users/:id
[2025-08-01 16:51:29][INFO] Registering route: DELETE /api/users/:id
[2025-08-01 16:51:29][INFO] Registering route: GET /api/stats
[2025-08-01 16:51:29][INFO] Registering route: GET /api/stats/:type
[2025-08-01 16:51:29][INFO] Dispatching request: GET /api/auth/captcha
[2025-08-01 16:51:29][INFO] 数据库连接成功: 127.0.0.1:3306/plb_kj
[2025-08-01 17:22:34][INFO] Request: GET /api/auth/captcha
[2025-08-01 17:22:34][INFO] Registering route: POST /api/admin/login
[2025-08-01 17:22:34][INFO] Registering route: POST /api/users/login
[2025-08-01 17:22:34][INFO] Registering route: GET /api/auth/captcha
[2025-08-01 17:22:34][INFO] Registering route: GET /api/auth/validate
[2025-08-01 17:22:34][INFO] Registering route: GET /api/users
[2025-08-01 17:22:34][INFO] Registering route: POST /api/users
[2025-08-01 17:22:34][INFO] Registering route: GET /api/users/profile
[2025-08-01 17:22:34][INFO] Registering route: GET /api/users/:id
[2025-08-01 17:22:34][INFO] Registering route: PUT /api/users/:id
[2025-08-01 17:22:34][INFO] Registering route: DELETE /api/users/:id
[2025-08-01 17:22:34][INFO] Registering route: GET /api/stats
[2025-08-01 17:22:34][INFO] Registering route: GET /api/stats/:type
[2025-08-01 17:22:34][INFO] Dispatching request: GET /api/auth/captcha
[2025-08-01 17:22:34][INFO] 数据库连接成功: 127.0.0.1:3306/plb_kj
[2025-08-01 17:22:34][INFO] Request: GET /api/auth/captcha
[2025-08-01 17:22:34][INFO] Registering route: POST /api/admin/login
[2025-08-01 17:22:34][INFO] Registering route: POST /api/users/login
[2025-08-01 17:22:34][INFO] Registering route: GET /api/auth/captcha
[2025-08-01 17:22:34][INFO] Registering route: GET /api/auth/validate
[2025-08-01 17:22:34][INFO] Registering route: GET /api/users
[2025-08-01 17:22:34][INFO] Registering route: POST /api/users
[2025-08-01 17:22:34][INFO] Registering route: GET /api/users/profile
[2025-08-01 17:22:34][INFO] Registering route: GET /api/users/:id
[2025-08-01 17:22:34][INFO] Registering route: PUT /api/users/:id
[2025-08-01 17:22:34][INFO] Registering route: DELETE /api/users/:id
[2025-08-01 17:22:34][INFO] Registering route: GET /api/stats
[2025-08-01 17:22:34][INFO] Registering route: GET /api/stats/:type
[2025-08-01 17:22:34][INFO] Dispatching request: GET /api/auth/captcha
[2025-08-01 17:22:34][INFO] 数据库连接成功: 127.0.0.1:3306/plb_kj
[2025-08-01 17:22:36][INFO] Request: GET /api/auth/validate
[2025-08-01 17:22:36][INFO] Registering route: POST /api/admin/login
[2025-08-01 17:22:36][INFO] Registering route: POST /api/users/login
[2025-08-01 17:22:36][INFO] Registering route: GET /api/auth/captcha
[2025-08-01 17:22:36][INFO] Registering route: GET /api/auth/validate
[2025-08-01 17:22:36][INFO] Registering route: GET /api/users
[2025-08-01 17:22:36][INFO] Registering route: POST /api/users
[2025-08-01 17:22:36][INFO] Registering route: GET /api/users/profile
[2025-08-01 17:22:36][INFO] Registering route: GET /api/users/:id
[2025-08-01 17:22:36][INFO] Registering route: PUT /api/users/:id
[2025-08-01 17:22:36][INFO] Registering route: DELETE /api/users/:id
[2025-08-01 17:22:36][INFO] Registering route: GET /api/stats
[2025-08-01 17:22:36][INFO] Registering route: GET /api/stats/:type
[2025-08-01 17:22:36][INFO] Dispatching request: GET /api/auth/validate
[2025-08-01 17:22:36][INFO] 数据库连接成功: 127.0.0.1:3306/plb_kj
[2025-08-01 17:22:36][ERROR] Token validation error: 无效的认证令牌
[2025-08-01 17:22:36][INFO] Request: GET /api/auth/captcha
[2025-08-01 17:22:36][INFO] Registering route: POST /api/admin/login
[2025-08-01 17:22:36][INFO] Registering route: POST /api/users/login
[2025-08-01 17:22:36][INFO] Registering route: GET /api/auth/captcha
[2025-08-01 17:22:36][INFO] Registering route: GET /api/auth/validate
[2025-08-01 17:22:36][INFO] Registering route: GET /api/users
[2025-08-01 17:22:36][INFO] Registering route: POST /api/users
[2025-08-01 17:22:36][INFO] Registering route: GET /api/users/profile
[2025-08-01 17:22:36][INFO] Registering route: GET /api/users/:id
[2025-08-01 17:22:36][INFO] Registering route: PUT /api/users/:id
[2025-08-01 17:22:36][INFO] Registering route: DELETE /api/users/:id
[2025-08-01 17:22:36][INFO] Registering route: GET /api/stats
[2025-08-01 17:22:36][INFO] Registering route: GET /api/stats/:type
[2025-08-01 17:22:36][INFO] Dispatching request: GET /api/auth/captcha
[2025-08-01 17:22:36][INFO] 数据库连接成功: 127.0.0.1:3306/plb_kj
[2025-08-01 17:22:36][INFO] Request: GET /api/auth/captcha
[2025-08-01 17:22:36][INFO] Registering route: POST /api/admin/login
[2025-08-01 17:22:36][INFO] Registering route: POST /api/users/login
[2025-08-01 17:22:36][INFO] Registering route: GET /api/auth/captcha
[2025-08-01 17:22:36][INFO] Registering route: GET /api/auth/validate
[2025-08-01 17:22:36][INFO] Registering route: GET /api/users
[2025-08-01 17:22:36][INFO] Registering route: POST /api/users
[2025-08-01 17:22:36][INFO] Registering route: GET /api/users/profile
[2025-08-01 17:22:36][INFO] Registering route: GET /api/users/:id
[2025-08-01 17:22:36][INFO] Registering route: PUT /api/users/:id
[2025-08-01 17:22:36][INFO] Registering route: DELETE /api/users/:id
[2025-08-01 17:22:36][INFO] Registering route: GET /api/stats
[2025-08-01 17:22:36][INFO] Registering route: GET /api/stats/:type
[2025-08-01 17:22:36][INFO] Dispatching request: GET /api/auth/captcha
[2025-08-01 17:22:36][INFO] 数据库连接成功: 127.0.0.1:3306/plb_kj
[2025-08-01 17:50:55][INFO] Request: GET /api/auth/captcha
[2025-08-01 17:50:55][INFO] Registering route: POST /api/admin/login
[2025-08-01 17:50:55][INFO] Registering route: POST /api/users/login
[2025-08-01 17:50:55][INFO] Registering route: GET /api/auth/captcha
[2025-08-01 17:50:55][INFO] Registering route: GET /api/auth/validate
[2025-08-01 17:50:55][INFO] Registering route: GET /api/users
[2025-08-01 17:50:55][INFO] Registering route: POST /api/users
[2025-08-01 17:50:55][INFO] Registering route: GET /api/users/profile
[2025-08-01 17:50:55][INFO] Registering route: GET /api/users/:id
[2025-08-01 17:50:55][INFO] Registering route: PUT /api/users/:id
[2025-08-01 17:50:55][INFO] Registering route: DELETE /api/users/:id
[2025-08-01 17:50:55][INFO] Registering route: GET /api/stats
[2025-08-01 17:50:55][INFO] Registering route: GET /api/stats/:type
[2025-08-01 17:50:55][INFO] Dispatching request: GET /api/auth/captcha
[2025-08-01 17:50:55][INFO] 数据库连接成功: 127.0.0.1:3306/plb_kj
[2025-08-01 17:50:55][INFO] Request: GET /api/auth/captcha
[2025-08-01 17:50:55][INFO] Registering route: POST /api/admin/login
[2025-08-01 17:50:55][INFO] Registering route: POST /api/users/login
[2025-08-01 17:50:55][INFO] Registering route: GET /api/auth/captcha
[2025-08-01 17:50:55][INFO] Registering route: GET /api/auth/validate
[2025-08-01 17:50:55][INFO] Registering route: GET /api/users
[2025-08-01 17:50:55][INFO] Registering route: POST /api/users
[2025-08-01 17:50:55][INFO] Registering route: GET /api/users/profile
[2025-08-01 17:50:55][INFO] Registering route: GET /api/users/:id
[2025-08-01 17:50:55][INFO] Registering route: PUT /api/users/:id
[2025-08-01 17:50:55][INFO] Registering route: DELETE /api/users/:id
[2025-08-01 17:50:55][INFO] Registering route: GET /api/stats
[2025-08-01 17:50:55][INFO] Registering route: GET /api/stats/:type
[2025-08-01 17:50:55][INFO] Dispatching request: GET /api/auth/captcha
[2025-08-01 17:50:55][INFO] 数据库连接成功: 127.0.0.1:3306/plb_kj
[2025-08-01 17:50:57][INFO] Request: GET /api/auth/validate
[2025-08-01 17:50:57][INFO] Registering route: POST /api/admin/login
[2025-08-01 17:50:57][INFO] Registering route: POST /api/users/login
[2025-08-01 17:50:57][INFO] Registering route: GET /api/auth/captcha
[2025-08-01 17:50:57][INFO] Registering route: GET /api/auth/validate
[2025-08-01 17:50:57][INFO] Registering route: GET /api/users
[2025-08-01 17:50:57][INFO] Registering route: POST /api/users
[2025-08-01 17:50:57][INFO] Registering route: GET /api/users/profile
[2025-08-01 17:50:57][INFO] Registering route: GET /api/users/:id
[2025-08-01 17:50:57][INFO] Registering route: PUT /api/users/:id
[2025-08-01 17:50:57][INFO] Registering route: DELETE /api/users/:id
[2025-08-01 17:50:57][INFO] Registering route: GET /api/stats
[2025-08-01 17:50:57][INFO] Registering route: GET /api/stats/:type
[2025-08-01 17:50:57][INFO] Dispatching request: GET /api/auth/validate
[2025-08-01 17:50:57][INFO] 数据库连接成功: 127.0.0.1:3306/plb_kj
[2025-08-01 17:50:57][ERROR] Token validation error: 无效的认证令牌
[2025-08-01 17:50:57][INFO] Request: GET /api/auth/captcha
[2025-08-01 17:50:57][INFO] Registering route: POST /api/admin/login
[2025-08-01 17:50:57][INFO] Registering route: POST /api/users/login
[2025-08-01 17:50:57][INFO] Registering route: GET /api/auth/captcha
[2025-08-01 17:50:57][INFO] Registering route: GET /api/auth/validate
[2025-08-01 17:50:57][INFO] Registering route: GET /api/users
[2025-08-01 17:50:57][INFO] Registering route: POST /api/users
[2025-08-01 17:50:57][INFO] Registering route: GET /api/users/profile
[2025-08-01 17:50:57][INFO] Registering route: GET /api/users/:id
[2025-08-01 17:50:57][INFO] Registering route: PUT /api/users/:id
[2025-08-01 17:50:57][INFO] Registering route: DELETE /api/users/:id
[2025-08-01 17:50:57][INFO] Registering route: GET /api/stats
[2025-08-01 17:50:57][INFO] Registering route: GET /api/stats/:type
[2025-08-01 17:50:57][INFO] Dispatching request: GET /api/auth/captcha
[2025-08-01 17:50:57][INFO] 数据库连接成功: 127.0.0.1:3306/plb_kj
[2025-08-01 17:50:57][INFO] Request: GET /api/auth/captcha
[2025-08-01 17:50:57][INFO] Registering route: POST /api/admin/login
[2025-08-01 17:50:57][INFO] Registering route: POST /api/users/login
[2025-08-01 17:50:57][INFO] Registering route: GET /api/auth/captcha
[2025-08-01 17:50:57][INFO] Registering route: GET /api/auth/validate
[2025-08-01 17:50:57][INFO] Registering route: GET /api/users
[2025-08-01 17:50:57][INFO] Registering route: POST /api/users
[2025-08-01 17:50:57][INFO] Registering route: GET /api/users/profile
[2025-08-01 17:50:57][INFO] Registering route: GET /api/users/:id
[2025-08-01 17:50:57][INFO] Registering route: PUT /api/users/:id
[2025-08-01 17:50:57][INFO] Registering route: DELETE /api/users/:id
[2025-08-01 17:50:57][INFO] Registering route: GET /api/stats
[2025-08-01 17:50:57][INFO] Registering route: GET /api/stats/:type
[2025-08-01 17:50:57][INFO] Dispatching request: GET /api/auth/captcha
[2025-08-01 17:50:57][INFO] 数据库连接成功: 127.0.0.1:3306/plb_kj
[2025-08-01 17:51:13][INFO] Request: GET /api/auth/captcha
[2025-08-01 17:51:13][INFO] Registering route: POST /api/admin/login
[2025-08-01 17:51:13][INFO] Registering route: POST /api/users/login
[2025-08-01 17:51:13][INFO] Registering route: GET /api/auth/captcha
[2025-08-01 17:51:13][INFO] Registering route: GET /api/auth/validate
[2025-08-01 17:51:13][INFO] Registering route: GET /api/users
[2025-08-01 17:51:13][INFO] Registering route: POST /api/users
[2025-08-01 17:51:13][INFO] Registering route: GET /api/users/profile
[2025-08-01 17:51:13][INFO] Registering route: GET /api/users/:id
[2025-08-01 17:51:13][INFO] Registering route: PUT /api/users/:id
[2025-08-01 17:51:13][INFO] Registering route: DELETE /api/users/:id
[2025-08-01 17:51:13][INFO] Registering route: GET /api/stats
[2025-08-01 17:51:13][INFO] Registering route: GET /api/stats/:type
[2025-08-01 17:51:13][INFO] Dispatching request: GET /api/auth/captcha
[2025-08-01 17:51:13][INFO] 数据库连接成功: 127.0.0.1:3306/plb_kj
[2025-08-01 17:51:13][INFO] Request: GET /api/auth/captcha
[2025-08-01 17:51:13][INFO] Registering route: POST /api/admin/login
[2025-08-01 17:51:13][INFO] Registering route: POST /api/users/login
[2025-08-01 17:51:13][INFO] Registering route: GET /api/auth/captcha
[2025-08-01 17:51:13][INFO] Registering route: GET /api/auth/validate
[2025-08-01 17:51:13][INFO] Registering route: GET /api/users
[2025-08-01 17:51:13][INFO] Registering route: POST /api/users
[2025-08-01 17:51:13][INFO] Registering route: GET /api/users/profile
[2025-08-01 17:51:13][INFO] Registering route: GET /api/users/:id
[2025-08-01 17:51:13][INFO] Registering route: PUT /api/users/:id
[2025-08-01 17:51:13][INFO] Registering route: DELETE /api/users/:id
[2025-08-01 17:51:13][INFO] Registering route: GET /api/stats
[2025-08-01 17:51:13][INFO] Registering route: GET /api/stats/:type
[2025-08-01 17:51:13][INFO] Dispatching request: GET /api/auth/captcha
[2025-08-01 17:51:13][INFO] 数据库连接成功: 127.0.0.1:3306/plb_kj
[2025-08-01 17:51:15][INFO] Request: GET /api/auth/validate
[2025-08-01 17:51:15][INFO] Registering route: POST /api/admin/login
[2025-08-01 17:51:15][INFO] Registering route: POST /api/users/login
[2025-08-01 17:51:15][INFO] Registering route: GET /api/auth/captcha
[2025-08-01 17:51:15][INFO] Registering route: GET /api/auth/validate
[2025-08-01 17:51:15][INFO] Registering route: GET /api/users
[2025-08-01 17:51:15][INFO] Registering route: POST /api/users
[2025-08-01 17:51:15][INFO] Registering route: GET /api/users/profile
[2025-08-01 17:51:15][INFO] Registering route: GET /api/users/:id
[2025-08-01 17:51:15][INFO] Registering route: PUT /api/users/:id
[2025-08-01 17:51:15][INFO] Registering route: DELETE /api/users/:id
[2025-08-01 17:51:15][INFO] Registering route: GET /api/stats
[2025-08-01 17:51:15][INFO] Registering route: GET /api/stats/:type
[2025-08-01 17:51:15][INFO] Dispatching request: GET /api/auth/validate
[2025-08-01 17:51:15][INFO] 数据库连接成功: 127.0.0.1:3306/plb_kj
[2025-08-01 17:51:15][ERROR] Token validation error: 无效的认证令牌
[2025-08-01 17:51:15][INFO] Request: GET /api/auth/captcha
[2025-08-01 17:51:15][INFO] Registering route: POST /api/admin/login
[2025-08-01 17:51:15][INFO] Registering route: POST /api/users/login
[2025-08-01 17:51:15][INFO] Registering route: GET /api/auth/captcha
[2025-08-01 17:51:15][INFO] Registering route: GET /api/auth/validate
[2025-08-01 17:51:15][INFO] Registering route: GET /api/users
[2025-08-01 17:51:15][INFO] Registering route: POST /api/users
[2025-08-01 17:51:15][INFO] Registering route: GET /api/users/profile
[2025-08-01 17:51:15][INFO] Registering route: GET /api/users/:id
[2025-08-01 17:51:15][INFO] Registering route: PUT /api/users/:id
[2025-08-01 17:51:15][INFO] Registering route: DELETE /api/users/:id
[2025-08-01 17:51:15][INFO] Registering route: GET /api/stats
[2025-08-01 17:51:15][INFO] Registering route: GET /api/stats/:type
[2025-08-01 17:51:15][INFO] Dispatching request: GET /api/auth/captcha
[2025-08-01 17:51:15][INFO] 数据库连接成功: 127.0.0.1:3306/plb_kj
[2025-08-01 17:51:15][INFO] Request: GET /api/auth/captcha
[2025-08-01 17:51:15][INFO] Registering route: POST /api/admin/login
[2025-08-01 17:51:15][INFO] Registering route: POST /api/users/login
[2025-08-01 17:51:15][INFO] Registering route: GET /api/auth/captcha
[2025-08-01 17:51:15][INFO] Registering route: GET /api/auth/validate
[2025-08-01 17:51:15][INFO] Registering route: GET /api/users
[2025-08-01 17:51:15][INFO] Registering route: POST /api/users
[2025-08-01 17:51:15][INFO] Registering route: GET /api/users/profile
[2025-08-01 17:51:15][INFO] Registering route: GET /api/users/:id
[2025-08-01 17:51:15][INFO] Registering route: PUT /api/users/:id
[2025-08-01 17:51:15][INFO] Registering route: DELETE /api/users/:id
[2025-08-01 17:51:15][INFO] Registering route: GET /api/stats
[2025-08-01 17:51:15][INFO] Registering route: GET /api/stats/:type
[2025-08-01 17:51:15][INFO] Dispatching request: GET /api/auth/captcha
[2025-08-01 17:51:15][INFO] 数据库连接成功: 127.0.0.1:3306/plb_kj
[2025-08-01 18:15:28][INFO] Request: GET /api/auth/captcha
[2025-08-01 18:15:28][INFO] Registering route: POST /api/admin/login
[2025-08-01 18:15:28][INFO] Registering route: POST /api/users/login
[2025-08-01 18:15:28][INFO] Registering route: GET /api/auth/captcha
[2025-08-01 18:15:28][INFO] Registering route: GET /api/auth/validate
[2025-08-01 18:15:28][INFO] Registering route: GET /api/users
[2025-08-01 18:15:28][INFO] Registering route: POST /api/users
[2025-08-01 18:15:28][INFO] Registering route: GET /api/users/profile
[2025-08-01 18:15:28][INFO] Registering route: GET /api/users/:id
[2025-08-01 18:15:28][INFO] Registering route: PUT /api/users/:id
[2025-08-01 18:15:28][INFO] Registering route: DELETE /api/users/:id
[2025-08-01 18:15:28][INFO] Registering route: GET /api/stats
[2025-08-01 18:15:28][INFO] Registering route: GET /api/stats/:type
[2025-08-01 18:15:28][INFO] Dispatching request: GET /api/auth/captcha
[2025-08-01 18:15:28][INFO] 数据库连接成功: 127.0.0.1:3306/plb_kj
[2025-08-01 18:15:28][INFO] Request: GET /api/auth/captcha
[2025-08-01 18:15:28][INFO] Registering route: POST /api/admin/login
[2025-08-01 18:15:28][INFO] Registering route: POST /api/users/login
[2025-08-01 18:15:28][INFO] Registering route: GET /api/auth/captcha
[2025-08-01 18:15:28][INFO] Registering route: GET /api/auth/validate
[2025-08-01 18:15:28][INFO] Registering route: GET /api/users
[2025-08-01 18:15:28][INFO] Registering route: POST /api/users
[2025-08-01 18:15:28][INFO] Registering route: GET /api/users/profile
[2025-08-01 18:15:28][INFO] Registering route: GET /api/users/:id
[2025-08-01 18:15:28][INFO] Registering route: PUT /api/users/:id
[2025-08-01 18:15:28][INFO] Registering route: DELETE /api/users/:id
[2025-08-01 18:15:28][INFO] Registering route: GET /api/stats
[2025-08-01 18:15:28][INFO] Registering route: GET /api/stats/:type
[2025-08-01 18:15:28][INFO] Dispatching request: GET /api/auth/captcha
[2025-08-01 18:15:28][INFO] 数据库连接成功: 127.0.0.1:3306/plb_kj
[2025-08-01 18:15:30][INFO] Request: GET /api/auth/validate
[2025-08-01 18:15:30][INFO] Registering route: POST /api/admin/login
[2025-08-01 18:15:30][INFO] Registering route: POST /api/users/login
[2025-08-01 18:15:30][INFO] Registering route: GET /api/auth/captcha
[2025-08-01 18:15:30][INFO] Registering route: GET /api/auth/validate
[2025-08-01 18:15:30][INFO] Registering route: GET /api/users
[2025-08-01 18:15:30][INFO] Registering route: POST /api/users
[2025-08-01 18:15:30][INFO] Registering route: GET /api/users/profile
[2025-08-01 18:15:30][INFO] Registering route: GET /api/users/:id
[2025-08-01 18:15:30][INFO] Registering route: PUT /api/users/:id
[2025-08-01 18:15:30][INFO] Registering route: DELETE /api/users/:id
[2025-08-01 18:15:30][INFO] Registering route: GET /api/stats
[2025-08-01 18:15:30][INFO] Registering route: GET /api/stats/:type
[2025-08-01 18:15:30][INFO] Dispatching request: GET /api/auth/validate
[2025-08-01 18:15:30][INFO] 数据库连接成功: 127.0.0.1:3306/plb_kj
[2025-08-01 18:15:30][ERROR] Token validation error: 无效的认证令牌
[2025-08-01 18:15:30][INFO] Request: GET /api/auth/captcha
[2025-08-01 18:15:30][INFO] Registering route: POST /api/admin/login
[2025-08-01 18:15:30][INFO] Registering route: POST /api/users/login
[2025-08-01 18:15:30][INFO] Registering route: GET /api/auth/captcha
[2025-08-01 18:15:30][INFO] Registering route: GET /api/auth/validate
[2025-08-01 18:15:30][INFO] Registering route: GET /api/users
[2025-08-01 18:15:30][INFO] Registering route: POST /api/users
[2025-08-01 18:15:30][INFO] Registering route: GET /api/users/profile
[2025-08-01 18:15:30][INFO] Registering route: GET /api/users/:id
[2025-08-01 18:15:30][INFO] Registering route: PUT /api/users/:id
[2025-08-01 18:15:30][INFO] Registering route: DELETE /api/users/:id
[2025-08-01 18:15:30][INFO] Registering route: GET /api/stats
[2025-08-01 18:15:30][INFO] Registering route: GET /api/stats/:type
[2025-08-01 18:15:30][INFO] Dispatching request: GET /api/auth/captcha
[2025-08-01 18:15:30][INFO] 数据库连接成功: 127.0.0.1:3306/plb_kj
[2025-08-01 18:15:30][INFO] Request: GET /api/auth/captcha
[2025-08-01 18:15:30][INFO] Registering route: POST /api/admin/login
[2025-08-01 18:15:30][INFO] Registering route: POST /api/users/login
[2025-08-01 18:15:30][INFO] Registering route: GET /api/auth/captcha
[2025-08-01 18:15:30][INFO] Registering route: GET /api/auth/validate
[2025-08-01 18:15:30][INFO] Registering route: GET /api/users
[2025-08-01 18:15:30][INFO] Registering route: POST /api/users
[2025-08-01 18:15:30][INFO] Registering route: GET /api/users/profile
[2025-08-01 18:15:30][INFO] Registering route: GET /api/users/:id
[2025-08-01 18:15:30][INFO] Registering route: PUT /api/users/:id
[2025-08-01 18:15:30][INFO] Registering route: DELETE /api/users/:id
[2025-08-01 18:15:30][INFO] Registering route: GET /api/stats
[2025-08-01 18:15:30][INFO] Registering route: GET /api/stats/:type
[2025-08-01 18:15:30][INFO] Dispatching request: GET /api/auth/captcha
[2025-08-01 18:15:30][INFO] 数据库连接成功: 127.0.0.1:3306/plb_kj
[2025-08-01 18:16:24][INFO] Request: POST /api/admin/login
[2025-08-01 18:16:24][INFO] Registering route: POST /api/admin/login
[2025-08-01 18:16:24][INFO] Registering route: POST /api/users/login
[2025-08-01 18:16:24][INFO] Registering route: GET /api/auth/captcha
[2025-08-01 18:16:24][INFO] Registering route: GET /api/auth/validate
[2025-08-01 18:16:24][INFO] Registering route: GET /api/users
[2025-08-01 18:16:24][INFO] Registering route: POST /api/users
[2025-08-01 18:16:24][INFO] Registering route: GET /api/users/profile
[2025-08-01 18:16:24][INFO] Registering route: GET /api/users/:id
[2025-08-01 18:16:24][INFO] Registering route: PUT /api/users/:id
[2025-08-01 18:16:24][INFO] Registering route: DELETE /api/users/:id
[2025-08-01 18:16:24][INFO] Registering route: GET /api/stats
[2025-08-01 18:16:24][INFO] Registering route: GET /api/stats/:type
[2025-08-01 18:16:24][INFO] Dispatching request: POST /api/admin/login
[2025-08-01 18:16:24][INFO] 数据库连接成功: 127.0.0.1:3306/plb_kj
[2025-08-01 18:16:24][INFO] Admin login attempt for username: admin
[2025-08-01 18:16:24][WARNING] Invalid captcha attempt for user: admin
[2025-08-01 18:16:24][ERROR] Admin login error: 验证码错误
[2025-08-01 18:16:24][INFO] Request: GET /api/auth/captcha
[2025-08-01 18:16:24][INFO] Registering route: POST /api/admin/login
[2025-08-01 18:16:24][INFO] Registering route: POST /api/users/login
[2025-08-01 18:16:24][INFO] Registering route: GET /api/auth/captcha
[2025-08-01 18:16:24][INFO] Registering route: GET /api/auth/validate
[2025-08-01 18:16:24][INFO] Registering route: GET /api/users
[2025-08-01 18:16:24][INFO] Registering route: POST /api/users
[2025-08-01 18:16:24][INFO] Registering route: GET /api/users/profile
[2025-08-01 18:16:24][INFO] Registering route: GET /api/users/:id
[2025-08-01 18:16:24][INFO] Registering route: PUT /api/users/:id
[2025-08-01 18:16:24][INFO] Registering route: DELETE /api/users/:id
[2025-08-01 18:16:24][INFO] Registering route: GET /api/stats
[2025-08-01 18:16:24][INFO] Registering route: GET /api/stats/:type
[2025-08-01 18:16:24][INFO] Dispatching request: GET /api/auth/captcha
[2025-08-01 18:16:24][INFO] 数据库连接成功: 127.0.0.1:3306/plb_kj
[2025-08-01 18:16:31][INFO] Request: POST /api/admin/login
[2025-08-01 18:16:31][INFO] Registering route: POST /api/admin/login
[2025-08-01 18:16:31][INFO] Registering route: POST /api/users/login
[2025-08-01 18:16:31][INFO] Registering route: GET /api/auth/captcha
[2025-08-01 18:16:31][INFO] Registering route: GET /api/auth/validate
[2025-08-01 18:16:31][INFO] Registering route: GET /api/users
[2025-08-01 18:16:31][INFO] Registering route: POST /api/users
[2025-08-01 18:16:31][INFO] Registering route: GET /api/users/profile
[2025-08-01 18:16:31][INFO] Registering route: GET /api/users/:id
[2025-08-01 18:16:31][INFO] Registering route: PUT /api/users/:id
[2025-08-01 18:16:31][INFO] Registering route: DELETE /api/users/:id
[2025-08-01 18:16:31][INFO] Registering route: GET /api/stats
[2025-08-01 18:16:31][INFO] Registering route: GET /api/stats/:type
[2025-08-01 18:16:31][INFO] Dispatching request: POST /api/admin/login
[2025-08-01 18:16:31][INFO] 数据库连接成功: 127.0.0.1:3306/plb_kj
[2025-08-01 18:16:31][INFO] Admin login attempt for username: admin
[2025-08-01 18:16:31][WARNING] Invalid captcha attempt for user: admin
[2025-08-01 18:16:31][ERROR] Admin login error: 验证码错误
[2025-08-01 18:16:31][INFO] Request: GET /api/auth/captcha
[2025-08-01 18:16:31][INFO] Registering route: POST /api/admin/login
[2025-08-01 18:16:31][INFO] Registering route: POST /api/users/login
[2025-08-01 18:16:31][INFO] Registering route: GET /api/auth/captcha
[2025-08-01 18:16:31][INFO] Registering route: GET /api/auth/validate
[2025-08-01 18:16:31][INFO] Registering route: GET /api/users
[2025-08-01 18:16:31][INFO] Registering route: POST /api/users
[2025-08-01 18:16:31][INFO] Registering route: GET /api/users/profile
[2025-08-01 18:16:31][INFO] Registering route: GET /api/users/:id
[2025-08-01 18:16:31][INFO] Registering route: PUT /api/users/:id
[2025-08-01 18:16:31][INFO] Registering route: DELETE /api/users/:id
[2025-08-01 18:16:31][INFO] Registering route: GET /api/stats
[2025-08-01 18:16:31][INFO] Registering route: GET /api/stats/:type
[2025-08-01 18:16:31][INFO] Dispatching request: GET /api/auth/captcha
[2025-08-01 18:16:31][INFO] 数据库连接成功: 127.0.0.1:3306/plb_kj
[2025-08-01 18:16:35][INFO] Request: POST /api/admin/login
[2025-08-01 18:16:35][INFO] Registering route: POST /api/admin/login
[2025-08-01 18:16:35][INFO] Registering route: POST /api/users/login
[2025-08-01 18:16:35][INFO] Registering route: GET /api/auth/captcha
[2025-08-01 18:16:35][INFO] Registering route: GET /api/auth/validate
[2025-08-01 18:16:35][INFO] Registering route: GET /api/users
[2025-08-01 18:16:35][INFO] Registering route: POST /api/users
[2025-08-01 18:16:35][INFO] Registering route: GET /api/users/profile
[2025-08-01 18:16:35][INFO] Registering route: GET /api/users/:id
[2025-08-01 18:16:35][INFO] Registering route: PUT /api/users/:id
[2025-08-01 18:16:35][INFO] Registering route: DELETE /api/users/:id
[2025-08-01 18:16:35][INFO] Registering route: GET /api/stats
[2025-08-01 18:16:35][INFO] Registering route: GET /api/stats/:type
[2025-08-01 18:16:35][INFO] Dispatching request: POST /api/admin/login
[2025-08-01 18:16:35][INFO] 数据库连接成功: 127.0.0.1:3306/plb_kj
[2025-08-01 18:16:35][INFO] Admin login attempt for username: admin
[2025-08-01 18:16:35][INFO] Admin user logged in successfully: admin
[2025-08-01 18:29:45][INFO] Request: GET /api/auth/captcha
[2025-08-01 18:29:45][INFO] Registering route: POST /api/admin/login
[2025-08-01 18:29:45][INFO] Registering route: POST /api/users/login
[2025-08-01 18:29:45][INFO] Registering route: GET /api/auth/captcha
[2025-08-01 18:29:45][INFO] Registering route: GET /api/auth/validate
[2025-08-01 18:29:45][INFO] Registering route: GET /api/users
[2025-08-01 18:29:45][INFO] Registering route: POST /api/users
[2025-08-01 18:29:45][INFO] Registering route: GET /api/users/profile
[2025-08-01 18:29:45][INFO] Registering route: GET /api/users/:id
[2025-08-01 18:29:45][INFO] Registering route: PUT /api/users/:id
[2025-08-01 18:29:45][INFO] Registering route: DELETE /api/users/:id
[2025-08-01 18:29:45][INFO] Registering route: GET /api/stats
[2025-08-01 18:29:45][INFO] Registering route: GET /api/stats/:type
[2025-08-01 18:29:45][INFO] Dispatching request: GET /api/auth/captcha
[2025-08-01 18:29:45][INFO] 数据库连接成功: 127.0.0.1:3306/plb_kj
[2025-08-01 18:29:45][INFO] Request: GET /api/auth/captcha
[2025-08-01 18:29:45][INFO] Registering route: POST /api/admin/login
[2025-08-01 18:29:45][INFO] Registering route: POST /api/users/login
[2025-08-01 18:29:45][INFO] Registering route: GET /api/auth/captcha
[2025-08-01 18:29:45][INFO] Registering route: GET /api/auth/validate
[2025-08-01 18:29:45][INFO] Registering route: GET /api/users
[2025-08-01 18:29:45][INFO] Registering route: POST /api/users
[2025-08-01 18:29:45][INFO] Registering route: GET /api/users/profile
[2025-08-01 18:29:45][INFO] Registering route: GET /api/users/:id
[2025-08-01 18:29:45][INFO] Registering route: PUT /api/users/:id
[2025-08-01 18:29:45][INFO] Registering route: DELETE /api/users/:id
[2025-08-01 18:29:45][INFO] Registering route: GET /api/stats
[2025-08-01 18:29:45][INFO] Registering route: GET /api/stats/:type
[2025-08-01 18:29:45][INFO] Dispatching request: GET /api/auth/captcha
[2025-08-01 18:29:45][INFO] 数据库连接成功: 127.0.0.1:3306/plb_kj
[2025-08-01 18:29:47][INFO] Request: GET /api/auth/validate
[2025-08-01 18:29:47][INFO] Registering route: POST /api/admin/login
[2025-08-01 18:29:47][INFO] Registering route: POST /api/users/login
[2025-08-01 18:29:47][INFO] Registering route: GET /api/auth/captcha
[2025-08-01 18:29:47][INFO] Registering route: GET /api/auth/validate
[2025-08-01 18:29:47][INFO] Registering route: GET /api/users
[2025-08-01 18:29:47][INFO] Registering route: POST /api/users
[2025-08-01 18:29:47][INFO] Registering route: GET /api/users/profile
[2025-08-01 18:29:47][INFO] Registering route: GET /api/users/:id
[2025-08-01 18:29:47][INFO] Registering route: PUT /api/users/:id
[2025-08-01 18:29:47][INFO] Registering route: DELETE /api/users/:id
[2025-08-01 18:29:47][INFO] Registering route: GET /api/stats
[2025-08-01 18:29:47][INFO] Registering route: GET /api/stats/:type
[2025-08-01 18:29:47][INFO] Dispatching request: GET /api/auth/validate
[2025-08-01 18:29:47][INFO] 数据库连接成功: 127.0.0.1:3306/plb_kj
[2025-08-01 18:29:47][ERROR] Token validation error: 无效的认证令牌
[2025-08-01 18:29:47][INFO] Request: GET /api/auth/captcha
[2025-08-01 18:29:47][INFO] Registering route: POST /api/admin/login
[2025-08-01 18:29:47][INFO] Registering route: POST /api/users/login
[2025-08-01 18:29:47][INFO] Registering route: GET /api/auth/captcha
[2025-08-01 18:29:47][INFO] Registering route: GET /api/auth/validate
[2025-08-01 18:29:47][INFO] Registering route: GET /api/users
[2025-08-01 18:29:47][INFO] Registering route: POST /api/users
[2025-08-01 18:29:47][INFO] Registering route: GET /api/users/profile
[2025-08-01 18:29:47][INFO] Registering route: GET /api/users/:id
[2025-08-01 18:29:47][INFO] Registering route: PUT /api/users/:id
[2025-08-01 18:29:47][INFO] Registering route: DELETE /api/users/:id
[2025-08-01 18:29:47][INFO] Registering route: GET /api/stats
[2025-08-01 18:29:47][INFO] Registering route: GET /api/stats/:type
[2025-08-01 18:29:47][INFO] Dispatching request: GET /api/auth/captcha
[2025-08-01 18:29:47][INFO] 数据库连接成功: 127.0.0.1:3306/plb_kj
[2025-08-01 18:29:47][INFO] Request: GET /api/auth/captcha
[2025-08-01 18:29:47][INFO] Registering route: POST /api/admin/login
[2025-08-01 18:29:47][INFO] Registering route: POST /api/users/login
[2025-08-01 18:29:47][INFO] Registering route: GET /api/auth/captcha
[2025-08-01 18:29:47][INFO] Registering route: GET /api/auth/validate
[2025-08-01 18:29:47][INFO] Registering route: GET /api/users
[2025-08-01 18:29:47][INFO] Registering route: POST /api/users
[2025-08-01 18:29:47][INFO] Registering route: GET /api/users/profile
[2025-08-01 18:29:47][INFO] Registering route: GET /api/users/:id
[2025-08-01 18:29:47][INFO] Registering route: PUT /api/users/:id
[2025-08-01 18:29:47][INFO] Registering route: DELETE /api/users/:id
[2025-08-01 18:29:47][INFO] Registering route: GET /api/stats
[2025-08-01 18:29:47][INFO] Registering route: GET /api/stats/:type
[2025-08-01 18:29:47][INFO] Dispatching request: GET /api/auth/captcha
[2025-08-01 18:29:47][INFO] 数据库连接成功: 127.0.0.1:3306/plb_kj
[2025-08-01 18:50:55][INFO] Request: GET /api/auth/captcha
[2025-08-01 18:50:55][INFO] Registering route: POST /api/admin/login
[2025-08-01 18:50:55][INFO] Registering route: POST /api/users/login
[2025-08-01 18:50:55][INFO] Registering route: GET /api/auth/captcha
[2025-08-01 18:50:55][INFO] Registering route: GET /api/auth/validate
[2025-08-01 18:50:55][INFO] Registering route: GET /api/users
[2025-08-01 18:50:55][INFO] Registering route: POST /api/users
[2025-08-01 18:50:55][INFO] Registering route: GET /api/users/profile
[2025-08-01 18:50:55][INFO] Registering route: GET /api/users/:id
[2025-08-01 18:50:55][INFO] Registering route: PUT /api/users/:id
[2025-08-01 18:50:55][INFO] Registering route: DELETE /api/users/:id
[2025-08-01 18:50:55][INFO] Registering route: GET /api/stats
[2025-08-01 18:50:55][INFO] Registering route: GET /api/stats/:type
[2025-08-01 18:50:55][INFO] Dispatching request: GET /api/auth/captcha
[2025-08-01 18:50:55][INFO] 数据库连接成功: 127.0.0.1:3306/plb_kj
[2025-08-01 18:50:55][INFO] Request: GET /api/auth/captcha
[2025-08-01 18:50:55][INFO] Registering route: POST /api/admin/login
[2025-08-01 18:50:55][INFO] Registering route: POST /api/users/login
[2025-08-01 18:50:55][INFO] Registering route: GET /api/auth/captcha
[2025-08-01 18:50:55][INFO] Registering route: GET /api/auth/validate
[2025-08-01 18:50:55][INFO] Registering route: GET /api/users
[2025-08-01 18:50:55][INFO] Registering route: POST /api/users
[2025-08-01 18:50:55][INFO] Registering route: GET /api/users/profile
[2025-08-01 18:50:55][INFO] Registering route: GET /api/users/:id
[2025-08-01 18:50:55][INFO] Registering route: PUT /api/users/:id
[2025-08-01 18:50:55][INFO] Registering route: DELETE /api/users/:id
[2025-08-01 18:50:55][INFO] Registering route: GET /api/stats
[2025-08-01 18:50:55][INFO] Registering route: GET /api/stats/:type
[2025-08-01 18:50:55][INFO] Dispatching request: GET /api/auth/captcha
[2025-08-01 18:50:55][INFO] 数据库连接成功: 127.0.0.1:3306/plb_kj
[2025-08-01 18:50:57][INFO] Request: GET /api/auth/validate
[2025-08-01 18:50:57][INFO] Registering route: POST /api/admin/login
[2025-08-01 18:50:57][INFO] Registering route: POST /api/users/login
[2025-08-01 18:50:57][INFO] Registering route: GET /api/auth/captcha
[2025-08-01 18:50:57][INFO] Registering route: GET /api/auth/validate
[2025-08-01 18:50:57][INFO] Registering route: GET /api/users
[2025-08-01 18:50:57][INFO] Registering route: POST /api/users
[2025-08-01 18:50:57][INFO] Registering route: GET /api/users/profile
[2025-08-01 18:50:57][INFO] Registering route: GET /api/users/:id
[2025-08-01 18:50:57][INFO] Registering route: PUT /api/users/:id
[2025-08-01 18:50:57][INFO] Registering route: DELETE /api/users/:id
[2025-08-01 18:50:57][INFO] Registering route: GET /api/stats
[2025-08-01 18:50:57][INFO] Registering route: GET /api/stats/:type
[2025-08-01 18:50:57][INFO] Dispatching request: GET /api/auth/validate
[2025-08-01 18:50:57][INFO] 数据库连接成功: 127.0.0.1:3306/plb_kj
[2025-08-01 18:50:57][ERROR] Token validation error: 无效的认证令牌
[2025-08-01 18:50:57][INFO] Request: GET /api/auth/captcha
[2025-08-01 18:50:57][INFO] Registering route: POST /api/admin/login
[2025-08-01 18:50:57][INFO] Registering route: POST /api/users/login
[2025-08-01 18:50:57][INFO] Registering route: GET /api/auth/captcha
[2025-08-01 18:50:57][INFO] Registering route: GET /api/auth/validate
[2025-08-01 18:50:57][INFO] Registering route: GET /api/users
[2025-08-01 18:50:57][INFO] Registering route: POST /api/users
[2025-08-01 18:50:57][INFO] Registering route: GET /api/users/profile
[2025-08-01 18:50:57][INFO] Registering route: GET /api/users/:id
[2025-08-01 18:50:57][INFO] Registering route: PUT /api/users/:id
[2025-08-01 18:50:57][INFO] Registering route: DELETE /api/users/:id
[2025-08-01 18:50:57][INFO] Registering route: GET /api/stats
[2025-08-01 18:50:57][INFO] Registering route: GET /api/stats/:type
[2025-08-01 18:50:57][INFO] Dispatching request: GET /api/auth/captcha
[2025-08-01 18:50:57][INFO] 数据库连接成功: 127.0.0.1:3306/plb_kj
[2025-08-01 18:50:57][INFO] Request: GET /api/auth/captcha
[2025-08-01 18:50:57][INFO] Registering route: POST /api/admin/login
[2025-08-01 18:50:57][INFO] Registering route: POST /api/users/login
[2025-08-01 18:50:57][INFO] Registering route: GET /api/auth/captcha
[2025-08-01 18:50:57][INFO] Registering route: GET /api/auth/validate
[2025-08-01 18:50:57][INFO] Registering route: GET /api/users
[2025-08-01 18:50:57][INFO] Registering route: POST /api/users
[2025-08-01 18:50:57][INFO] Registering route: GET /api/users/profile
[2025-08-01 18:50:57][INFO] Registering route: GET /api/users/:id
[2025-08-01 18:50:57][INFO] Registering route: PUT /api/users/:id
[2025-08-01 18:50:57][INFO] Registering route: DELETE /api/users/:id
[2025-08-01 18:50:57][INFO] Registering route: GET /api/stats
[2025-08-01 18:50:57][INFO] Registering route: GET /api/stats/:type
[2025-08-01 18:50:57][INFO] Dispatching request: GET /api/auth/captcha
[2025-08-01 18:50:57][INFO] 数据库连接成功: 127.0.0.1:3306/plb_kj
[2025-08-01 19:07:03][INFO] Request: GET /api/auth/captcha
[2025-08-01 19:07:03][INFO] Registering route: POST /api/admin/login
[2025-08-01 19:07:03][INFO] Registering route: POST /api/users/login
[2025-08-01 19:07:03][INFO] Registering route: GET /api/auth/captcha
[2025-08-01 19:07:03][INFO] Registering route: GET /api/auth/validate
[2025-08-01 19:07:03][INFO] Registering route: GET /api/users
[2025-08-01 19:07:03][INFO] Registering route: POST /api/users
[2025-08-01 19:07:03][INFO] Registering route: GET /api/users/profile
[2025-08-01 19:07:03][INFO] Registering route: GET /api/users/:id
[2025-08-01 19:07:03][INFO] Registering route: PUT /api/users/:id
[2025-08-01 19:07:03][INFO] Registering route: DELETE /api/users/:id
[2025-08-01 19:07:03][INFO] Registering route: GET /api/stats
[2025-08-01 19:07:03][INFO] Registering route: GET /api/stats/:type
[2025-08-01 19:07:03][INFO] Dispatching request: GET /api/auth/captcha
[2025-08-01 19:07:03][INFO] 数据库连接成功: 127.0.0.1:3306/plb_kj
[2025-08-01 19:07:03][INFO] Request: GET /api/auth/captcha
[2025-08-01 19:07:03][INFO] Registering route: POST /api/admin/login
[2025-08-01 19:07:03][INFO] Registering route: POST /api/users/login
[2025-08-01 19:07:03][INFO] Registering route: GET /api/auth/captcha
[2025-08-01 19:07:03][INFO] Registering route: GET /api/auth/validate
[2025-08-01 19:07:03][INFO] Registering route: GET /api/users
[2025-08-01 19:07:03][INFO] Registering route: POST /api/users
[2025-08-01 19:07:03][INFO] Registering route: GET /api/users/profile
[2025-08-01 19:07:03][INFO] Registering route: GET /api/users/:id
[2025-08-01 19:07:03][INFO] Registering route: PUT /api/users/:id
[2025-08-01 19:07:03][INFO] Registering route: DELETE /api/users/:id
[2025-08-01 19:07:03][INFO] Registering route: GET /api/stats
[2025-08-01 19:07:03][INFO] Registering route: GET /api/stats/:type
[2025-08-01 19:07:03][INFO] Dispatching request: GET /api/auth/captcha
[2025-08-01 19:07:03][INFO] 数据库连接成功: 127.0.0.1:3306/plb_kj
[2025-08-01 19:07:05][INFO] Request: GET /api/auth/validate
[2025-08-01 19:07:05][INFO] Registering route: POST /api/admin/login
[2025-08-01 19:07:05][INFO] Registering route: POST /api/users/login
[2025-08-01 19:07:05][INFO] Registering route: GET /api/auth/captcha
[2025-08-01 19:07:05][INFO] Registering route: GET /api/auth/validate
[2025-08-01 19:07:05][INFO] Registering route: GET /api/users
[2025-08-01 19:07:05][INFO] Registering route: POST /api/users
[2025-08-01 19:07:05][INFO] Registering route: GET /api/users/profile
[2025-08-01 19:07:05][INFO] Registering route: GET /api/users/:id
[2025-08-01 19:07:05][INFO] Registering route: PUT /api/users/:id
[2025-08-01 19:07:05][INFO] Registering route: DELETE /api/users/:id
[2025-08-01 19:07:05][INFO] Registering route: GET /api/stats
[2025-08-01 19:07:05][INFO] Registering route: GET /api/stats/:type
[2025-08-01 19:07:05][INFO] Dispatching request: GET /api/auth/validate
[2025-08-01 19:07:05][INFO] 数据库连接成功: 127.0.0.1:3306/plb_kj
[2025-08-01 19:07:05][ERROR] Token validation error: 无效的认证令牌
[2025-08-01 19:07:05][INFO] Request: GET /api/auth/captcha
[2025-08-01 19:07:05][INFO] Registering route: POST /api/admin/login
[2025-08-01 19:07:05][INFO] Registering route: POST /api/users/login
[2025-08-01 19:07:05][INFO] Registering route: GET /api/auth/captcha
[2025-08-01 19:07:05][INFO] Registering route: GET /api/auth/validate
[2025-08-01 19:07:05][INFO] Registering route: GET /api/users
[2025-08-01 19:07:05][INFO] Registering route: POST /api/users
[2025-08-01 19:07:05][INFO] Registering route: GET /api/users/profile
[2025-08-01 19:07:05][INFO] Registering route: GET /api/users/:id
[2025-08-01 19:07:05][INFO] Registering route: PUT /api/users/:id
[2025-08-01 19:07:05][INFO] Registering route: DELETE /api/users/:id
[2025-08-01 19:07:05][INFO] Registering route: GET /api/stats
[2025-08-01 19:07:05][INFO] Registering route: GET /api/stats/:type
[2025-08-01 19:07:05][INFO] Dispatching request: GET /api/auth/captcha
[2025-08-01 19:07:05][INFO] 数据库连接成功: 127.0.0.1:3306/plb_kj
[2025-08-01 19:07:05][INFO] Request: GET /api/auth/captcha
[2025-08-01 19:07:05][INFO] Registering route: POST /api/admin/login
[2025-08-01 19:07:05][INFO] Registering route: POST /api/users/login
[2025-08-01 19:07:05][INFO] Registering route: GET /api/auth/captcha
[2025-08-01 19:07:05][INFO] Registering route: GET /api/auth/validate
[2025-08-01 19:07:05][INFO] Registering route: GET /api/users
[2025-08-01 19:07:05][INFO] Registering route: POST /api/users
[2025-08-01 19:07:05][INFO] Registering route: GET /api/users/profile
[2025-08-01 19:07:05][INFO] Registering route: GET /api/users/:id
[2025-08-01 19:07:05][INFO] Registering route: PUT /api/users/:id
[2025-08-01 19:07:05][INFO] Registering route: DELETE /api/users/:id
[2025-08-01 19:07:05][INFO] Registering route: GET /api/stats
[2025-08-01 19:07:05][INFO] Registering route: GET /api/stats/:type
[2025-08-01 19:07:05][INFO] Dispatching request: GET /api/auth/captcha
[2025-08-01 19:07:05][INFO] 数据库连接成功: 127.0.0.1:3306/plb_kj
[2025-08-01 19:07:17][INFO] Request: GET /api/auth/captcha
[2025-08-01 19:07:17][INFO] Registering route: POST /api/admin/login
[2025-08-01 19:07:17][INFO] Registering route: POST /api/users/login
[2025-08-01 19:07:17][INFO] Registering route: GET /api/auth/captcha
[2025-08-01 19:07:17][INFO] Registering route: GET /api/auth/validate
[2025-08-01 19:07:17][INFO] Registering route: GET /api/users
[2025-08-01 19:07:17][INFO] Registering route: POST /api/users
[2025-08-01 19:07:17][INFO] Registering route: GET /api/users/profile
[2025-08-01 19:07:17][INFO] Registering route: GET /api/users/:id
[2025-08-01 19:07:17][INFO] Registering route: PUT /api/users/:id
[2025-08-01 19:07:17][INFO] Registering route: DELETE /api/users/:id
[2025-08-01 19:07:17][INFO] Registering route: GET /api/stats
[2025-08-01 19:07:17][INFO] Registering route: GET /api/stats/:type
[2025-08-01 19:07:17][INFO] Dispatching request: GET /api/auth/captcha
[2025-08-01 19:07:17][INFO] 数据库连接成功: 127.0.0.1:3306/plb_kj
[2025-08-01 19:07:17][INFO] Request: GET /api/auth/captcha
[2025-08-01 19:07:17][INFO] Registering route: POST /api/admin/login
[2025-08-01 19:07:17][INFO] Registering route: POST /api/users/login
[2025-08-01 19:07:17][INFO] Registering route: GET /api/auth/captcha
[2025-08-01 19:07:17][INFO] Registering route: GET /api/auth/validate
[2025-08-01 19:07:17][INFO] Registering route: GET /api/users
[2025-08-01 19:07:17][INFO] Registering route: POST /api/users
[2025-08-01 19:07:17][INFO] Registering route: GET /api/users/profile
[2025-08-01 19:07:17][INFO] Registering route: GET /api/users/:id
[2025-08-01 19:07:17][INFO] Registering route: PUT /api/users/:id
[2025-08-01 19:07:17][INFO] Registering route: DELETE /api/users/:id
[2025-08-01 19:07:17][INFO] Registering route: GET /api/stats
[2025-08-01 19:07:17][INFO] Registering route: GET /api/stats/:type
[2025-08-01 19:07:17][INFO] Dispatching request: GET /api/auth/captcha
[2025-08-01 19:07:17][INFO] 数据库连接成功: 127.0.0.1:3306/plb_kj
[2025-08-01 19:07:19][INFO] Request: GET /api/auth/validate
[2025-08-01 19:07:19][INFO] Registering route: POST /api/admin/login
[2025-08-01 19:07:19][INFO] Registering route: POST /api/users/login
[2025-08-01 19:07:19][INFO] Registering route: GET /api/auth/captcha
[2025-08-01 19:07:19][INFO] Registering route: GET /api/auth/validate
[2025-08-01 19:07:19][INFO] Registering route: GET /api/users
[2025-08-01 19:07:19][INFO] Registering route: POST /api/users
[2025-08-01 19:07:19][INFO] Registering route: GET /api/users/profile
[2025-08-01 19:07:19][INFO] Registering route: GET /api/users/:id
[2025-08-01 19:07:19][INFO] Registering route: PUT /api/users/:id
[2025-08-01 19:07:19][INFO] Registering route: DELETE /api/users/:id
[2025-08-01 19:07:19][INFO] Registering route: GET /api/stats
[2025-08-01 19:07:19][INFO] Registering route: GET /api/stats/:type
[2025-08-01 19:07:19][INFO] Dispatching request: GET /api/auth/validate
[2025-08-01 19:07:19][INFO] 数据库连接成功: 127.0.0.1:3306/plb_kj
[2025-08-01 19:07:19][ERROR] Token validation error: 无效的认证令牌
[2025-08-01 19:07:19][INFO] Request: GET /api/auth/captcha
[2025-08-01 19:07:19][INFO] Registering route: POST /api/admin/login
[2025-08-01 19:07:19][INFO] Registering route: POST /api/users/login
[2025-08-01 19:07:19][INFO] Registering route: GET /api/auth/captcha
[2025-08-01 19:07:19][INFO] Registering route: GET /api/auth/validate
[2025-08-01 19:07:19][INFO] Registering route: GET /api/users
[2025-08-01 19:07:19][INFO] Registering route: POST /api/users
[2025-08-01 19:07:19][INFO] Registering route: GET /api/users/profile
[2025-08-01 19:07:19][INFO] Registering route: GET /api/users/:id
[2025-08-01 19:07:19][INFO] Registering route: PUT /api/users/:id
[2025-08-01 19:07:19][INFO] Registering route: DELETE /api/users/:id
[2025-08-01 19:07:19][INFO] Registering route: GET /api/stats
[2025-08-01 19:07:19][INFO] Registering route: GET /api/stats/:type
[2025-08-01 19:07:19][INFO] Dispatching request: GET /api/auth/captcha
[2025-08-01 19:07:19][INFO] 数据库连接成功: 127.0.0.1:3306/plb_kj
[2025-08-01 19:07:19][INFO] Request: GET /api/auth/captcha
[2025-08-01 19:07:19][INFO] Registering route: POST /api/admin/login
[2025-08-01 19:07:19][INFO] Registering route: POST /api/users/login
[2025-08-01 19:07:19][INFO] Registering route: GET /api/auth/captcha
[2025-08-01 19:07:19][INFO] Registering route: GET /api/auth/validate
[2025-08-01 19:07:19][INFO] Registering route: GET /api/users
[2025-08-01 19:07:19][INFO] Registering route: POST /api/users
[2025-08-01 19:07:19][INFO] Registering route: GET /api/users/profile
[2025-08-01 19:07:19][INFO] Registering route: GET /api/users/:id
[2025-08-01 19:07:19][INFO] Registering route: PUT /api/users/:id
[2025-08-01 19:07:19][INFO] Registering route: DELETE /api/users/:id
[2025-08-01 19:07:19][INFO] Registering route: GET /api/stats
[2025-08-01 19:07:19][INFO] Registering route: GET /api/stats/:type
[2025-08-01 19:07:19][INFO] Dispatching request: GET /api/auth/captcha
[2025-08-01 19:07:19][INFO] 数据库连接成功: 127.0.0.1:3306/plb_kj
[2025-08-01 19:40:11][INFO] Request: GET /api/auth/captcha
[2025-08-01 19:40:11][INFO] Registering route: POST /api/admin/login
[2025-08-01 19:40:11][INFO] Registering route: POST /api/users/login
[2025-08-01 19:40:11][INFO] Registering route: GET /api/auth/captcha
[2025-08-01 19:40:11][INFO] Registering route: GET /api/auth/validate
[2025-08-01 19:40:11][INFO] Registering route: GET /api/users
[2025-08-01 19:40:11][INFO] Registering route: POST /api/users
[2025-08-01 19:40:11][INFO] Registering route: GET /api/users/profile
[2025-08-01 19:40:11][INFO] Registering route: GET /api/users/:id
[2025-08-01 19:40:11][INFO] Registering route: PUT /api/users/:id
[2025-08-01 19:40:11][INFO] Registering route: DELETE /api/users/:id
[2025-08-01 19:40:11][INFO] Registering route: GET /api/stats
[2025-08-01 19:40:11][INFO] Registering route: GET /api/stats/:type
[2025-08-01 19:40:11][INFO] Dispatching request: GET /api/auth/captcha
[2025-08-01 19:40:11][INFO] 数据库连接成功: 127.0.0.1:3306/plb_kj
[2025-08-01 19:40:11][INFO] Request: GET /api/auth/captcha
[2025-08-01 19:40:11][INFO] Registering route: POST /api/admin/login
[2025-08-01 19:40:11][INFO] Registering route: POST /api/users/login
[2025-08-01 19:40:11][INFO] Registering route: GET /api/auth/captcha
[2025-08-01 19:40:11][INFO] Registering route: GET /api/auth/validate
[2025-08-01 19:40:11][INFO] Registering route: GET /api/users
[2025-08-01 19:40:11][INFO] Registering route: POST /api/users
[2025-08-01 19:40:11][INFO] Registering route: GET /api/users/profile
[2025-08-01 19:40:11][INFO] Registering route: GET /api/users/:id
[2025-08-01 19:40:11][INFO] Registering route: PUT /api/users/:id
[2025-08-01 19:40:11][INFO] Registering route: DELETE /api/users/:id
[2025-08-01 19:40:11][INFO] Registering route: GET /api/stats
[2025-08-01 19:40:11][INFO] Registering route: GET /api/stats/:type
[2025-08-01 19:40:11][INFO] Dispatching request: GET /api/auth/captcha
[2025-08-01 19:40:11][INFO] 数据库连接成功: 127.0.0.1:3306/plb_kj
[2025-08-01 19:40:13][INFO] Request: GET /api/auth/validate
[2025-08-01 19:40:13][INFO] Registering route: POST /api/admin/login
[2025-08-01 19:40:13][INFO] Registering route: POST /api/users/login
[2025-08-01 19:40:13][INFO] Registering route: GET /api/auth/captcha
[2025-08-01 19:40:13][INFO] Registering route: GET /api/auth/validate
[2025-08-01 19:40:13][INFO] Registering route: GET /api/users
[2025-08-01 19:40:13][INFO] Registering route: POST /api/users
[2025-08-01 19:40:13][INFO] Registering route: GET /api/users/profile
[2025-08-01 19:40:13][INFO] Registering route: GET /api/users/:id
[2025-08-01 19:40:13][INFO] Registering route: PUT /api/users/:id
[2025-08-01 19:40:13][INFO] Registering route: DELETE /api/users/:id
[2025-08-01 19:40:13][INFO] Registering route: GET /api/stats
[2025-08-01 19:40:13][INFO] Registering route: GET /api/stats/:type
[2025-08-01 19:40:13][INFO] Dispatching request: GET /api/auth/validate
[2025-08-01 19:40:13][INFO] 数据库连接成功: 127.0.0.1:3306/plb_kj
[2025-08-01 19:40:13][ERROR] Token validation error: 无效的认证令牌
[2025-08-01 19:40:13][INFO] Request: GET /api/auth/captcha
[2025-08-01 19:40:13][INFO] Registering route: POST /api/admin/login
[2025-08-01 19:40:13][INFO] Registering route: POST /api/users/login
[2025-08-01 19:40:13][INFO] Registering route: GET /api/auth/captcha
[2025-08-01 19:40:13][INFO] Registering route: GET /api/auth/validate
[2025-08-01 19:40:13][INFO] Registering route: GET /api/users
[2025-08-01 19:40:13][INFO] Registering route: POST /api/users
[2025-08-01 19:40:13][INFO] Registering route: GET /api/users/profile
[2025-08-01 19:40:13][INFO] Registering route: GET /api/users/:id
[2025-08-01 19:40:13][INFO] Registering route: PUT /api/users/:id
[2025-08-01 19:40:13][INFO] Registering route: DELETE /api/users/:id
[2025-08-01 19:40:13][INFO] Registering route: GET /api/stats
[2025-08-01 19:40:13][INFO] Registering route: GET /api/stats/:type
[2025-08-01 19:40:13][INFO] Dispatching request: GET /api/auth/captcha
[2025-08-01 19:40:13][INFO] 数据库连接成功: 127.0.0.1:3306/plb_kj
[2025-08-01 19:40:13][INFO] Request: GET /api/auth/captcha
[2025-08-01 19:40:13][INFO] Registering route: POST /api/admin/login
[2025-08-01 19:40:13][INFO] Registering route: POST /api/users/login
[2025-08-01 19:40:13][INFO] Registering route: GET /api/auth/captcha
[2025-08-01 19:40:13][INFO] Registering route: GET /api/auth/validate
[2025-08-01 19:40:13][INFO] Registering route: GET /api/users
[2025-08-01 19:40:13][INFO] Registering route: POST /api/users
[2025-08-01 19:40:13][INFO] Registering route: GET /api/users/profile
[2025-08-01 19:40:13][INFO] Registering route: GET /api/users/:id
[2025-08-01 19:40:13][INFO] Registering route: PUT /api/users/:id
[2025-08-01 19:40:13][INFO] Registering route: DELETE /api/users/:id
[2025-08-01 19:40:13][INFO] Registering route: GET /api/stats
[2025-08-01 19:40:13][INFO] Registering route: GET /api/stats/:type
[2025-08-01 19:40:13][INFO] Dispatching request: GET /api/auth/captcha
[2025-08-01 19:40:13][INFO] 数据库连接成功: 127.0.0.1:3306/plb_kj
[2025-08-01 19:55:27][INFO] Request: GET /api/auth/captcha
[2025-08-01 19:55:27][INFO] Registering route: POST /api/admin/login
[2025-08-01 19:55:27][INFO] Registering route: POST /api/users/login
[2025-08-01 19:55:27][INFO] Registering route: GET /api/auth/captcha
[2025-08-01 19:55:27][INFO] Registering route: GET /api/auth/validate
[2025-08-01 19:55:27][INFO] Registering route: GET /api/users
[2025-08-01 19:55:27][INFO] Registering route: POST /api/users
[2025-08-01 19:55:27][INFO] Registering route: GET /api/users/profile
[2025-08-01 19:55:27][INFO] Registering route: GET /api/users/:id
[2025-08-01 19:55:27][INFO] Registering route: PUT /api/users/:id
[2025-08-01 19:55:27][INFO] Registering route: DELETE /api/users/:id
[2025-08-01 19:55:27][INFO] Registering route: GET /api/stats
[2025-08-01 19:55:27][INFO] Registering route: GET /api/stats/:type
[2025-08-01 19:55:27][INFO] Dispatching request: GET /api/auth/captcha
[2025-08-01 19:55:27][INFO] 数据库连接成功: 127.0.0.1:3306/plb_kj
[2025-08-01 19:55:27][INFO] Request: GET /api/auth/captcha
[2025-08-01 19:55:27][INFO] Registering route: POST /api/admin/login
[2025-08-01 19:55:27][INFO] Registering route: POST /api/users/login
[2025-08-01 19:55:27][INFO] Registering route: GET /api/auth/captcha
[2025-08-01 19:55:27][INFO] Registering route: GET /api/auth/validate
[2025-08-01 19:55:27][INFO] Registering route: GET /api/users
[2025-08-01 19:55:27][INFO] Registering route: POST /api/users
[2025-08-01 19:55:27][INFO] Registering route: GET /api/users/profile
[2025-08-01 19:55:27][INFO] Registering route: GET /api/users/:id
[2025-08-01 19:55:27][INFO] Registering route: PUT /api/users/:id
[2025-08-01 19:55:27][INFO] Registering route: DELETE /api/users/:id
[2025-08-01 19:55:27][INFO] Registering route: GET /api/stats
[2025-08-01 19:55:27][INFO] Registering route: GET /api/stats/:type
[2025-08-01 19:55:27][INFO] Dispatching request: GET /api/auth/captcha
[2025-08-01 19:55:27][INFO] 数据库连接成功: 127.0.0.1:3306/plb_kj
[2025-08-01 19:55:29][INFO] Request: GET /api/auth/validate
[2025-08-01 19:55:29][INFO] Registering route: POST /api/admin/login
[2025-08-01 19:55:29][INFO] Registering route: POST /api/users/login
[2025-08-01 19:55:29][INFO] Registering route: GET /api/auth/captcha
[2025-08-01 19:55:29][INFO] Registering route: GET /api/auth/validate
[2025-08-01 19:55:29][INFO] Registering route: GET /api/users
[2025-08-01 19:55:29][INFO] Registering route: POST /api/users
[2025-08-01 19:55:29][INFO] Registering route: GET /api/users/profile
[2025-08-01 19:55:29][INFO] Registering route: GET /api/users/:id
[2025-08-01 19:55:29][INFO] Registering route: PUT /api/users/:id
[2025-08-01 19:55:29][INFO] Registering route: DELETE /api/users/:id
[2025-08-01 19:55:29][INFO] Registering route: GET /api/stats
[2025-08-01 19:55:29][INFO] Registering route: GET /api/stats/:type
[2025-08-01 19:55:29][INFO] Dispatching request: GET /api/auth/validate
[2025-08-01 19:55:29][INFO] 数据库连接成功: 127.0.0.1:3306/plb_kj
[2025-08-01 19:55:29][ERROR] Token validation error: 无效的认证令牌
[2025-08-01 19:55:29][INFO] Request: GET /api/auth/captcha
[2025-08-01 19:55:29][INFO] Registering route: POST /api/admin/login
[2025-08-01 19:55:29][INFO] Registering route: POST /api/users/login
[2025-08-01 19:55:29][INFO] Registering route: GET /api/auth/captcha
[2025-08-01 19:55:29][INFO] Registering route: GET /api/auth/validate
[2025-08-01 19:55:29][INFO] Registering route: GET /api/users
[2025-08-01 19:55:29][INFO] Registering route: POST /api/users
[2025-08-01 19:55:29][INFO] Registering route: GET /api/users/profile
[2025-08-01 19:55:29][INFO] Registering route: GET /api/users/:id
[2025-08-01 19:55:29][INFO] Registering route: PUT /api/users/:id
[2025-08-01 19:55:29][INFO] Registering route: DELETE /api/users/:id
[2025-08-01 19:55:29][INFO] Registering route: GET /api/stats
[2025-08-01 19:55:29][INFO] Registering route: GET /api/stats/:type
[2025-08-01 19:55:29][INFO] Dispatching request: GET /api/auth/captcha
[2025-08-01 19:55:29][INFO] 数据库连接成功: 127.0.0.1:3306/plb_kj
[2025-08-01 19:55:29][INFO] Request: GET /api/auth/captcha
[2025-08-01 19:55:29][INFO] Registering route: POST /api/admin/login
[2025-08-01 19:55:29][INFO] Registering route: POST /api/users/login
[2025-08-01 19:55:29][INFO] Registering route: GET /api/auth/captcha
[2025-08-01 19:55:29][INFO] Registering route: GET /api/auth/validate
[2025-08-01 19:55:29][INFO] Registering route: GET /api/users
[2025-08-01 19:55:29][INFO] Registering route: POST /api/users
[2025-08-01 19:55:29][INFO] Registering route: GET /api/users/profile
[2025-08-01 19:55:29][INFO] Registering route: GET /api/users/:id
[2025-08-01 19:55:29][INFO] Registering route: PUT /api/users/:id
[2025-08-01 19:55:29][INFO] Registering route: DELETE /api/users/:id
[2025-08-01 19:55:29][INFO] Registering route: GET /api/stats
[2025-08-01 19:55:29][INFO] Registering route: GET /api/stats/:type
[2025-08-01 19:55:29][INFO] Dispatching request: GET /api/auth/captcha
[2025-08-01 19:55:29][INFO] 数据库连接成功: 127.0.0.1:3306/plb_kj
[2025-08-01 20:00:17][INFO] Request: GET /api/auth/captcha
[2025-08-01 20:00:17][INFO] Registering route: POST /api/admin/login
[2025-08-01 20:00:17][INFO] Registering route: POST /api/users/login
[2025-08-01 20:00:17][INFO] Registering route: GET /api/auth/captcha
[2025-08-01 20:00:17][INFO] Registering route: GET /api/auth/validate
[2025-08-01 20:00:17][INFO] Registering route: GET /api/users
[2025-08-01 20:00:17][INFO] Registering route: POST /api/users
[2025-08-01 20:00:17][INFO] Registering route: GET /api/users/profile
[2025-08-01 20:00:17][INFO] Registering route: GET /api/users/:id
[2025-08-01 20:00:17][INFO] Registering route: PUT /api/users/:id
[2025-08-01 20:00:17][INFO] Registering route: DELETE /api/users/:id
[2025-08-01 20:00:17][INFO] Registering route: GET /api/stats
[2025-08-01 20:00:17][INFO] Registering route: GET /api/stats/:type
[2025-08-01 20:00:17][INFO] Dispatching request: GET /api/auth/captcha
[2025-08-01 20:00:17][INFO] 数据库连接成功: 127.0.0.1:3306/plb_kj
[2025-08-01 20:00:17][INFO] Request: GET /api/auth/captcha
[2025-08-01 20:00:17][INFO] Registering route: POST /api/admin/login
[2025-08-01 20:00:17][INFO] Registering route: POST /api/users/login
[2025-08-01 20:00:17][INFO] Registering route: GET /api/auth/captcha
[2025-08-01 20:00:17][INFO] Registering route: GET /api/auth/validate
[2025-08-01 20:00:17][INFO] Registering route: GET /api/users
[2025-08-01 20:00:17][INFO] Registering route: POST /api/users
[2025-08-01 20:00:17][INFO] Registering route: GET /api/users/profile
[2025-08-01 20:00:17][INFO] Registering route: GET /api/users/:id
[2025-08-01 20:00:17][INFO] Registering route: PUT /api/users/:id
[2025-08-01 20:00:17][INFO] Registering route: DELETE /api/users/:id
[2025-08-01 20:00:17][INFO] Registering route: GET /api/stats
[2025-08-01 20:00:17][INFO] Registering route: GET /api/stats/:type
[2025-08-01 20:00:17][INFO] Dispatching request: GET /api/auth/captcha
[2025-08-01 20:00:17][INFO] 数据库连接成功: 127.0.0.1:3306/plb_kj
[2025-08-01 20:00:19][INFO] Request: GET /api/auth/validate
[2025-08-01 20:00:19][INFO] Registering route: POST /api/admin/login
[2025-08-01 20:00:19][INFO] Registering route: POST /api/users/login
[2025-08-01 20:00:19][INFO] Registering route: GET /api/auth/captcha
[2025-08-01 20:00:19][INFO] Registering route: GET /api/auth/validate
[2025-08-01 20:00:19][INFO] Registering route: GET /api/users
[2025-08-01 20:00:19][INFO] Registering route: POST /api/users
[2025-08-01 20:00:19][INFO] Registering route: GET /api/users/profile
[2025-08-01 20:00:19][INFO] Registering route: GET /api/users/:id
[2025-08-01 20:00:19][INFO] Registering route: PUT /api/users/:id
[2025-08-01 20:00:19][INFO] Registering route: DELETE /api/users/:id
[2025-08-01 20:00:19][INFO] Registering route: GET /api/stats
[2025-08-01 20:00:19][INFO] Registering route: GET /api/stats/:type
[2025-08-01 20:00:19][INFO] Dispatching request: GET /api/auth/validate
[2025-08-01 20:00:19][INFO] 数据库连接成功: 127.0.0.1:3306/plb_kj
[2025-08-01 20:00:19][ERROR] Token validation error: 无效的认证令牌
[2025-08-01 20:00:19][INFO] Request: GET /api/auth/captcha
[2025-08-01 20:00:19][INFO] Registering route: POST /api/admin/login
[2025-08-01 20:00:19][INFO] Registering route: POST /api/users/login
[2025-08-01 20:00:19][INFO] Registering route: GET /api/auth/captcha
[2025-08-01 20:00:19][INFO] Registering route: GET /api/auth/validate
[2025-08-01 20:00:19][INFO] Registering route: GET /api/users
[2025-08-01 20:00:19][INFO] Registering route: POST /api/users
[2025-08-01 20:00:19][INFO] Registering route: GET /api/users/profile
[2025-08-01 20:00:19][INFO] Registering route: GET /api/users/:id
[2025-08-01 20:00:19][INFO] Registering route: PUT /api/users/:id
[2025-08-01 20:00:19][INFO] Registering route: DELETE /api/users/:id
[2025-08-01 20:00:19][INFO] Registering route: GET /api/stats
[2025-08-01 20:00:19][INFO] Registering route: GET /api/stats/:type
[2025-08-01 20:00:19][INFO] Dispatching request: GET /api/auth/captcha
[2025-08-01 20:00:19][INFO] 数据库连接成功: 127.0.0.1:3306/plb_kj
[2025-08-01 20:00:19][INFO] Request: GET /api/auth/captcha
[2025-08-01 20:00:19][INFO] Registering route: POST /api/admin/login
[2025-08-01 20:00:19][INFO] Registering route: POST /api/users/login
[2025-08-01 20:00:19][INFO] Registering route: GET /api/auth/captcha
[2025-08-01 20:00:19][INFO] Registering route: GET /api/auth/validate
[2025-08-01 20:00:19][INFO] Registering route: GET /api/users
[2025-08-01 20:00:19][INFO] Registering route: POST /api/users
[2025-08-01 20:00:19][INFO] Registering route: GET /api/users/profile
[2025-08-01 20:00:19][INFO] Registering route: GET /api/users/:id
[2025-08-01 20:00:19][INFO] Registering route: PUT /api/users/:id
[2025-08-01 20:00:19][INFO] Registering route: DELETE /api/users/:id
[2025-08-01 20:00:19][INFO] Registering route: GET /api/stats
[2025-08-01 20:00:19][INFO] Registering route: GET /api/stats/:type
[2025-08-01 20:00:19][INFO] Dispatching request: GET /api/auth/captcha
[2025-08-01 20:00:19][INFO] 数据库连接成功: 127.0.0.1:3306/plb_kj
[2025-08-01 20:00:32][INFO] Request: GET /api/auth/captcha
[2025-08-01 20:00:32][INFO] Registering route: POST /api/admin/login
[2025-08-01 20:00:32][INFO] Registering route: POST /api/users/login
[2025-08-01 20:00:32][INFO] Registering route: GET /api/auth/captcha
[2025-08-01 20:00:32][INFO] Registering route: GET /api/auth/validate
[2025-08-01 20:00:32][INFO] Registering route: GET /api/users
[2025-08-01 20:00:32][INFO] Registering route: POST /api/users
[2025-08-01 20:00:32][INFO] Registering route: GET /api/users/profile
[2025-08-01 20:00:32][INFO] Registering route: GET /api/users/:id
[2025-08-01 20:00:32][INFO] Registering route: PUT /api/users/:id
[2025-08-01 20:00:32][INFO] Registering route: DELETE /api/users/:id
[2025-08-01 20:00:32][INFO] Registering route: GET /api/stats
[2025-08-01 20:00:32][INFO] Registering route: GET /api/stats/:type
[2025-08-01 20:00:32][INFO] Dispatching request: GET /api/auth/captcha
[2025-08-01 20:00:32][INFO] 数据库连接成功: 127.0.0.1:3306/plb_kj
[2025-08-01 20:00:32][INFO] Request: GET /api/auth/captcha
[2025-08-01 20:00:32][INFO] Registering route: POST /api/admin/login
[2025-08-01 20:00:32][INFO] Registering route: POST /api/users/login
[2025-08-01 20:00:32][INFO] Registering route: GET /api/auth/captcha
[2025-08-01 20:00:32][INFO] Registering route: GET /api/auth/validate
[2025-08-01 20:00:32][INFO] Registering route: GET /api/users
[2025-08-01 20:00:32][INFO] Registering route: POST /api/users
[2025-08-01 20:00:32][INFO] Registering route: GET /api/users/profile
[2025-08-01 20:00:32][INFO] Registering route: GET /api/users/:id
[2025-08-01 20:00:32][INFO] Registering route: PUT /api/users/:id
[2025-08-01 20:00:32][INFO] Registering route: DELETE /api/users/:id
[2025-08-01 20:00:32][INFO] Registering route: GET /api/stats
[2025-08-01 20:00:32][INFO] Registering route: GET /api/stats/:type
[2025-08-01 20:00:32][INFO] Dispatching request: GET /api/auth/captcha
[2025-08-01 20:00:32][INFO] 数据库连接成功: 127.0.0.1:3306/plb_kj
[2025-08-01 20:00:34][INFO] Request: GET /api/auth/validate
[2025-08-01 20:00:34][INFO] Registering route: POST /api/admin/login
[2025-08-01 20:00:34][INFO] Registering route: POST /api/users/login
[2025-08-01 20:00:34][INFO] Registering route: GET /api/auth/captcha
[2025-08-01 20:00:34][INFO] Registering route: GET /api/auth/validate
[2025-08-01 20:00:34][INFO] Registering route: GET /api/users
[2025-08-01 20:00:34][INFO] Registering route: POST /api/users
[2025-08-01 20:00:34][INFO] Registering route: GET /api/users/profile
[2025-08-01 20:00:34][INFO] Registering route: GET /api/users/:id
[2025-08-01 20:00:34][INFO] Registering route: PUT /api/users/:id
[2025-08-01 20:00:34][INFO] Registering route: DELETE /api/users/:id
[2025-08-01 20:00:34][INFO] Registering route: GET /api/stats
[2025-08-01 20:00:34][INFO] Registering route: GET /api/stats/:type
[2025-08-01 20:00:34][INFO] Dispatching request: GET /api/auth/validate
[2025-08-01 20:00:34][INFO] 数据库连接成功: 127.0.0.1:3306/plb_kj
[2025-08-01 20:00:34][ERROR] Token validation error: 无效的认证令牌
[2025-08-01 20:00:34][INFO] Request: GET /api/auth/captcha
[2025-08-01 20:00:34][INFO] Registering route: POST /api/admin/login
[2025-08-01 20:00:34][INFO] Registering route: POST /api/users/login
[2025-08-01 20:00:34][INFO] Registering route: GET /api/auth/captcha
[2025-08-01 20:00:34][INFO] Registering route: GET /api/auth/validate
[2025-08-01 20:00:34][INFO] Registering route: GET /api/users
[2025-08-01 20:00:34][INFO] Registering route: POST /api/users
[2025-08-01 20:00:34][INFO] Registering route: GET /api/users/profile
[2025-08-01 20:00:34][INFO] Registering route: GET /api/users/:id
[2025-08-01 20:00:34][INFO] Registering route: PUT /api/users/:id
[2025-08-01 20:00:34][INFO] Registering route: DELETE /api/users/:id
[2025-08-01 20:00:34][INFO] Registering route: GET /api/stats
[2025-08-01 20:00:34][INFO] Registering route: GET /api/stats/:type
[2025-08-01 20:00:34][INFO] Dispatching request: GET /api/auth/captcha
[2025-08-01 20:00:34][INFO] 数据库连接成功: 127.0.0.1:3306/plb_kj
[2025-08-01 20:00:34][INFO] Request: GET /api/auth/captcha
[2025-08-01 20:00:34][INFO] Registering route: POST /api/admin/login
[2025-08-01 20:00:34][INFO] Registering route: POST /api/users/login
[2025-08-01 20:00:34][INFO] Registering route: GET /api/auth/captcha
[2025-08-01 20:00:34][INFO] Registering route: GET /api/auth/validate
[2025-08-01 20:00:34][INFO] Registering route: GET /api/users
[2025-08-01 20:00:34][INFO] Registering route: POST /api/users
[2025-08-01 20:00:34][INFO] Registering route: GET /api/users/profile
[2025-08-01 20:00:34][INFO] Registering route: GET /api/users/:id
[2025-08-01 20:00:34][INFO] Registering route: PUT /api/users/:id
[2025-08-01 20:00:34][INFO] Registering route: DELETE /api/users/:id
[2025-08-01 20:00:34][INFO] Registering route: GET /api/stats
[2025-08-01 20:00:34][INFO] Registering route: GET /api/stats/:type
[2025-08-01 20:00:34][INFO] Dispatching request: GET /api/auth/captcha
[2025-08-01 20:00:34][INFO] 数据库连接成功: 127.0.0.1:3306/plb_kj
[2025-08-01 20:31:01][INFO] Request: GET /api/auth/captcha
[2025-08-01 20:31:01][INFO] Registering route: POST /api/admin/login
[2025-08-01 20:31:01][INFO] Registering route: POST /api/users/login
[2025-08-01 20:31:01][INFO] Registering route: GET /api/auth/captcha
[2025-08-01 20:31:01][INFO] Registering route: GET /api/auth/validate
[2025-08-01 20:31:01][INFO] Registering route: GET /api/users
[2025-08-01 20:31:01][INFO] Registering route: POST /api/users
[2025-08-01 20:31:01][INFO] Registering route: GET /api/users/profile
[2025-08-01 20:31:01][INFO] Registering route: GET /api/users/:id
[2025-08-01 20:31:01][INFO] Registering route: PUT /api/users/:id
[2025-08-01 20:31:01][INFO] Registering route: DELETE /api/users/:id
[2025-08-01 20:31:01][INFO] Registering route: GET /api/stats
[2025-08-01 20:31:01][INFO] Registering route: GET /api/stats/:type
[2025-08-01 20:31:01][INFO] Dispatching request: GET /api/auth/captcha
[2025-08-01 20:31:01][INFO] 数据库连接成功: 127.0.0.1:3306/plb_kj
[2025-08-01 20:31:01][INFO] Request: GET /api/auth/captcha
[2025-08-01 20:31:01][INFO] Registering route: POST /api/admin/login
[2025-08-01 20:31:01][INFO] Registering route: POST /api/users/login
[2025-08-01 20:31:01][INFO] Registering route: GET /api/auth/captcha
[2025-08-01 20:31:01][INFO] Registering route: GET /api/auth/validate
[2025-08-01 20:31:01][INFO] Registering route: GET /api/users
[2025-08-01 20:31:01][INFO] Registering route: POST /api/users
[2025-08-01 20:31:01][INFO] Registering route: GET /api/users/profile
[2025-08-01 20:31:01][INFO] Registering route: GET /api/users/:id
[2025-08-01 20:31:01][INFO] Registering route: PUT /api/users/:id
[2025-08-01 20:31:01][INFO] Registering route: DELETE /api/users/:id
[2025-08-01 20:31:01][INFO] Registering route: GET /api/stats
[2025-08-01 20:31:01][INFO] Registering route: GET /api/stats/:type
[2025-08-01 20:31:01][INFO] Dispatching request: GET /api/auth/captcha
[2025-08-01 20:31:01][INFO] 数据库连接成功: 127.0.0.1:3306/plb_kj
[2025-08-01 20:31:03][INFO] Request: GET /api/auth/validate
[2025-08-01 20:31:03][INFO] Registering route: POST /api/admin/login
[2025-08-01 20:31:03][INFO] Registering route: POST /api/users/login
[2025-08-01 20:31:03][INFO] Registering route: GET /api/auth/captcha
[2025-08-01 20:31:03][INFO] Registering route: GET /api/auth/validate
[2025-08-01 20:31:03][INFO] Registering route: GET /api/users
[2025-08-01 20:31:03][INFO] Registering route: POST /api/users
[2025-08-01 20:31:03][INFO] Registering route: GET /api/users/profile
[2025-08-01 20:31:03][INFO] Registering route: GET /api/users/:id
[2025-08-01 20:31:03][INFO] Registering route: PUT /api/users/:id
[2025-08-01 20:31:03][INFO] Registering route: DELETE /api/users/:id
[2025-08-01 20:31:03][INFO] Registering route: GET /api/stats
[2025-08-01 20:31:03][INFO] Registering route: GET /api/stats/:type
[2025-08-01 20:31:03][INFO] Dispatching request: GET /api/auth/validate
[2025-08-01 20:31:03][INFO] 数据库连接成功: 127.0.0.1:3306/plb_kj
[2025-08-01 20:31:03][ERROR] Token validation error: 无效的认证令牌
[2025-08-01 20:31:03][INFO] Request: GET /api/auth/captcha
[2025-08-01 20:31:03][INFO] Registering route: POST /api/admin/login
[2025-08-01 20:31:03][INFO] Registering route: POST /api/users/login
[2025-08-01 20:31:03][INFO] Registering route: GET /api/auth/captcha
[2025-08-01 20:31:03][INFO] Registering route: GET /api/auth/validate
[2025-08-01 20:31:03][INFO] Registering route: GET /api/users
[2025-08-01 20:31:03][INFO] Registering route: POST /api/users
[2025-08-01 20:31:03][INFO] Registering route: GET /api/users/profile
[2025-08-01 20:31:03][INFO] Registering route: GET /api/users/:id
[2025-08-01 20:31:03][INFO] Registering route: PUT /api/users/:id
[2025-08-01 20:31:03][INFO] Registering route: DELETE /api/users/:id
[2025-08-01 20:31:03][INFO] Registering route: GET /api/stats
[2025-08-01 20:31:03][INFO] Registering route: GET /api/stats/:type
[2025-08-01 20:31:03][INFO] Dispatching request: GET /api/auth/captcha
[2025-08-01 20:31:03][INFO] 数据库连接成功: 127.0.0.1:3306/plb_kj
[2025-08-01 20:31:03][INFO] Request: GET /api/auth/captcha
[2025-08-01 20:31:03][INFO] Registering route: POST /api/admin/login
[2025-08-01 20:31:03][INFO] Registering route: POST /api/users/login
[2025-08-01 20:31:03][INFO] Registering route: GET /api/auth/captcha
[2025-08-01 20:31:03][INFO] Registering route: GET /api/auth/validate
[2025-08-01 20:31:03][INFO] Registering route: GET /api/users
[2025-08-01 20:31:03][INFO] Registering route: POST /api/users
[2025-08-01 20:31:03][INFO] Registering route: GET /api/users/profile
[2025-08-01 20:31:03][INFO] Registering route: GET /api/users/:id
[2025-08-01 20:31:03][INFO] Registering route: PUT /api/users/:id
[2025-08-01 20:31:03][INFO] Registering route: DELETE /api/users/:id
[2025-08-01 20:31:03][INFO] Registering route: GET /api/stats
[2025-08-01 20:31:03][INFO] Registering route: GET /api/stats/:type
[2025-08-01 20:31:03][INFO] Dispatching request: GET /api/auth/captcha
[2025-08-01 20:31:03][INFO] 数据库连接成功: 127.0.0.1:3306/plb_kj
[2025-08-01 20:37:07][INFO] Request: GET /api/auth/captcha
[2025-08-01 20:37:07][INFO] Registering route: POST /api/admin/login
[2025-08-01 20:37:07][INFO] Registering route: POST /api/users/login
[2025-08-01 20:37:07][INFO] Registering route: GET /api/auth/captcha
[2025-08-01 20:37:07][INFO] Registering route: GET /api/auth/validate
[2025-08-01 20:37:07][INFO] Registering route: GET /api/users
[2025-08-01 20:37:07][INFO] Registering route: POST /api/users
[2025-08-01 20:37:07][INFO] Registering route: GET /api/users/profile
[2025-08-01 20:37:07][INFO] Registering route: GET /api/users/:id
[2025-08-01 20:37:07][INFO] Registering route: PUT /api/users/:id
[2025-08-01 20:37:07][INFO] Registering route: DELETE /api/users/:id
[2025-08-01 20:37:07][INFO] Registering route: GET /api/stats
[2025-08-01 20:37:07][INFO] Registering route: GET /api/stats/:type
[2025-08-01 20:37:07][INFO] Dispatching request: GET /api/auth/captcha
[2025-08-01 20:37:07][INFO] 数据库连接成功: 127.0.0.1:3306/plb_kj
[2025-08-01 20:37:07][INFO] Request: GET /api/auth/captcha
[2025-08-01 20:37:07][INFO] Registering route: POST /api/admin/login
[2025-08-01 20:37:07][INFO] Registering route: POST /api/users/login
[2025-08-01 20:37:07][INFO] Registering route: GET /api/auth/captcha
[2025-08-01 20:37:07][INFO] Registering route: GET /api/auth/validate
[2025-08-01 20:37:07][INFO] Registering route: GET /api/users
[2025-08-01 20:37:07][INFO] Registering route: POST /api/users
[2025-08-01 20:37:07][INFO] Registering route: GET /api/users/profile
[2025-08-01 20:37:07][INFO] Registering route: GET /api/users/:id
[2025-08-01 20:37:07][INFO] Registering route: PUT /api/users/:id
[2025-08-01 20:37:07][INFO] Registering route: DELETE /api/users/:id
[2025-08-01 20:37:07][INFO] Registering route: GET /api/stats
[2025-08-01 20:37:07][INFO] Registering route: GET /api/stats/:type
[2025-08-01 20:37:07][INFO] Dispatching request: GET /api/auth/captcha
[2025-08-01 20:37:07][INFO] 数据库连接成功: 127.0.0.1:3306/plb_kj
[2025-08-01 20:37:09][INFO] Request: GET /api/auth/validate
[2025-08-01 20:37:09][INFO] Registering route: POST /api/admin/login
[2025-08-01 20:37:09][INFO] Registering route: POST /api/users/login
[2025-08-01 20:37:09][INFO] Registering route: GET /api/auth/captcha
[2025-08-01 20:37:09][INFO] Registering route: GET /api/auth/validate
[2025-08-01 20:37:09][INFO] Registering route: GET /api/users
[2025-08-01 20:37:09][INFO] Registering route: POST /api/users
[2025-08-01 20:37:09][INFO] Registering route: GET /api/users/profile
[2025-08-01 20:37:09][INFO] Registering route: GET /api/users/:id
[2025-08-01 20:37:09][INFO] Registering route: PUT /api/users/:id
[2025-08-01 20:37:09][INFO] Registering route: DELETE /api/users/:id
[2025-08-01 20:37:09][INFO] Registering route: GET /api/stats
[2025-08-01 20:37:09][INFO] Registering route: GET /api/stats/:type
[2025-08-01 20:37:09][INFO] Dispatching request: GET /api/auth/validate
[2025-08-01 20:37:09][INFO] 数据库连接成功: 127.0.0.1:3306/plb_kj
[2025-08-01 20:37:09][ERROR] Token validation error: 无效的认证令牌
[2025-08-01 20:37:09][INFO] Request: GET /api/auth/captcha
[2025-08-01 20:37:09][INFO] Registering route: POST /api/admin/login
[2025-08-01 20:37:09][INFO] Registering route: POST /api/users/login
[2025-08-01 20:37:09][INFO] Registering route: GET /api/auth/captcha
[2025-08-01 20:37:09][INFO] Registering route: GET /api/auth/validate
[2025-08-01 20:37:09][INFO] Registering route: GET /api/users
[2025-08-01 20:37:09][INFO] Registering route: POST /api/users
[2025-08-01 20:37:09][INFO] Registering route: GET /api/users/profile
[2025-08-01 20:37:09][INFO] Registering route: GET /api/users/:id
[2025-08-01 20:37:09][INFO] Registering route: PUT /api/users/:id
[2025-08-01 20:37:09][INFO] Registering route: DELETE /api/users/:id
[2025-08-01 20:37:09][INFO] Registering route: GET /api/stats
[2025-08-01 20:37:09][INFO] Registering route: GET /api/stats/:type
[2025-08-01 20:37:09][INFO] Dispatching request: GET /api/auth/captcha
[2025-08-01 20:37:09][INFO] 数据库连接成功: 127.0.0.1:3306/plb_kj
[2025-08-01 20:37:09][INFO] Request: GET /api/auth/captcha
[2025-08-01 20:37:09][INFO] Registering route: POST /api/admin/login
[2025-08-01 20:37:09][INFO] Registering route: POST /api/users/login
[2025-08-01 20:37:09][INFO] Registering route: GET /api/auth/captcha
[2025-08-01 20:37:09][INFO] Registering route: GET /api/auth/validate
[2025-08-01 20:37:09][INFO] Registering route: GET /api/users
[2025-08-01 20:37:09][INFO] Registering route: POST /api/users
[2025-08-01 20:37:09][INFO] Registering route: GET /api/users/profile
[2025-08-01 20:37:09][INFO] Registering route: GET /api/users/:id
[2025-08-01 20:37:09][INFO] Registering route: PUT /api/users/:id
[2025-08-01 20:37:09][INFO] Registering route: DELETE /api/users/:id
[2025-08-01 20:37:09][INFO] Registering route: GET /api/stats
[2025-08-01 20:37:09][INFO] Registering route: GET /api/stats/:type
[2025-08-01 20:37:09][INFO] Dispatching request: GET /api/auth/captcha
[2025-08-01 20:37:09][INFO] 数据库连接成功: 127.0.0.1:3306/plb_kj
[2025-08-01 20:48:19][INFO] Request: GET /api/auth/validate
[2025-08-01 20:48:19][INFO] Registering route: POST /api/admin/login
[2025-08-01 20:48:19][INFO] Registering route: POST /api/users/login
[2025-08-01 20:48:19][INFO] Registering route: GET /api/auth/captcha
[2025-08-01 20:48:19][INFO] Registering route: GET /api/auth/validate
[2025-08-01 20:48:19][INFO] Registering route: GET /api/users
[2025-08-01 20:48:19][INFO] Registering route: POST /api/users
[2025-08-01 20:48:19][INFO] Registering route: GET /api/users/profile
[2025-08-01 20:48:19][INFO] Registering route: GET /api/users/:id
[2025-08-01 20:48:19][INFO] Registering route: PUT /api/users/:id
[2025-08-01 20:48:19][INFO] Registering route: DELETE /api/users/:id
[2025-08-01 20:48:19][INFO] Registering route: GET /api/stats
[2025-08-01 20:48:19][INFO] Registering route: GET /api/stats/:type
[2025-08-01 20:48:19][INFO] Dispatching request: GET /api/auth/validate
[2025-08-01 20:48:19][INFO] 数据库连接成功: 127.0.0.1:3306/plb_kj
[2025-08-01 20:48:19][ERROR] Token validation error: 无效的认证令牌
[2025-08-01 20:48:26][INFO] Request: POST /api/admin/login
[2025-08-01 20:48:26][INFO] Registering route: POST /api/admin/login
[2025-08-01 20:48:26][INFO] Registering route: POST /api/users/login
[2025-08-01 20:48:26][INFO] Registering route: GET /api/auth/captcha
[2025-08-01 20:48:26][INFO] Registering route: GET /api/auth/validate
[2025-08-01 20:48:26][INFO] Registering route: GET /api/users
[2025-08-01 20:48:26][INFO] Registering route: POST /api/users
[2025-08-01 20:48:26][INFO] Registering route: GET /api/users/profile
[2025-08-01 20:48:26][INFO] Registering route: GET /api/users/:id
[2025-08-01 20:48:26][INFO] Registering route: PUT /api/users/:id
[2025-08-01 20:48:26][INFO] Registering route: DELETE /api/users/:id
[2025-08-01 20:48:26][INFO] Registering route: GET /api/stats
[2025-08-01 20:48:26][INFO] Registering route: GET /api/stats/:type
[2025-08-01 20:48:26][INFO] Dispatching request: POST /api/admin/login
[2025-08-01 20:48:27][INFO] 数据库连接成功: 127.0.0.1:3306/plb_kj
[2025-08-01 20:48:27][INFO] Admin login attempt for username: admin
[2025-08-01 20:48:27][INFO] Admin user logged in successfully: admin
[2025-08-01 20:55:38][INFO] Request: GET /api/auth/captcha
[2025-08-01 20:55:38][INFO] Registering route: POST /api/admin/login
[2025-08-01 20:55:38][INFO] Registering route: POST /api/users/login
[2025-08-01 20:55:38][INFO] Registering route: GET /api/auth/captcha
[2025-08-01 20:55:38][INFO] Registering route: GET /api/auth/validate
[2025-08-01 20:55:38][INFO] Registering route: GET /api/users
[2025-08-01 20:55:38][INFO] Registering route: POST /api/users
[2025-08-01 20:55:38][INFO] Registering route: GET /api/users/profile
[2025-08-01 20:55:38][INFO] Registering route: GET /api/users/:id
[2025-08-01 20:55:38][INFO] Registering route: PUT /api/users/:id
[2025-08-01 20:55:38][INFO] Registering route: DELETE /api/users/:id
[2025-08-01 20:55:38][INFO] Registering route: GET /api/stats
[2025-08-01 20:55:38][INFO] Registering route: GET /api/stats/:type
[2025-08-01 20:55:38][INFO] Dispatching request: GET /api/auth/captcha
[2025-08-01 20:55:38][INFO] 数据库连接成功: 127.0.0.1:3306/plb_kj
[2025-08-01 20:55:40][INFO] Request: GET /api/auth/validate
[2025-08-01 20:55:40][INFO] Registering route: POST /api/admin/login
[2025-08-01 20:55:40][INFO] Registering route: POST /api/users/login
[2025-08-01 20:55:40][INFO] Registering route: GET /api/auth/captcha
[2025-08-01 20:55:40][INFO] Registering route: GET /api/auth/validate
[2025-08-01 20:55:40][INFO] Registering route: GET /api/users
[2025-08-01 20:55:40][INFO] Registering route: POST /api/users
[2025-08-01 20:55:40][INFO] Registering route: GET /api/users/profile
[2025-08-01 20:55:40][INFO] Registering route: GET /api/users/:id
[2025-08-01 20:55:40][INFO] Registering route: PUT /api/users/:id
[2025-08-01 20:55:40][INFO] Registering route: DELETE /api/users/:id
[2025-08-01 20:55:40][INFO] Registering route: GET /api/stats
[2025-08-01 20:55:40][INFO] Registering route: GET /api/stats/:type
[2025-08-01 20:55:40][INFO] Dispatching request: GET /api/auth/validate
[2025-08-01 20:55:40][INFO] 数据库连接成功: 127.0.0.1:3306/plb_kj
[2025-08-01 20:55:40][ERROR] Token validation error: 无效的认证令牌
[2025-08-01 20:55:40][INFO] Request: GET /api/auth/captcha
[2025-08-01 20:55:40][INFO] Registering route: POST /api/admin/login
[2025-08-01 20:55:40][INFO] Registering route: POST /api/users/login
[2025-08-01 20:55:40][INFO] Registering route: GET /api/auth/captcha
[2025-08-01 20:55:40][INFO] Registering route: GET /api/auth/validate
[2025-08-01 20:55:40][INFO] Registering route: GET /api/users
[2025-08-01 20:55:40][INFO] Registering route: POST /api/users
[2025-08-01 20:55:40][INFO] Registering route: GET /api/users/profile
[2025-08-01 20:55:40][INFO] Registering route: GET /api/users/:id
[2025-08-01 20:55:40][INFO] Registering route: PUT /api/users/:id
[2025-08-01 20:55:40][INFO] Registering route: DELETE /api/users/:id
[2025-08-01 20:55:40][INFO] Registering route: GET /api/stats
[2025-08-01 20:55:40][INFO] Registering route: GET /api/stats/:type
[2025-08-01 20:55:40][INFO] Dispatching request: GET /api/auth/captcha
[2025-08-01 20:55:40][INFO] 数据库连接成功: 127.0.0.1:3306/plb_kj
[2025-08-01 20:59:30][INFO] Request: GET /api/auth/captcha
[2025-08-01 20:59:30][INFO] Registering route: POST /api/admin/login
[2025-08-01 20:59:30][INFO] Registering route: POST /api/users/login
[2025-08-01 20:59:30][INFO] Registering route: GET /api/auth/captcha
[2025-08-01 20:59:30][INFO] Registering route: GET /api/auth/validate
[2025-08-01 20:59:30][INFO] Registering route: GET /api/users
[2025-08-01 20:59:30][INFO] Registering route: POST /api/users
[2025-08-01 20:59:30][INFO] Registering route: GET /api/users/profile
[2025-08-01 20:59:30][INFO] Registering route: GET /api/users/:id
[2025-08-01 20:59:30][INFO] Registering route: PUT /api/users/:id
[2025-08-01 20:59:30][INFO] Registering route: DELETE /api/users/:id
[2025-08-01 20:59:30][INFO] Registering route: GET /api/stats
[2025-08-01 20:59:30][INFO] Registering route: GET /api/stats/:type
[2025-08-01 20:59:30][INFO] Dispatching request: GET /api/auth/captcha
[2025-08-01 20:59:30][INFO] 数据库连接成功: 127.0.0.1:3306/plb_kj
[2025-08-01 20:59:32][INFO] Request: GET /api/auth/validate
[2025-08-01 20:59:32][INFO] Registering route: POST /api/admin/login
[2025-08-01 20:59:32][INFO] Registering route: POST /api/users/login
[2025-08-01 20:59:32][INFO] Registering route: GET /api/auth/captcha
[2025-08-01 20:59:32][INFO] Registering route: GET /api/auth/validate
[2025-08-01 20:59:32][INFO] Registering route: GET /api/users
[2025-08-01 20:59:32][INFO] Registering route: POST /api/users
[2025-08-01 20:59:32][INFO] Registering route: GET /api/users/profile
[2025-08-01 20:59:32][INFO] Registering route: GET /api/users/:id
[2025-08-01 20:59:32][INFO] Registering route: PUT /api/users/:id
[2025-08-01 20:59:32][INFO] Registering route: DELETE /api/users/:id
[2025-08-01 20:59:32][INFO] Registering route: GET /api/stats
[2025-08-01 20:59:32][INFO] Registering route: GET /api/stats/:type
[2025-08-01 20:59:32][INFO] Dispatching request: GET /api/auth/validate
[2025-08-01 20:59:32][INFO] 数据库连接成功: 127.0.0.1:3306/plb_kj
[2025-08-01 20:59:32][ERROR] Token validation error: 无效的认证令牌
[2025-08-01 20:59:32][INFO] Request: GET /api/auth/captcha
[2025-08-01 20:59:32][INFO] Registering route: POST /api/admin/login
[2025-08-01 20:59:32][INFO] Registering route: POST /api/users/login
[2025-08-01 20:59:32][INFO] Registering route: GET /api/auth/captcha
[2025-08-01 20:59:32][INFO] Registering route: GET /api/auth/validate
[2025-08-01 20:59:32][INFO] Registering route: GET /api/users
[2025-08-01 20:59:32][INFO] Registering route: POST /api/users
[2025-08-01 20:59:32][INFO] Registering route: GET /api/users/profile
[2025-08-01 20:59:32][INFO] Registering route: GET /api/users/:id
[2025-08-01 20:59:32][INFO] Registering route: PUT /api/users/:id
[2025-08-01 20:59:32][INFO] Registering route: DELETE /api/users/:id
[2025-08-01 20:59:32][INFO] Registering route: GET /api/stats
[2025-08-01 20:59:32][INFO] Registering route: GET /api/stats/:type
[2025-08-01 20:59:32][INFO] Dispatching request: GET /api/auth/captcha
[2025-08-01 20:59:32][INFO] 数据库连接成功: 127.0.0.1:3306/plb_kj
[2025-08-01 21:00:30][INFO] Request: GET /api/auth/captcha
[2025-08-01 21:00:30][INFO] Registering route: POST /api/admin/login
[2025-08-01 21:00:30][INFO] Registering route: POST /api/users/login
[2025-08-01 21:00:30][INFO] Registering route: GET /api/auth/captcha
[2025-08-01 21:00:30][INFO] Registering route: GET /api/auth/validate
[2025-08-01 21:00:30][INFO] Registering route: GET /api/users
[2025-08-01 21:00:30][INFO] Registering route: POST /api/users
[2025-08-01 21:00:30][INFO] Registering route: GET /api/users/profile
[2025-08-01 21:00:30][INFO] Registering route: GET /api/users/:id
[2025-08-01 21:00:30][INFO] Registering route: PUT /api/users/:id
[2025-08-01 21:00:30][INFO] Registering route: DELETE /api/users/:id
[2025-08-01 21:00:30][INFO] Registering route: GET /api/stats
[2025-08-01 21:00:30][INFO] Registering route: GET /api/stats/:type
[2025-08-01 21:00:30][INFO] Dispatching request: GET /api/auth/captcha
[2025-08-01 21:00:30][INFO] 数据库连接成功: 127.0.0.1:3306/plb_kj
[2025-08-01 21:00:32][INFO] Request: GET /api/auth/validate
[2025-08-01 21:00:32][INFO] Registering route: POST /api/admin/login
[2025-08-01 21:00:32][INFO] Registering route: POST /api/users/login
[2025-08-01 21:00:32][INFO] Registering route: GET /api/auth/captcha
[2025-08-01 21:00:32][INFO] Registering route: GET /api/auth/validate
[2025-08-01 21:00:32][INFO] Registering route: GET /api/users
[2025-08-01 21:00:32][INFO] Registering route: POST /api/users
[2025-08-01 21:00:32][INFO] Registering route: GET /api/users/profile
[2025-08-01 21:00:32][INFO] Registering route: GET /api/users/:id
[2025-08-01 21:00:32][INFO] Registering route: PUT /api/users/:id
[2025-08-01 21:00:32][INFO] Registering route: DELETE /api/users/:id
[2025-08-01 21:00:32][INFO] Registering route: GET /api/stats
[2025-08-01 21:00:32][INFO] Registering route: GET /api/stats/:type
[2025-08-01 21:00:32][INFO] Dispatching request: GET /api/auth/validate
[2025-08-01 21:00:32][INFO] 数据库连接成功: 127.0.0.1:3306/plb_kj
[2025-08-01 21:00:32][ERROR] Token validation error: 无效的认证令牌
[2025-08-01 21:00:32][INFO] Request: GET /api/auth/captcha
[2025-08-01 21:00:32][INFO] Registering route: POST /api/admin/login
[2025-08-01 21:00:32][INFO] Registering route: POST /api/users/login
[2025-08-01 21:00:32][INFO] Registering route: GET /api/auth/captcha
[2025-08-01 21:00:32][INFO] Registering route: GET /api/auth/validate
[2025-08-01 21:00:32][INFO] Registering route: GET /api/users
[2025-08-01 21:00:32][INFO] Registering route: POST /api/users
[2025-08-01 21:00:32][INFO] Registering route: GET /api/users/profile
[2025-08-01 21:00:32][INFO] Registering route: GET /api/users/:id
[2025-08-01 21:00:32][INFO] Registering route: PUT /api/users/:id
[2025-08-01 21:00:32][INFO] Registering route: DELETE /api/users/:id
[2025-08-01 21:00:32][INFO] Registering route: GET /api/stats
[2025-08-01 21:00:32][INFO] Registering route: GET /api/stats/:type
[2025-08-01 21:00:32][INFO] Dispatching request: GET /api/auth/captcha
[2025-08-01 21:00:32][INFO] 数据库连接成功: 127.0.0.1:3306/plb_kj
[2025-08-01 21:06:17][INFO] Request: GET /api/auth/captcha
[2025-08-01 21:06:17][INFO] Registering route: POST /api/admin/login
[2025-08-01 21:06:17][INFO] Registering route: POST /api/users/login
[2025-08-01 21:06:17][INFO] Registering route: GET /api/auth/captcha
[2025-08-01 21:06:17][INFO] Registering route: GET /api/auth/validate
[2025-08-01 21:06:17][INFO] Registering route: GET /api/users
[2025-08-01 21:06:17][INFO] Registering route: POST /api/users
[2025-08-01 21:06:17][INFO] Registering route: GET /api/users/profile
[2025-08-01 21:06:17][INFO] Registering route: GET /api/users/:id
[2025-08-01 21:06:17][INFO] Registering route: PUT /api/users/:id
[2025-08-01 21:06:17][INFO] Registering route: DELETE /api/users/:id
[2025-08-01 21:06:17][INFO] Registering route: GET /api/stats
[2025-08-01 21:06:17][INFO] Registering route: GET /api/stats/:type
[2025-08-01 21:06:17][INFO] Dispatching request: GET /api/auth/captcha
[2025-08-01 21:06:17][INFO] 数据库连接成功: 127.0.0.1:3306/plb_kj
[2025-08-01 21:06:19][INFO] Request: GET /api/auth/validate
[2025-08-01 21:06:19][INFO] Registering route: POST /api/admin/login
[2025-08-01 21:06:19][INFO] Registering route: POST /api/users/login
[2025-08-01 21:06:19][INFO] Registering route: GET /api/auth/captcha
[2025-08-01 21:06:19][INFO] Registering route: GET /api/auth/validate
[2025-08-01 21:06:19][INFO] Registering route: GET /api/users
[2025-08-01 21:06:19][INFO] Registering route: POST /api/users
[2025-08-01 21:06:19][INFO] Registering route: GET /api/users/profile
[2025-08-01 21:06:19][INFO] Registering route: GET /api/users/:id
[2025-08-01 21:06:19][INFO] Registering route: PUT /api/users/:id
[2025-08-01 21:06:19][INFO] Registering route: DELETE /api/users/:id
[2025-08-01 21:06:19][INFO] Registering route: GET /api/stats
[2025-08-01 21:06:19][INFO] Registering route: GET /api/stats/:type
[2025-08-01 21:06:19][INFO] Dispatching request: GET /api/auth/validate
[2025-08-01 21:06:19][INFO] 数据库连接成功: 127.0.0.1:3306/plb_kj
[2025-08-01 21:06:19][ERROR] Token validation error: 无效的认证令牌
[2025-08-01 21:06:19][INFO] Request: GET /api/auth/captcha
[2025-08-01 21:06:19][INFO] Registering route: POST /api/admin/login
[2025-08-01 21:06:19][INFO] Registering route: POST /api/users/login
[2025-08-01 21:06:19][INFO] Registering route: GET /api/auth/captcha
[2025-08-01 21:06:19][INFO] Registering route: GET /api/auth/validate
[2025-08-01 21:06:19][INFO] Registering route: GET /api/users
[2025-08-01 21:06:19][INFO] Registering route: POST /api/users
[2025-08-01 21:06:19][INFO] Registering route: GET /api/users/profile
[2025-08-01 21:06:19][INFO] Registering route: GET /api/users/:id
[2025-08-01 21:06:19][INFO] Registering route: PUT /api/users/:id
[2025-08-01 21:06:19][INFO] Registering route: DELETE /api/users/:id
[2025-08-01 21:06:19][INFO] Registering route: GET /api/stats
[2025-08-01 21:06:19][INFO] Registering route: GET /api/stats/:type
[2025-08-01 21:06:19][INFO] Dispatching request: GET /api/auth/captcha
[2025-08-01 21:06:19][INFO] 数据库连接成功: 127.0.0.1:3306/plb_kj
[2025-08-01 21:08:51][INFO] Request: GET /api/auth/captcha
[2025-08-01 21:08:51][INFO] Registering route: POST /api/admin/login
[2025-08-01 21:08:51][INFO] Registering route: POST /api/users/login
[2025-08-01 21:08:51][INFO] Registering route: GET /api/auth/captcha
[2025-08-01 21:08:51][INFO] Registering route: GET /api/auth/validate
[2025-08-01 21:08:51][INFO] Registering route: GET /api/users
[2025-08-01 21:08:51][INFO] Registering route: POST /api/users
[2025-08-01 21:08:51][INFO] Registering route: GET /api/users/profile
[2025-08-01 21:08:51][INFO] Registering route: GET /api/users/:id
[2025-08-01 21:08:51][INFO] Registering route: PUT /api/users/:id
[2025-08-01 21:08:51][INFO] Registering route: DELETE /api/users/:id
[2025-08-01 21:08:51][INFO] Registering route: GET /api/stats
[2025-08-01 21:08:51][INFO] Registering route: GET /api/stats/:type
[2025-08-01 21:08:51][INFO] Dispatching request: GET /api/auth/captcha
[2025-08-01 21:08:51][INFO] 数据库连接成功: 127.0.0.1:3306/plb_kj
[2025-08-01 21:08:53][INFO] Request: GET /api/auth/validate
[2025-08-01 21:08:53][INFO] Registering route: POST /api/admin/login
[2025-08-01 21:08:53][INFO] Registering route: POST /api/users/login
[2025-08-01 21:08:53][INFO] Registering route: GET /api/auth/captcha
[2025-08-01 21:08:53][INFO] Registering route: GET /api/auth/validate
[2025-08-01 21:08:53][INFO] Registering route: GET /api/users
[2025-08-01 21:08:53][INFO] Registering route: POST /api/users
[2025-08-01 21:08:53][INFO] Registering route: GET /api/users/profile
[2025-08-01 21:08:53][INFO] Registering route: GET /api/users/:id
[2025-08-01 21:08:53][INFO] Registering route: PUT /api/users/:id
[2025-08-01 21:08:53][INFO] Registering route: DELETE /api/users/:id
[2025-08-01 21:08:53][INFO] Registering route: GET /api/stats
[2025-08-01 21:08:53][INFO] Registering route: GET /api/stats/:type
[2025-08-01 21:08:53][INFO] Dispatching request: GET /api/auth/validate
[2025-08-01 21:08:53][INFO] 数据库连接成功: 127.0.0.1:3306/plb_kj
[2025-08-01 21:08:53][ERROR] Token validation error: 无效的认证令牌
[2025-08-01 21:08:53][INFO] Request: GET /api/auth/captcha
[2025-08-01 21:08:53][INFO] Registering route: POST /api/admin/login
[2025-08-01 21:08:53][INFO] Registering route: POST /api/users/login
[2025-08-01 21:08:53][INFO] Registering route: GET /api/auth/captcha
[2025-08-01 21:08:53][INFO] Registering route: GET /api/auth/validate
[2025-08-01 21:08:53][INFO] Registering route: GET /api/users
[2025-08-01 21:08:53][INFO] Registering route: POST /api/users
[2025-08-01 21:08:53][INFO] Registering route: GET /api/users/profile
[2025-08-01 21:08:53][INFO] Registering route: GET /api/users/:id
[2025-08-01 21:08:53][INFO] Registering route: PUT /api/users/:id
[2025-08-01 21:08:53][INFO] Registering route: DELETE /api/users/:id
[2025-08-01 21:08:53][INFO] Registering route: GET /api/stats
[2025-08-01 21:08:53][INFO] Registering route: GET /api/stats/:type
[2025-08-01 21:08:53][INFO] Dispatching request: GET /api/auth/captcha
[2025-08-01 21:08:53][INFO] 数据库连接成功: 127.0.0.1:3306/plb_kj
[2025-08-01 21:11:54][INFO] Request: GET /api/auth/captcha
[2025-08-01 21:11:54][INFO] Registering route: POST /api/admin/login
[2025-08-01 21:11:54][INFO] Registering route: POST /api/users/login
[2025-08-01 21:11:54][INFO] Registering route: GET /api/auth/captcha
[2025-08-01 21:11:54][INFO] Registering route: GET /api/auth/validate
[2025-08-01 21:11:54][INFO] Registering route: GET /api/users
[2025-08-01 21:11:54][INFO] Registering route: POST /api/users
[2025-08-01 21:11:54][INFO] Registering route: GET /api/users/profile
[2025-08-01 21:11:54][INFO] Registering route: GET /api/users/:id
[2025-08-01 21:11:54][INFO] Registering route: PUT /api/users/:id
[2025-08-01 21:11:54][INFO] Registering route: DELETE /api/users/:id
[2025-08-01 21:11:54][INFO] Registering route: GET /api/stats
[2025-08-01 21:11:54][INFO] Registering route: GET /api/stats/:type
[2025-08-01 21:11:54][INFO] Dispatching request: GET /api/auth/captcha
[2025-08-01 21:11:54][INFO] 数据库连接成功: 127.0.0.1:3306/plb_kj
[2025-08-01 21:11:54][INFO] Request: GET /api/auth/captcha
[2025-08-01 21:11:54][INFO] Registering route: POST /api/admin/login
[2025-08-01 21:11:54][INFO] Registering route: POST /api/users/login
[2025-08-01 21:11:54][INFO] Registering route: GET /api/auth/captcha
[2025-08-01 21:11:54][INFO] Registering route: GET /api/auth/validate
[2025-08-01 21:11:54][INFO] Registering route: GET /api/users
[2025-08-01 21:11:54][INFO] Registering route: POST /api/users
[2025-08-01 21:11:54][INFO] Registering route: GET /api/users/profile
[2025-08-01 21:11:54][INFO] Registering route: GET /api/users/:id
[2025-08-01 21:11:54][INFO] Registering route: PUT /api/users/:id
[2025-08-01 21:11:54][INFO] Registering route: DELETE /api/users/:id
[2025-08-01 21:11:54][INFO] Registering route: GET /api/stats
[2025-08-01 21:11:54][INFO] Registering route: GET /api/stats/:type
[2025-08-01 21:11:54][INFO] Dispatching request: GET /api/auth/captcha
[2025-08-01 21:11:54][INFO] 数据库连接成功: 127.0.0.1:3306/plb_kj
[2025-08-01 21:11:55][INFO] Request: GET /api/auth/validate
[2025-08-01 21:11:55][INFO] Registering route: POST /api/admin/login
[2025-08-01 21:11:55][INFO] Registering route: POST /api/users/login
[2025-08-01 21:11:55][INFO] Registering route: GET /api/auth/captcha
[2025-08-01 21:11:55][INFO] Registering route: GET /api/auth/validate
[2025-08-01 21:11:55][INFO] Registering route: GET /api/users
[2025-08-01 21:11:55][INFO] Registering route: POST /api/users
[2025-08-01 21:11:55][INFO] Registering route: GET /api/users/profile
[2025-08-01 21:11:55][INFO] Registering route: GET /api/users/:id
[2025-08-01 21:11:55][INFO] Registering route: PUT /api/users/:id
[2025-08-01 21:11:55][INFO] Registering route: DELETE /api/users/:id
[2025-08-01 21:11:55][INFO] Registering route: GET /api/stats
[2025-08-01 21:11:55][INFO] Registering route: GET /api/stats/:type
[2025-08-01 21:11:55][INFO] Dispatching request: GET /api/auth/validate
[2025-08-01 21:11:56][INFO] 数据库连接成功: 127.0.0.1:3306/plb_kj
[2025-08-01 21:11:56][ERROR] Token validation error: 无效的认证令牌
[2025-08-01 21:11:56][INFO] Request: GET /api/auth/captcha
[2025-08-01 21:11:56][INFO] Registering route: POST /api/admin/login
[2025-08-01 21:11:56][INFO] Registering route: POST /api/users/login
[2025-08-01 21:11:56][INFO] Registering route: GET /api/auth/captcha
[2025-08-01 21:11:56][INFO] Registering route: GET /api/auth/validate
[2025-08-01 21:11:56][INFO] Registering route: GET /api/users
[2025-08-01 21:11:56][INFO] Registering route: POST /api/users
[2025-08-01 21:11:56][INFO] Registering route: GET /api/users/profile
[2025-08-01 21:11:56][INFO] Registering route: GET /api/users/:id
[2025-08-01 21:11:56][INFO] Registering route: PUT /api/users/:id
[2025-08-01 21:11:56][INFO] Registering route: DELETE /api/users/:id
[2025-08-01 21:11:56][INFO] Registering route: GET /api/stats
[2025-08-01 21:11:56][INFO] Registering route: GET /api/stats/:type
[2025-08-01 21:11:56][INFO] Dispatching request: GET /api/auth/captcha
[2025-08-01 21:11:56][INFO] 数据库连接成功: 127.0.0.1:3306/plb_kj
[2025-08-01 21:11:56][INFO] Request: GET /api/auth/validate
[2025-08-01 21:11:56][INFO] Registering route: POST /api/admin/login
[2025-08-01 21:11:56][INFO] Registering route: POST /api/users/login
[2025-08-01 21:11:56][INFO] Registering route: GET /api/auth/captcha
[2025-08-01 21:11:56][INFO] Registering route: GET /api/auth/validate
[2025-08-01 21:11:56][INFO] Registering route: GET /api/users
[2025-08-01 21:11:56][INFO] Registering route: POST /api/users
[2025-08-01 21:11:56][INFO] Registering route: GET /api/users/profile
[2025-08-01 21:11:56][INFO] Registering route: GET /api/users/:id
[2025-08-01 21:11:56][INFO] Registering route: PUT /api/users/:id
[2025-08-01 21:11:56][INFO] Registering route: DELETE /api/users/:id
[2025-08-01 21:11:56][INFO] Registering route: GET /api/stats
[2025-08-01 21:11:56][INFO] Registering route: GET /api/stats/:type
[2025-08-01 21:11:56][INFO] Dispatching request: GET /api/auth/validate
[2025-08-01 21:11:56][INFO] 数据库连接成功: 127.0.0.1:3306/plb_kj
[2025-08-01 21:11:56][ERROR] Token validation error: 无效的认证令牌
[2025-08-01 21:11:56][INFO] Request: GET /api/auth/captcha
[2025-08-01 21:11:56][INFO] Registering route: POST /api/admin/login
[2025-08-01 21:11:56][INFO] Registering route: POST /api/users/login
[2025-08-01 21:11:56][INFO] Registering route: GET /api/auth/captcha
[2025-08-01 21:11:56][INFO] Registering route: GET /api/auth/validate
[2025-08-01 21:11:56][INFO] Registering route: GET /api/users
[2025-08-01 21:11:56][INFO] Registering route: POST /api/users
[2025-08-01 21:11:56][INFO] Registering route: GET /api/users/profile
[2025-08-01 21:11:56][INFO] Registering route: GET /api/users/:id
[2025-08-01 21:11:56][INFO] Registering route: PUT /api/users/:id
[2025-08-01 21:11:56][INFO] Registering route: DELETE /api/users/:id
[2025-08-01 21:11:56][INFO] Registering route: GET /api/stats
[2025-08-01 21:11:56][INFO] Registering route: GET /api/stats/:type
[2025-08-01 21:11:56][INFO] Dispatching request: GET /api/auth/captcha
[2025-08-01 21:11:56][INFO] 数据库连接成功: 127.0.0.1:3306/plb_kj
[2025-08-01 21:15:31][INFO] Request: GET /
[2025-08-01 21:15:31][INFO] Registering route: POST /api/admin/login
[2025-08-01 21:15:31][INFO] Registering route: POST /api/users/login
[2025-08-01 21:15:31][INFO] Registering route: GET /api/auth/captcha
[2025-08-01 21:15:31][INFO] Registering route: GET /api/auth/validate
[2025-08-01 21:15:31][INFO] Registering route: GET /api/users
[2025-08-01 21:15:31][INFO] Registering route: POST /api/users
[2025-08-01 21:15:31][INFO] Registering route: GET /api/users/profile
[2025-08-01 21:15:31][INFO] Registering route: GET /api/users/:id
[2025-08-01 21:15:31][INFO] Registering route: PUT /api/users/:id
[2025-08-01 21:15:31][INFO] Registering route: DELETE /api/users/:id
[2025-08-01 21:15:31][INFO] Registering route: GET /api/stats
[2025-08-01 21:15:31][INFO] Registering route: GET /api/stats/:type
[2025-08-01 21:15:31][INFO] Dispatching request: GET /
[2025-08-01 21:15:31][WARNING] Route not found: GET /
[2025-08-01 21:23:55][INFO] Request: GET /api/auth/captcha
[2025-08-01 21:23:55][INFO] Registering route: POST /api/admin/login
[2025-08-01 21:23:55][INFO] Registering route: POST /api/users/login
[2025-08-01 21:23:55][INFO] Registering route: GET /api/auth/captcha
[2025-08-01 21:23:55][INFO] Registering route: GET /api/auth/validate
[2025-08-01 21:23:55][INFO] Registering route: GET /api/users
[2025-08-01 21:23:55][INFO] Registering route: POST /api/users
[2025-08-01 21:23:55][INFO] Registering route: GET /api/users/profile
[2025-08-01 21:23:55][INFO] Registering route: GET /api/users/:id
[2025-08-01 21:23:55][INFO] Registering route: PUT /api/users/:id
[2025-08-01 21:23:55][INFO] Registering route: DELETE /api/users/:id
[2025-08-01 21:23:55][INFO] Registering route: GET /api/stats
[2025-08-01 21:23:55][INFO] Registering route: GET /api/stats/:type
[2025-08-01 21:23:55][INFO] Dispatching request: GET /api/auth/captcha
[2025-08-01 21:23:55][INFO] 数据库连接成功: 127.0.0.1:3306/plb_kj
[2025-08-01 21:23:57][INFO] Request: GET /api/auth/validate
[2025-08-01 21:23:57][INFO] Registering route: POST /api/admin/login
[2025-08-01 21:23:57][INFO] Registering route: POST /api/users/login
[2025-08-01 21:23:57][INFO] Registering route: GET /api/auth/captcha
[2025-08-01 21:23:57][INFO] Registering route: GET /api/auth/validate
[2025-08-01 21:23:57][INFO] Registering route: GET /api/users
[2025-08-01 21:23:57][INFO] Registering route: POST /api/users
[2025-08-01 21:23:57][INFO] Registering route: GET /api/users/profile
[2025-08-01 21:23:57][INFO] Registering route: GET /api/users/:id
[2025-08-01 21:23:57][INFO] Registering route: PUT /api/users/:id
[2025-08-01 21:23:57][INFO] Registering route: DELETE /api/users/:id
[2025-08-01 21:23:57][INFO] Registering route: GET /api/stats
[2025-08-01 21:23:57][INFO] Registering route: GET /api/stats/:type
[2025-08-01 21:23:57][INFO] Dispatching request: GET /api/auth/validate
[2025-08-01 21:23:57][INFO] 数据库连接成功: 127.0.0.1:3306/plb_kj
[2025-08-01 21:23:57][ERROR] Token validation error: 无效的认证令牌
[2025-08-01 21:23:57][INFO] Request: GET /api/auth/captcha
[2025-08-01 21:23:57][INFO] Registering route: POST /api/admin/login
[2025-08-01 21:23:57][INFO] Registering route: POST /api/users/login
[2025-08-01 21:23:57][INFO] Registering route: GET /api/auth/captcha
[2025-08-01 21:23:57][INFO] Registering route: GET /api/auth/validate
[2025-08-01 21:23:57][INFO] Registering route: GET /api/users
[2025-08-01 21:23:57][INFO] Registering route: POST /api/users
[2025-08-01 21:23:57][INFO] Registering route: GET /api/users/profile
[2025-08-01 21:23:57][INFO] Registering route: GET /api/users/:id
[2025-08-01 21:23:57][INFO] Registering route: PUT /api/users/:id
[2025-08-01 21:23:57][INFO] Registering route: DELETE /api/users/:id
[2025-08-01 21:23:57][INFO] Registering route: GET /api/stats
[2025-08-01 21:23:57][INFO] Registering route: GET /api/stats/:type
[2025-08-01 21:23:57][INFO] Dispatching request: GET /api/auth/captcha
[2025-08-01 21:23:57][INFO] 数据库连接成功: 127.0.0.1:3306/plb_kj
[2025-08-01 21:26:56][INFO] Request: GET /api/auth/captcha
[2025-08-01 21:26:56][INFO] Registering route: POST /api/admin/login
[2025-08-01 21:26:56][INFO] Registering route: POST /api/users/login
[2025-08-01 21:26:56][INFO] Registering route: GET /api/auth/captcha
[2025-08-01 21:26:56][INFO] Registering route: GET /api/auth/validate
[2025-08-01 21:26:56][INFO] Registering route: GET /api/users
[2025-08-01 21:26:56][INFO] Registering route: POST /api/users
[2025-08-01 21:26:56][INFO] Registering route: GET /api/users/profile
[2025-08-01 21:26:56][INFO] Registering route: GET /api/users/:id
[2025-08-01 21:26:56][INFO] Registering route: PUT /api/users/:id
[2025-08-01 21:26:56][INFO] Registering route: DELETE /api/users/:id
[2025-08-01 21:26:56][INFO] Registering route: GET /api/stats
[2025-08-01 21:26:56][INFO] Registering route: GET /api/stats/:type
[2025-08-01 21:26:56][INFO] Dispatching request: GET /api/auth/captcha
[2025-08-01 21:26:56][INFO] 数据库连接成功: 127.0.0.1:3306/plb_kj
[2025-08-01 21:26:58][INFO] Request: GET /api/auth/validate
[2025-08-01 21:26:58][INFO] Registering route: POST /api/admin/login
[2025-08-01 21:26:58][INFO] Registering route: POST /api/users/login
[2025-08-01 21:26:58][INFO] Registering route: GET /api/auth/captcha
[2025-08-01 21:26:58][INFO] Registering route: GET /api/auth/validate
[2025-08-01 21:26:58][INFO] Registering route: GET /api/users
[2025-08-01 21:26:58][INFO] Registering route: POST /api/users
[2025-08-01 21:26:58][INFO] Registering route: GET /api/users/profile
[2025-08-01 21:26:58][INFO] Registering route: GET /api/users/:id
[2025-08-01 21:26:58][INFO] Registering route: PUT /api/users/:id
[2025-08-01 21:26:58][INFO] Registering route: DELETE /api/users/:id
[2025-08-01 21:26:58][INFO] Registering route: GET /api/stats
[2025-08-01 21:26:58][INFO] Registering route: GET /api/stats/:type
[2025-08-01 21:26:58][INFO] Dispatching request: GET /api/auth/validate
[2025-08-01 21:26:58][INFO] 数据库连接成功: 127.0.0.1:3306/plb_kj
[2025-08-01 21:26:58][ERROR] Token validation error: 无效的认证令牌
[2025-08-01 21:26:58][INFO] Request: GET /api/auth/captcha
[2025-08-01 21:26:58][INFO] Registering route: POST /api/admin/login
[2025-08-01 21:26:58][INFO] Registering route: POST /api/users/login
[2025-08-01 21:26:58][INFO] Registering route: GET /api/auth/captcha
[2025-08-01 21:26:58][INFO] Registering route: GET /api/auth/validate
[2025-08-01 21:26:58][INFO] Registering route: GET /api/users
[2025-08-01 21:26:58][INFO] Registering route: POST /api/users
[2025-08-01 21:26:58][INFO] Registering route: GET /api/users/profile
[2025-08-01 21:26:58][INFO] Registering route: GET /api/users/:id
[2025-08-01 21:26:58][INFO] Registering route: PUT /api/users/:id
[2025-08-01 21:26:58][INFO] Registering route: DELETE /api/users/:id
[2025-08-01 21:26:58][INFO] Registering route: GET /api/stats
[2025-08-01 21:26:58][INFO] Registering route: GET /api/stats/:type
[2025-08-01 21:26:58][INFO] Dispatching request: GET /api/auth/captcha
[2025-08-01 21:26:58][INFO] 数据库连接成功: 127.0.0.1:3306/plb_kj
[2025-08-01 21:31:13][INFO] Request: GET /api/auth/captcha
[2025-08-01 21:31:13][INFO] Registering route: POST /api/admin/login
[2025-08-01 21:31:13][INFO] Registering route: POST /api/users/login
[2025-08-01 21:31:13][INFO] Registering route: GET /api/auth/captcha
[2025-08-01 21:31:13][INFO] Registering route: GET /api/auth/validate
[2025-08-01 21:31:13][INFO] Registering route: GET /api/users
[2025-08-01 21:31:13][INFO] Registering route: POST /api/users
[2025-08-01 21:31:13][INFO] Registering route: GET /api/users/profile
[2025-08-01 21:31:13][INFO] Registering route: GET /api/users/:id
[2025-08-01 21:31:13][INFO] Registering route: PUT /api/users/:id
[2025-08-01 21:31:13][INFO] Registering route: DELETE /api/users/:id
[2025-08-01 21:31:13][INFO] Registering route: GET /api/stats
[2025-08-01 21:31:13][INFO] Registering route: GET /api/stats/:type
[2025-08-01 21:31:13][INFO] Dispatching request: GET /api/auth/captcha
[2025-08-01 21:31:13][INFO] 数据库连接成功: 127.0.0.1:3306/plb_kj
[2025-08-01 21:31:15][INFO] Request: GET /api/auth/validate
[2025-08-01 21:31:15][INFO] Registering route: POST /api/admin/login
[2025-08-01 21:31:15][INFO] Registering route: POST /api/users/login
[2025-08-01 21:31:15][INFO] Registering route: GET /api/auth/captcha
[2025-08-01 21:31:15][INFO] Registering route: GET /api/auth/validate
[2025-08-01 21:31:15][INFO] Registering route: GET /api/users
[2025-08-01 21:31:15][INFO] Registering route: POST /api/users
[2025-08-01 21:31:15][INFO] Registering route: GET /api/users/profile
[2025-08-01 21:31:15][INFO] Registering route: GET /api/users/:id
[2025-08-01 21:31:15][INFO] Registering route: PUT /api/users/:id
[2025-08-01 21:31:15][INFO] Registering route: DELETE /api/users/:id
[2025-08-01 21:31:15][INFO] Registering route: GET /api/stats
[2025-08-01 21:31:15][INFO] Registering route: GET /api/stats/:type
[2025-08-01 21:31:15][INFO] Dispatching request: GET /api/auth/validate
[2025-08-01 21:31:15][INFO] 数据库连接成功: 127.0.0.1:3306/plb_kj
[2025-08-01 21:31:15][ERROR] Token validation error: 无效的认证令牌
[2025-08-01 21:31:15][INFO] Request: GET /api/auth/captcha
[2025-08-01 21:31:15][INFO] Registering route: POST /api/admin/login
[2025-08-01 21:31:15][INFO] Registering route: POST /api/users/login
[2025-08-01 21:31:15][INFO] Registering route: GET /api/auth/captcha
[2025-08-01 21:31:15][INFO] Registering route: GET /api/auth/validate
[2025-08-01 21:31:15][INFO] Registering route: GET /api/users
[2025-08-01 21:31:15][INFO] Registering route: POST /api/users
[2025-08-01 21:31:15][INFO] Registering route: GET /api/users/profile
[2025-08-01 21:31:15][INFO] Registering route: GET /api/users/:id
[2025-08-01 21:31:15][INFO] Registering route: PUT /api/users/:id
[2025-08-01 21:31:15][INFO] Registering route: DELETE /api/users/:id
[2025-08-01 21:31:15][INFO] Registering route: GET /api/stats
[2025-08-01 21:31:15][INFO] Registering route: GET /api/stats/:type
[2025-08-01 21:31:15][INFO] Dispatching request: GET /api/auth/captcha
[2025-08-01 21:31:15][INFO] 数据库连接成功: 127.0.0.1:3306/plb_kj
[2025-08-01 21:31:17][INFO] Request: GET /api/auth/captcha
[2025-08-01 21:31:17][INFO] Registering route: POST /api/admin/login
[2025-08-01 21:31:17][INFO] Registering route: POST /api/users/login
[2025-08-01 21:31:17][INFO] Registering route: GET /api/auth/captcha
[2025-08-01 21:31:17][INFO] Registering route: GET /api/auth/validate
[2025-08-01 21:31:17][INFO] Registering route: GET /api/users
[2025-08-01 21:31:17][INFO] Registering route: POST /api/users
[2025-08-01 21:31:17][INFO] Registering route: GET /api/users/profile
[2025-08-01 21:31:17][INFO] Registering route: GET /api/users/:id
[2025-08-01 21:31:17][INFO] Registering route: PUT /api/users/:id
[2025-08-01 21:31:17][INFO] Registering route: DELETE /api/users/:id
[2025-08-01 21:31:17][INFO] Registering route: GET /api/stats
[2025-08-01 21:31:17][INFO] Registering route: GET /api/stats/:type
[2025-08-01 21:31:17][INFO] Dispatching request: GET /api/auth/captcha
[2025-08-01 21:31:17][INFO] 数据库连接成功: 127.0.0.1:3306/plb_kj
[2025-08-01 21:31:27][INFO] Request: GET /api/auth/captcha
[2025-08-01 21:31:27][INFO] Registering route: POST /api/admin/login
[2025-08-01 21:31:27][INFO] Registering route: POST /api/users/login
[2025-08-01 21:31:27][INFO] Registering route: GET /api/auth/captcha
[2025-08-01 21:31:27][INFO] Registering route: GET /api/auth/validate
[2025-08-01 21:31:27][INFO] Registering route: GET /api/users
[2025-08-01 21:31:27][INFO] Registering route: POST /api/users
[2025-08-01 21:31:27][INFO] Registering route: GET /api/users/profile
[2025-08-01 21:31:27][INFO] Registering route: GET /api/users/:id
[2025-08-01 21:31:27][INFO] Registering route: PUT /api/users/:id
[2025-08-01 21:31:27][INFO] Registering route: DELETE /api/users/:id
[2025-08-01 21:31:27][INFO] Registering route: GET /api/stats
[2025-08-01 21:31:27][INFO] Registering route: GET /api/stats/:type
[2025-08-01 21:31:27][INFO] Dispatching request: GET /api/auth/captcha
[2025-08-01 21:31:27][INFO] 数据库连接成功: 127.0.0.1:3306/plb_kj
[2025-08-01 21:33:19][INFO] Request: GET /api/auth/captcha
[2025-08-01 21:33:19][INFO] Registering route: POST /api/admin/login
[2025-08-01 21:33:19][INFO] Registering route: POST /api/users/login
[2025-08-01 21:33:19][INFO] Registering route: GET /api/auth/captcha
[2025-08-01 21:33:19][INFO] Registering route: GET /api/auth/validate
[2025-08-01 21:33:19][INFO] Registering route: GET /api/users
[2025-08-01 21:33:19][INFO] Registering route: POST /api/users
[2025-08-01 21:33:19][INFO] Registering route: GET /api/users/profile
[2025-08-01 21:33:19][INFO] Registering route: GET /api/users/:id
[2025-08-01 21:33:19][INFO] Registering route: PUT /api/users/:id
[2025-08-01 21:33:19][INFO] Registering route: DELETE /api/users/:id
[2025-08-01 21:33:19][INFO] Registering route: GET /api/stats
[2025-08-01 21:33:19][INFO] Registering route: GET /api/stats/:type
[2025-08-01 21:33:19][INFO] Dispatching request: GET /api/auth/captcha
[2025-08-01 21:33:19][INFO] 数据库连接成功: 127.0.0.1:3306/plb_kj
[2025-08-01 21:33:21][INFO] Request: GET /api/auth/captcha
[2025-08-01 21:33:21][INFO] Registering route: POST /api/admin/login
[2025-08-01 21:33:21][INFO] Registering route: POST /api/users/login
[2025-08-01 21:33:21][INFO] Registering route: GET /api/auth/captcha
[2025-08-01 21:33:21][INFO] Registering route: GET /api/auth/validate
[2025-08-01 21:33:21][INFO] Registering route: GET /api/users
[2025-08-01 21:33:21][INFO] Registering route: POST /api/users
[2025-08-01 21:33:21][INFO] Registering route: GET /api/users/profile
[2025-08-01 21:33:21][INFO] Registering route: GET /api/users/:id
[2025-08-01 21:33:21][INFO] Registering route: PUT /api/users/:id
[2025-08-01 21:33:21][INFO] Registering route: DELETE /api/users/:id
[2025-08-01 21:33:21][INFO] Registering route: GET /api/stats
[2025-08-01 21:33:21][INFO] Registering route: GET /api/stats/:type
[2025-08-01 21:33:21][INFO] Dispatching request: GET /api/auth/captcha
[2025-08-01 21:33:21][INFO] 数据库连接成功: 127.0.0.1:3306/plb_kj
[2025-08-01 21:33:21][INFO] Request: GET /api/auth/validate
[2025-08-01 21:33:21][INFO] Registering route: POST /api/admin/login
[2025-08-01 21:33:21][INFO] Registering route: POST /api/users/login
[2025-08-01 21:33:21][INFO] Registering route: GET /api/auth/captcha
[2025-08-01 21:33:21][INFO] Registering route: GET /api/auth/validate
[2025-08-01 21:33:21][INFO] Registering route: GET /api/users
[2025-08-01 21:33:21][INFO] Registering route: POST /api/users
[2025-08-01 21:33:21][INFO] Registering route: GET /api/users/profile
[2025-08-01 21:33:21][INFO] Registering route: GET /api/users/:id
[2025-08-01 21:33:21][INFO] Registering route: PUT /api/users/:id
[2025-08-01 21:33:21][INFO] Registering route: DELETE /api/users/:id
[2025-08-01 21:33:21][INFO] Registering route: GET /api/stats
[2025-08-01 21:33:21][INFO] Registering route: GET /api/stats/:type
[2025-08-01 21:33:21][INFO] Dispatching request: GET /api/auth/validate
[2025-08-01 21:33:21][INFO] 数据库连接成功: 127.0.0.1:3306/plb_kj
[2025-08-01 21:33:21][ERROR] Token validation error: 无效的认证令牌
[2025-08-01 21:33:21][INFO] Request: GET /api/auth/captcha
[2025-08-01 21:33:21][INFO] Registering route: POST /api/admin/login
[2025-08-01 21:33:21][INFO] Registering route: POST /api/users/login
[2025-08-01 21:33:21][INFO] Registering route: GET /api/auth/captcha
[2025-08-01 21:33:21][INFO] Registering route: GET /api/auth/validate
[2025-08-01 21:33:21][INFO] Registering route: GET /api/users
[2025-08-01 21:33:21][INFO] Registering route: POST /api/users
[2025-08-01 21:33:21][INFO] Registering route: GET /api/users/profile
[2025-08-01 21:33:21][INFO] Registering route: GET /api/users/:id
[2025-08-01 21:33:21][INFO] Registering route: PUT /api/users/:id
[2025-08-01 21:33:21][INFO] Registering route: DELETE /api/users/:id
[2025-08-01 21:33:21][INFO] Registering route: GET /api/stats
[2025-08-01 21:33:21][INFO] Registering route: GET /api/stats/:type
[2025-08-01 21:33:21][INFO] Dispatching request: GET /api/auth/captcha
[2025-08-01 21:33:21][INFO] 数据库连接成功: 127.0.0.1:3306/plb_kj
[2025-08-01 21:33:23][INFO] Request: GET /api/auth/captcha
[2025-08-01 21:33:23][INFO] Registering route: POST /api/admin/login
[2025-08-01 21:33:23][INFO] Registering route: POST /api/users/login
[2025-08-01 21:33:23][INFO] Registering route: GET /api/auth/captcha
[2025-08-01 21:33:23][INFO] Registering route: GET /api/auth/validate
[2025-08-01 21:33:23][INFO] Registering route: GET /api/users
[2025-08-01 21:33:23][INFO] Registering route: POST /api/users
[2025-08-01 21:33:23][INFO] Registering route: GET /api/users/profile
[2025-08-01 21:33:23][INFO] Registering route: GET /api/users/:id
[2025-08-01 21:33:23][INFO] Registering route: PUT /api/users/:id
[2025-08-01 21:33:23][INFO] Registering route: DELETE /api/users/:id
[2025-08-01 21:33:23][INFO] Registering route: GET /api/stats
[2025-08-01 21:33:23][INFO] Registering route: GET /api/stats/:type
[2025-08-01 21:33:23][INFO] Dispatching request: GET /api/auth/captcha
[2025-08-01 21:33:23][INFO] 数据库连接成功: 127.0.0.1:3306/plb_kj
[2025-08-01 21:33:24][INFO] Request: GET /api/auth/captcha
[2025-08-01 21:33:24][INFO] Registering route: POST /api/admin/login
[2025-08-01 21:33:24][INFO] Registering route: POST /api/users/login
[2025-08-01 21:33:24][INFO] Registering route: GET /api/auth/captcha
[2025-08-01 21:33:24][INFO] Registering route: GET /api/auth/validate
[2025-08-01 21:33:24][INFO] Registering route: GET /api/users
[2025-08-01 21:33:24][INFO] Registering route: POST /api/users
[2025-08-01 21:33:24][INFO] Registering route: GET /api/users/profile
[2025-08-01 21:33:24][INFO] Registering route: GET /api/users/:id
[2025-08-01 21:33:24][INFO] Registering route: PUT /api/users/:id
[2025-08-01 21:33:24][INFO] Registering route: DELETE /api/users/:id
[2025-08-01 21:33:24][INFO] Registering route: GET /api/stats
[2025-08-01 21:33:24][INFO] Registering route: GET /api/stats/:type
[2025-08-01 21:33:24][INFO] Dispatching request: GET /api/auth/captcha
[2025-08-01 21:33:24][INFO] 数据库连接成功: 127.0.0.1:3306/plb_kj
[2025-08-01 21:33:25][INFO] Request: GET /api/auth/captcha
[2025-08-01 21:33:25][INFO] Registering route: POST /api/admin/login
[2025-08-01 21:33:25][INFO] Registering route: POST /api/users/login
[2025-08-01 21:33:25][INFO] Registering route: GET /api/auth/captcha
[2025-08-01 21:33:25][INFO] Registering route: GET /api/auth/validate
[2025-08-01 21:33:25][INFO] Registering route: GET /api/users
[2025-08-01 21:33:25][INFO] Registering route: POST /api/users
[2025-08-01 21:33:25][INFO] Registering route: GET /api/users/profile
[2025-08-01 21:33:25][INFO] Registering route: GET /api/users/:id
[2025-08-01 21:33:25][INFO] Registering route: PUT /api/users/:id
[2025-08-01 21:33:25][INFO] Registering route: DELETE /api/users/:id
[2025-08-01 21:33:25][INFO] Registering route: GET /api/stats
[2025-08-01 21:33:25][INFO] Registering route: GET /api/stats/:type
[2025-08-01 21:33:25][INFO] Dispatching request: GET /api/auth/captcha
[2025-08-01 21:33:25][INFO] 数据库连接成功: 127.0.0.1:3306/plb_kj
[2025-08-01 21:33:26][INFO] Request: GET /api/auth/captcha
[2025-08-01 21:33:26][INFO] Registering route: POST /api/admin/login
[2025-08-01 21:33:26][INFO] Registering route: POST /api/users/login
[2025-08-01 21:33:26][INFO] Registering route: GET /api/auth/captcha
[2025-08-01 21:33:26][INFO] Registering route: GET /api/auth/validate
[2025-08-01 21:33:26][INFO] Registering route: GET /api/users
[2025-08-01 21:33:26][INFO] Registering route: POST /api/users
[2025-08-01 21:33:26][INFO] Registering route: GET /api/users/profile
[2025-08-01 21:33:26][INFO] Registering route: GET /api/users/:id
[2025-08-01 21:33:26][INFO] Registering route: PUT /api/users/:id
[2025-08-01 21:33:26][INFO] Registering route: DELETE /api/users/:id
[2025-08-01 21:33:26][INFO] Registering route: GET /api/stats
[2025-08-01 21:33:26][INFO] Registering route: GET /api/stats/:type
[2025-08-01 21:33:26][INFO] Dispatching request: GET /api/auth/captcha
[2025-08-01 21:33:26][INFO] 数据库连接成功: 127.0.0.1:3306/plb_kj
[2025-08-01 21:34:24][INFO] Request: GET /api/auth/captcha
[2025-08-01 21:34:24][INFO] Registering route: POST /api/admin/login
[2025-08-01 21:34:24][INFO] Registering route: POST /api/users/login
[2025-08-01 21:34:24][INFO] Registering route: GET /api/auth/captcha
[2025-08-01 21:34:24][INFO] Registering route: GET /api/auth/validate
[2025-08-01 21:34:24][INFO] Registering route: GET /api/users
[2025-08-01 21:34:24][INFO] Registering route: POST /api/users
[2025-08-01 21:34:24][INFO] Registering route: GET /api/users/profile
[2025-08-01 21:34:24][INFO] Registering route: GET /api/users/:id
[2025-08-01 21:34:24][INFO] Registering route: PUT /api/users/:id
[2025-08-01 21:34:24][INFO] Registering route: DELETE /api/users/:id
[2025-08-01 21:34:24][INFO] Registering route: GET /api/stats
[2025-08-01 21:34:24][INFO] Registering route: GET /api/stats/:type
[2025-08-01 21:34:24][INFO] Dispatching request: GET /api/auth/captcha
[2025-08-01 21:34:24][INFO] 数据库连接成功: 127.0.0.1:3306/plb_kj
[2025-08-01 21:34:25][INFO] Request: GET /api/auth/captcha
[2025-08-01 21:34:25][INFO] Registering route: POST /api/admin/login
[2025-08-01 21:34:25][INFO] Registering route: POST /api/users/login
[2025-08-01 21:34:25][INFO] Registering route: GET /api/auth/captcha
[2025-08-01 21:34:25][INFO] Registering route: GET /api/auth/validate
[2025-08-01 21:34:25][INFO] Registering route: GET /api/users
[2025-08-01 21:34:25][INFO] Registering route: POST /api/users
[2025-08-01 21:34:25][INFO] Registering route: GET /api/users/profile
[2025-08-01 21:34:25][INFO] Registering route: GET /api/users/:id
[2025-08-01 21:34:25][INFO] Registering route: PUT /api/users/:id
[2025-08-01 21:34:25][INFO] Registering route: DELETE /api/users/:id
[2025-08-01 21:34:25][INFO] Registering route: GET /api/stats
[2025-08-01 21:34:25][INFO] Registering route: GET /api/stats/:type
[2025-08-01 21:34:25][INFO] Dispatching request: GET /api/auth/captcha
[2025-08-01 21:34:25][INFO] 数据库连接成功: 127.0.0.1:3306/plb_kj
[2025-08-01 21:34:25][INFO] Request: GET /api/auth/captcha
[2025-08-01 21:34:25][INFO] Registering route: POST /api/admin/login
[2025-08-01 21:34:25][INFO] Registering route: POST /api/users/login
[2025-08-01 21:34:25][INFO] Registering route: GET /api/auth/captcha
[2025-08-01 21:34:25][INFO] Registering route: GET /api/auth/validate
[2025-08-01 21:34:25][INFO] Registering route: GET /api/users
[2025-08-01 21:34:25][INFO] Registering route: POST /api/users
[2025-08-01 21:34:25][INFO] Registering route: GET /api/users/profile
[2025-08-01 21:34:25][INFO] Registering route: GET /api/users/:id
[2025-08-01 21:34:25][INFO] Registering route: PUT /api/users/:id
[2025-08-01 21:34:25][INFO] Registering route: DELETE /api/users/:id
[2025-08-01 21:34:25][INFO] Registering route: GET /api/stats
[2025-08-01 21:34:25][INFO] Registering route: GET /api/stats/:type
[2025-08-01 21:34:25][INFO] Dispatching request: GET /api/auth/captcha
[2025-08-01 21:34:25][INFO] 数据库连接成功: 127.0.0.1:3306/plb_kj
[2025-08-01 21:36:58][INFO] Request: GET /api/auth/captcha
[2025-08-01 21:36:58][INFO] Registering route: POST /api/admin/login
[2025-08-01 21:36:58][INFO] Registering route: POST /api/users/login
[2025-08-01 21:36:58][INFO] Registering route: GET /api/auth/captcha
[2025-08-01 21:36:58][INFO] Registering route: GET /api/auth/validate
[2025-08-01 21:36:58][INFO] Registering route: GET /api/users
[2025-08-01 21:36:58][INFO] Registering route: POST /api/users
[2025-08-01 21:36:58][INFO] Registering route: GET /api/users/profile
[2025-08-01 21:36:58][INFO] Registering route: GET /api/users/:id
[2025-08-01 21:36:58][INFO] Registering route: PUT /api/users/:id
[2025-08-01 21:36:58][INFO] Registering route: DELETE /api/users/:id
[2025-08-01 21:36:58][INFO] Registering route: GET /api/stats
[2025-08-01 21:36:58][INFO] Registering route: GET /api/stats/:type
[2025-08-01 21:36:58][INFO] Dispatching request: GET /api/auth/captcha
[2025-08-01 21:36:58][INFO] 数据库连接成功: 127.0.0.1:3306/plb_kj
[2025-08-01 21:37:00][INFO] Request: GET /api/auth/validate
[2025-08-01 21:37:00][INFO] Registering route: POST /api/admin/login
[2025-08-01 21:37:00][INFO] Registering route: POST /api/users/login
[2025-08-01 21:37:00][INFO] Registering route: GET /api/auth/captcha
[2025-08-01 21:37:00][INFO] Registering route: GET /api/auth/validate
[2025-08-01 21:37:00][INFO] Registering route: GET /api/users
[2025-08-01 21:37:00][INFO] Registering route: POST /api/users
[2025-08-01 21:37:00][INFO] Registering route: GET /api/users/profile
[2025-08-01 21:37:00][INFO] Registering route: GET /api/users/:id
[2025-08-01 21:37:00][INFO] Registering route: PUT /api/users/:id
[2025-08-01 21:37:00][INFO] Registering route: DELETE /api/users/:id
[2025-08-01 21:37:00][INFO] Registering route: GET /api/stats
[2025-08-01 21:37:00][INFO] Registering route: GET /api/stats/:type
[2025-08-01 21:37:00][INFO] Dispatching request: GET /api/auth/validate
[2025-08-01 21:37:00][INFO] 数据库连接成功: 127.0.0.1:3306/plb_kj
[2025-08-01 21:37:00][ERROR] Token validation error: 无效的认证令牌
[2025-08-01 21:37:00][INFO] Request: GET /api/auth/captcha
[2025-08-01 21:37:00][INFO] Registering route: POST /api/admin/login
[2025-08-01 21:37:00][INFO] Registering route: POST /api/users/login
[2025-08-01 21:37:00][INFO] Registering route: GET /api/auth/captcha
[2025-08-01 21:37:00][INFO] Registering route: GET /api/auth/validate
[2025-08-01 21:37:00][INFO] Registering route: GET /api/users
[2025-08-01 21:37:00][INFO] Registering route: POST /api/users
[2025-08-01 21:37:00][INFO] Registering route: GET /api/users/profile
[2025-08-01 21:37:00][INFO] Registering route: GET /api/users/:id
[2025-08-01 21:37:00][INFO] Registering route: PUT /api/users/:id
[2025-08-01 21:37:00][INFO] Registering route: DELETE /api/users/:id
[2025-08-01 21:37:00][INFO] Registering route: GET /api/stats
[2025-08-01 21:37:00][INFO] Registering route: GET /api/stats/:type
[2025-08-01 21:37:00][INFO] Dispatching request: GET /api/auth/captcha
[2025-08-01 21:37:00][INFO] 数据库连接成功: 127.0.0.1:3306/plb_kj
[2025-08-01 21:57:24][INFO] Request: GET /api/auth/validate
[2025-08-01 21:57:24][INFO] Registering route: POST /api/admin/login
[2025-08-01 21:57:24][INFO] Registering route: POST /api/users/login
[2025-08-01 21:57:24][INFO] Registering route: GET /api/auth/captcha
[2025-08-01 21:57:24][INFO] Registering route: GET /api/auth/validate
[2025-08-01 21:57:24][INFO] Registering route: GET /api/users
[2025-08-01 21:57:24][INFO] Registering route: POST /api/users
[2025-08-01 21:57:24][INFO] Registering route: GET /api/users/profile
[2025-08-01 21:57:24][INFO] Registering route: GET /api/users/:id
[2025-08-01 21:57:24][INFO] Registering route: PUT /api/users/:id
[2025-08-01 21:57:24][INFO] Registering route: DELETE /api/users/:id
[2025-08-01 21:57:24][INFO] Registering route: GET /api/stats
[2025-08-01 21:57:24][INFO] Registering route: GET /api/stats/:type
[2025-08-01 21:57:24][INFO] Dispatching request: GET /api/auth/validate
[2025-08-01 21:57:24][INFO] 数据库连接成功: 127.0.0.1:3306/plb_kj
[2025-08-01 21:57:24][ERROR] Token validation error: 无效的认证令牌
[2025-08-01 22:15:48][INFO] Request: GET /api/auth/captcha
[2025-08-01 22:15:48][INFO] Registering route: POST /api/admin/login
[2025-08-01 22:15:48][INFO] Registering route: POST /api/users/login
[2025-08-01 22:15:48][INFO] Registering route: GET /api/auth/captcha
[2025-08-01 22:15:48][INFO] Registering route: GET /api/auth/validate
[2025-08-01 22:15:48][INFO] Registering route: GET /api/users
[2025-08-01 22:15:48][INFO] Registering route: POST /api/users
[2025-08-01 22:15:48][INFO] Registering route: GET /api/users/profile
[2025-08-01 22:15:48][INFO] Registering route: GET /api/users/:id
[2025-08-01 22:15:48][INFO] Registering route: PUT /api/users/:id
[2025-08-01 22:15:48][INFO] Registering route: DELETE /api/users/:id
[2025-08-01 22:15:48][INFO] Registering route: GET /api/stats
[2025-08-01 22:15:48][INFO] Registering route: GET /api/stats/:type
[2025-08-01 22:15:48][INFO] Dispatching request: GET /api/auth/captcha
[2025-08-01 22:15:48][INFO] 数据库连接成功: 127.0.0.1:3306/plb_kj
[2025-08-01 22:15:50][INFO] Request: GET /api/auth/validate
[2025-08-01 22:15:50][INFO] Registering route: POST /api/admin/login
[2025-08-01 22:15:50][INFO] Registering route: POST /api/users/login
[2025-08-01 22:15:50][INFO] Registering route: GET /api/auth/captcha
[2025-08-01 22:15:50][INFO] Registering route: GET /api/auth/validate
[2025-08-01 22:15:50][INFO] Registering route: GET /api/users
[2025-08-01 22:15:50][INFO] Registering route: POST /api/users
[2025-08-01 22:15:50][INFO] Registering route: GET /api/users/profile
[2025-08-01 22:15:50][INFO] Registering route: GET /api/users/:id
[2025-08-01 22:15:50][INFO] Registering route: PUT /api/users/:id
[2025-08-01 22:15:50][INFO] Registering route: DELETE /api/users/:id
[2025-08-01 22:15:50][INFO] Registering route: GET /api/stats
[2025-08-01 22:15:50][INFO] Registering route: GET /api/stats/:type
[2025-08-01 22:15:50][INFO] Dispatching request: GET /api/auth/validate
[2025-08-01 22:15:50][INFO] 数据库连接成功: 127.0.0.1:3306/plb_kj
[2025-08-01 22:15:50][ERROR] Token validation error: 无效的认证令牌
[2025-08-01 22:15:50][INFO] Request: GET /api/auth/captcha
[2025-08-01 22:15:50][INFO] Registering route: POST /api/admin/login
[2025-08-01 22:15:50][INFO] Registering route: POST /api/users/login
[2025-08-01 22:15:50][INFO] Registering route: GET /api/auth/captcha
[2025-08-01 22:15:50][INFO] Registering route: GET /api/auth/validate
[2025-08-01 22:15:50][INFO] Registering route: GET /api/users
[2025-08-01 22:15:50][INFO] Registering route: POST /api/users
[2025-08-01 22:15:50][INFO] Registering route: GET /api/users/profile
[2025-08-01 22:15:50][INFO] Registering route: GET /api/users/:id
[2025-08-01 22:15:50][INFO] Registering route: PUT /api/users/:id
[2025-08-01 22:15:50][INFO] Registering route: DELETE /api/users/:id
[2025-08-01 22:15:50][INFO] Registering route: GET /api/stats
[2025-08-01 22:15:50][INFO] Registering route: GET /api/stats/:type
[2025-08-01 22:15:50][INFO] Dispatching request: GET /api/auth/captcha
[2025-08-01 22:15:50][INFO] 数据库连接成功: 127.0.0.1:3306/plb_kj
[2025-08-01 22:31:29][INFO] Request: GET /api/auth/validate
[2025-08-01 22:31:29][INFO] Registering route: POST /api/admin/login
[2025-08-01 22:31:29][INFO] Registering route: POST /api/users/login
[2025-08-01 22:31:29][INFO] Registering route: GET /api/auth/captcha
[2025-08-01 22:31:29][INFO] Registering route: GET /api/auth/validate
[2025-08-01 22:31:29][INFO] Registering route: GET /api/users
[2025-08-01 22:31:29][INFO] Registering route: POST /api/users
[2025-08-01 22:31:29][INFO] Registering route: GET /api/users/profile
[2025-08-01 22:31:29][INFO] Registering route: GET /api/users/:id
[2025-08-01 22:31:29][INFO] Registering route: PUT /api/users/:id
[2025-08-01 22:31:29][INFO] Registering route: DELETE /api/users/:id
[2025-08-01 22:31:29][INFO] Registering route: GET /api/stats
[2025-08-01 22:31:29][INFO] Registering route: GET /api/stats/:type
[2025-08-01 22:31:29][INFO] Dispatching request: GET /api/auth/validate
[2025-08-01 22:31:29][INFO] 数据库连接成功: 127.0.0.1:3306/plb_kj
[2025-08-01 22:31:29][ERROR] Token validation error: 无效的认证令牌
[2025-08-01 22:56:29][INFO] Request: GET /api/auth/validate
[2025-08-01 22:56:29][INFO] Registering route: POST /api/admin/login
[2025-08-01 22:56:29][INFO] Registering route: POST /api/users/login
[2025-08-01 22:56:29][INFO] Registering route: GET /api/auth/captcha
[2025-08-01 22:56:29][INFO] Registering route: GET /api/auth/validate
[2025-08-01 22:56:29][INFO] Registering route: GET /api/users
[2025-08-01 22:56:29][INFO] Registering route: POST /api/users
[2025-08-01 22:56:29][INFO] Registering route: GET /api/users/profile
[2025-08-01 22:56:29][INFO] Registering route: GET /api/users/:id
[2025-08-01 22:56:29][INFO] Registering route: PUT /api/users/:id
[2025-08-01 22:56:29][INFO] Registering route: DELETE /api/users/:id
[2025-08-01 22:56:29][INFO] Registering route: GET /api/stats
[2025-08-01 22:56:29][INFO] Registering route: GET /api/stats/:type
[2025-08-01 22:56:29][INFO] Dispatching request: GET /api/auth/validate
[2025-08-01 22:56:29][INFO] 数据库连接成功: 127.0.0.1:3306/plb_kj
[2025-08-01 22:56:30][ERROR] Token validation error: 无效的认证令牌
[2025-08-01 23:15:37][INFO] Request: GET /api/auth/captcha
[2025-08-01 23:15:37][INFO] Registering route: POST /api/admin/login
[2025-08-01 23:15:37][INFO] Registering route: POST /api/users/login
[2025-08-01 23:15:37][INFO] Registering route: GET /api/auth/captcha
[2025-08-01 23:15:37][INFO] Registering route: GET /api/auth/validate
[2025-08-01 23:15:37][INFO] Registering route: GET /api/users
[2025-08-01 23:15:37][INFO] Registering route: POST /api/users
[2025-08-01 23:15:37][INFO] Registering route: GET /api/users/profile
[2025-08-01 23:15:37][INFO] Registering route: GET /api/users/:id
[2025-08-01 23:15:37][INFO] Registering route: PUT /api/users/:id
[2025-08-01 23:15:37][INFO] Registering route: DELETE /api/users/:id
[2025-08-01 23:15:37][INFO] Registering route: GET /api/stats
[2025-08-01 23:15:37][INFO] Registering route: GET /api/stats/:type
[2025-08-01 23:15:37][INFO] Dispatching request: GET /api/auth/captcha
[2025-08-01 23:15:37][INFO] 数据库连接成功: 127.0.0.1:3306/plb_kj
[2025-08-01 23:15:39][INFO] Request: GET /api/auth/validate
[2025-08-01 23:15:39][INFO] Registering route: POST /api/admin/login
[2025-08-01 23:15:39][INFO] Registering route: POST /api/users/login
[2025-08-01 23:15:39][INFO] Registering route: GET /api/auth/captcha
[2025-08-01 23:15:39][INFO] Registering route: GET /api/auth/validate
[2025-08-01 23:15:39][INFO] Registering route: GET /api/users
[2025-08-01 23:15:39][INFO] Registering route: POST /api/users
[2025-08-01 23:15:39][INFO] Registering route: GET /api/users/profile
[2025-08-01 23:15:39][INFO] Registering route: GET /api/users/:id
[2025-08-01 23:15:39][INFO] Registering route: PUT /api/users/:id
[2025-08-01 23:15:39][INFO] Registering route: DELETE /api/users/:id
[2025-08-01 23:15:39][INFO] Registering route: GET /api/stats
[2025-08-01 23:15:39][INFO] Registering route: GET /api/stats/:type
[2025-08-01 23:15:39][INFO] Dispatching request: GET /api/auth/validate
[2025-08-01 23:15:39][INFO] 数据库连接成功: 127.0.0.1:3306/plb_kj
[2025-08-01 23:15:39][ERROR] Token validation error: 无效的认证令牌
[2025-08-01 23:15:39][INFO] Request: GET /api/auth/captcha
[2025-08-01 23:15:39][INFO] Registering route: POST /api/admin/login
[2025-08-01 23:15:39][INFO] Registering route: POST /api/users/login
[2025-08-01 23:15:39][INFO] Registering route: GET /api/auth/captcha
[2025-08-01 23:15:39][INFO] Registering route: GET /api/auth/validate
[2025-08-01 23:15:39][INFO] Registering route: GET /api/users
[2025-08-01 23:15:39][INFO] Registering route: POST /api/users
[2025-08-01 23:15:39][INFO] Registering route: GET /api/users/profile
[2025-08-01 23:15:39][INFO] Registering route: GET /api/users/:id
[2025-08-01 23:15:39][INFO] Registering route: PUT /api/users/:id
[2025-08-01 23:15:39][INFO] Registering route: DELETE /api/users/:id
[2025-08-01 23:15:39][INFO] Registering route: GET /api/stats
[2025-08-01 23:15:39][INFO] Registering route: GET /api/stats/:type
[2025-08-01 23:15:39][INFO] Dispatching request: GET /api/auth/captcha
[2025-08-01 23:15:39][INFO] 数据库连接成功: 127.0.0.1:3306/plb_kj
[2025-08-01 23:28:38][INFO] Request: GET /api/auth/validate
[2025-08-01 23:28:38][INFO] Registering route: POST /api/admin/login
[2025-08-01 23:28:38][INFO] Registering route: POST /api/users/login
[2025-08-01 23:28:38][INFO] Registering route: GET /api/auth/captcha
[2025-08-01 23:28:38][INFO] Registering route: GET /api/auth/validate
[2025-08-01 23:28:38][INFO] Registering route: GET /api/users
[2025-08-01 23:28:38][INFO] Registering route: POST /api/users
[2025-08-01 23:28:38][INFO] Registering route: GET /api/users/profile
[2025-08-01 23:28:38][INFO] Registering route: GET /api/users/:id
[2025-08-01 23:28:38][INFO] Registering route: PUT /api/users/:id
[2025-08-01 23:28:38][INFO] Registering route: DELETE /api/users/:id
[2025-08-01 23:28:38][INFO] Registering route: GET /api/stats
[2025-08-01 23:28:38][INFO] Registering route: GET /api/stats/:type
[2025-08-01 23:28:38][INFO] Dispatching request: GET /api/auth/validate
[2025-08-01 23:28:38][INFO] 数据库连接成功: 127.0.0.1:3306/plb_kj
[2025-08-01 23:28:38][ERROR] Token validation error: 无效的认证令牌
[2025-08-01 23:30:09][INFO] Request: GET /api/auth/validate
[2025-08-01 23:30:09][INFO] Registering route: POST /api/admin/login
[2025-08-01 23:30:09][INFO] Registering route: POST /api/users/login
[2025-08-01 23:30:09][INFO] Registering route: GET /api/auth/captcha
[2025-08-01 23:30:09][INFO] Registering route: GET /api/auth/validate
[2025-08-01 23:30:09][INFO] Registering route: GET /api/users
[2025-08-01 23:30:09][INFO] Registering route: POST /api/users
[2025-08-01 23:30:09][INFO] Registering route: GET /api/users/profile
[2025-08-01 23:30:09][INFO] Registering route: GET /api/users/:id
[2025-08-01 23:30:09][INFO] Registering route: PUT /api/users/:id
[2025-08-01 23:30:09][INFO] Registering route: DELETE /api/users/:id
[2025-08-01 23:30:09][INFO] Registering route: GET /api/stats
[2025-08-01 23:30:09][INFO] Registering route: GET /api/stats/:type
[2025-08-01 23:30:09][INFO] Dispatching request: GET /api/auth/validate
[2025-08-01 23:30:09][INFO] 数据库连接成功: 127.0.0.1:3306/plb_kj
[2025-08-01 23:30:09][ERROR] Token validation error: 无效的认证令牌
[2025-08-01 23:41:14][INFO] Request: GET /api/auth/validate
[2025-08-01 23:41:14][INFO] Registering route: POST /api/admin/login
[2025-08-01 23:41:14][INFO] Registering route: POST /api/users/login
[2025-08-01 23:41:14][INFO] Registering route: GET /api/auth/captcha
[2025-08-01 23:41:14][INFO] Registering route: GET /api/auth/validate
[2025-08-01 23:41:14][INFO] Registering route: GET /api/users
[2025-08-01 23:41:14][INFO] Registering route: POST /api/users
[2025-08-01 23:41:14][INFO] Registering route: GET /api/users/profile
[2025-08-01 23:41:14][INFO] Registering route: GET /api/users/:id
[2025-08-01 23:41:14][INFO] Registering route: PUT /api/users/:id
[2025-08-01 23:41:14][INFO] Registering route: DELETE /api/users/:id
[2025-08-01 23:41:14][INFO] Registering route: GET /api/stats
[2025-08-01 23:41:14][INFO] Registering route: GET /api/stats/:type
[2025-08-01 23:41:14][INFO] Dispatching request: GET /api/auth/validate
[2025-08-01 23:41:14][INFO] 数据库连接成功: 127.0.0.1:3306/plb_kj
[2025-08-01 23:41:14][ERROR] Token validation error: 无效的认证令牌
[2025-08-02 00:00:02][INFO] Request: GET /api/auth/validate
[2025-08-02 00:00:02][INFO] Registering route: POST /api/admin/login
[2025-08-02 00:00:02][INFO] Registering route: POST /api/users/login
[2025-08-02 00:00:02][INFO] Registering route: GET /api/auth/captcha
[2025-08-02 00:00:02][INFO] Registering route: GET /api/auth/validate
[2025-08-02 00:00:02][INFO] Registering route: GET /api/users
[2025-08-02 00:00:02][INFO] Registering route: POST /api/users
[2025-08-02 00:00:02][INFO] Registering route: GET /api/users/profile
[2025-08-02 00:00:02][INFO] Registering route: GET /api/users/:id
[2025-08-02 00:00:02][INFO] Registering route: PUT /api/users/:id
[2025-08-02 00:00:02][INFO] Registering route: DELETE /api/users/:id
[2025-08-02 00:00:02][INFO] Registering route: GET /api/stats
[2025-08-02 00:00:02][INFO] Registering route: GET /api/stats/:type
[2025-08-02 00:00:02][INFO] Dispatching request: GET /api/auth/validate
[2025-08-02 00:00:02][INFO] 数据库连接成功: 127.0.0.1:3306/plb_kj
[2025-08-02 00:00:02][ERROR] Token validation error: 无效的认证令牌
[2025-08-02 00:31:10][INFO] Request: GET /api/auth/validate
[2025-08-02 00:31:10][INFO] Registering route: POST /api/admin/login
[2025-08-02 00:31:10][INFO] Registering route: POST /api/users/login
[2025-08-02 00:31:10][INFO] Registering route: GET /api/auth/captcha
[2025-08-02 00:31:10][INFO] Registering route: GET /api/auth/validate
[2025-08-02 00:31:10][INFO] Registering route: GET /api/users
[2025-08-02 00:31:10][INFO] Registering route: POST /api/users
[2025-08-02 00:31:10][INFO] Registering route: GET /api/users/profile
[2025-08-02 00:31:10][INFO] Registering route: GET /api/users/:id
[2025-08-02 00:31:10][INFO] Registering route: PUT /api/users/:id
[2025-08-02 00:31:10][INFO] Registering route: DELETE /api/users/:id
[2025-08-02 00:31:10][INFO] Registering route: GET /api/stats
[2025-08-02 00:31:10][INFO] Registering route: GET /api/stats/:type
[2025-08-02 00:31:10][INFO] Dispatching request: GET /api/auth/validate
[2025-08-02 00:31:10][INFO] 数据库连接成功: 127.0.0.1:3306/plb_kj
[2025-08-02 00:31:10][ERROR] Token validation error: 无效的认证令牌
[2025-08-02 00:31:34][INFO] Request: GET /api/auth/validate
[2025-08-02 00:31:34][INFO] Registering route: POST /api/admin/login
[2025-08-02 00:31:34][INFO] Registering route: POST /api/users/login
[2025-08-02 00:31:34][INFO] Registering route: GET /api/auth/captcha
[2025-08-02 00:31:34][INFO] Registering route: GET /api/auth/validate
[2025-08-02 00:31:34][INFO] Registering route: GET /api/users
[2025-08-02 00:31:34][INFO] Registering route: POST /api/users
[2025-08-02 00:31:34][INFO] Registering route: GET /api/users/profile
[2025-08-02 00:31:34][INFO] Registering route: GET /api/users/:id
[2025-08-02 00:31:34][INFO] Registering route: PUT /api/users/:id
[2025-08-02 00:31:34][INFO] Registering route: DELETE /api/users/:id
[2025-08-02 00:31:34][INFO] Registering route: GET /api/stats
[2025-08-02 00:31:34][INFO] Registering route: GET /api/stats/:type
[2025-08-02 00:31:34][INFO] Dispatching request: GET /api/auth/validate
[2025-08-02 00:31:34][INFO] 数据库连接成功: 127.0.0.1:3306/plb_kj
[2025-08-02 00:31:34][ERROR] Token validation error: 无效的认证令牌
[2025-08-02 01:13:28][INFO] Request: GET /api/auth/captcha
[2025-08-02 01:13:28][INFO] Registering route: POST /api/admin/login
[2025-08-02 01:13:28][INFO] Registering route: POST /api/users/login
[2025-08-02 01:13:28][INFO] Registering route: GET /api/auth/captcha
[2025-08-02 01:13:28][INFO] Registering route: GET /api/auth/validate
[2025-08-02 01:13:28][INFO] Registering route: GET /api/users
[2025-08-02 01:13:28][INFO] Registering route: POST /api/users
[2025-08-02 01:13:28][INFO] Registering route: GET /api/users/profile
[2025-08-02 01:13:28][INFO] Registering route: GET /api/users/:id
[2025-08-02 01:13:28][INFO] Registering route: PUT /api/users/:id
[2025-08-02 01:13:28][INFO] Registering route: DELETE /api/users/:id
[2025-08-02 01:13:28][INFO] Registering route: GET /api/stats
[2025-08-02 01:13:28][INFO] Registering route: GET /api/stats/:type
[2025-08-02 01:13:28][INFO] Dispatching request: GET /api/auth/captcha
[2025-08-02 01:13:28][INFO] 数据库连接成功: 127.0.0.1:3306/plb_kj
[2025-08-02 01:13:30][INFO] Request: GET /api/auth/validate
[2025-08-02 01:13:30][INFO] Registering route: POST /api/admin/login
[2025-08-02 01:13:30][INFO] Registering route: POST /api/users/login
[2025-08-02 01:13:30][INFO] Registering route: GET /api/auth/captcha
[2025-08-02 01:13:30][INFO] Registering route: GET /api/auth/validate
[2025-08-02 01:13:30][INFO] Registering route: GET /api/users
[2025-08-02 01:13:30][INFO] Registering route: POST /api/users
[2025-08-02 01:13:30][INFO] Registering route: GET /api/users/profile
[2025-08-02 01:13:30][INFO] Registering route: GET /api/users/:id
[2025-08-02 01:13:30][INFO] Registering route: PUT /api/users/:id
[2025-08-02 01:13:30][INFO] Registering route: DELETE /api/users/:id
[2025-08-02 01:13:30][INFO] Registering route: GET /api/stats
[2025-08-02 01:13:30][INFO] Registering route: GET /api/stats/:type
[2025-08-02 01:13:30][INFO] Dispatching request: GET /api/auth/validate
[2025-08-02 01:13:30][INFO] 数据库连接成功: 127.0.0.1:3306/plb_kj
[2025-08-02 01:13:30][ERROR] Token validation error: 无效的认证令牌
[2025-08-02 01:13:30][INFO] Request: GET /api/auth/captcha
[2025-08-02 01:13:30][INFO] Registering route: POST /api/admin/login
[2025-08-02 01:13:30][INFO] Registering route: POST /api/users/login
[2025-08-02 01:13:30][INFO] Registering route: GET /api/auth/captcha
[2025-08-02 01:13:30][INFO] Registering route: GET /api/auth/validate
[2025-08-02 01:13:30][INFO] Registering route: GET /api/users
[2025-08-02 01:13:30][INFO] Registering route: POST /api/users
[2025-08-02 01:13:30][INFO] Registering route: GET /api/users/profile
[2025-08-02 01:13:30][INFO] Registering route: GET /api/users/:id
[2025-08-02 01:13:30][INFO] Registering route: PUT /api/users/:id
[2025-08-02 01:13:30][INFO] Registering route: DELETE /api/users/:id
[2025-08-02 01:13:30][INFO] Registering route: GET /api/stats
[2025-08-02 01:13:30][INFO] Registering route: GET /api/stats/:type
[2025-08-02 01:13:30][INFO] Dispatching request: GET /api/auth/captcha
[2025-08-02 01:13:30][INFO] 数据库连接成功: 127.0.0.1:3306/plb_kj
[2025-08-02 01:17:49][INFO] Request: GET /api/auth/captcha
[2025-08-02 01:17:49][INFO] Registering route: POST /api/admin/login
[2025-08-02 01:17:49][INFO] Registering route: POST /api/users/login
[2025-08-02 01:17:49][INFO] Registering route: GET /api/auth/captcha
[2025-08-02 01:17:49][INFO] Registering route: GET /api/auth/validate
[2025-08-02 01:17:49][INFO] Registering route: GET /api/users
[2025-08-02 01:17:49][INFO] Registering route: POST /api/users
[2025-08-02 01:17:49][INFO] Registering route: GET /api/users/profile
[2025-08-02 01:17:49][INFO] Registering route: GET /api/users/:id
[2025-08-02 01:17:49][INFO] Registering route: PUT /api/users/:id
[2025-08-02 01:17:49][INFO] Registering route: DELETE /api/users/:id
[2025-08-02 01:17:49][INFO] Registering route: GET /api/stats
[2025-08-02 01:17:49][INFO] Registering route: GET /api/stats/:type
[2025-08-02 01:17:49][INFO] Dispatching request: GET /api/auth/captcha
[2025-08-02 01:17:49][INFO] 数据库连接成功: 127.0.0.1:3306/plb_kj
[2025-08-02 01:17:51][INFO] Request: GET /api/auth/validate
[2025-08-02 01:17:51][INFO] Registering route: POST /api/admin/login
[2025-08-02 01:17:51][INFO] Registering route: POST /api/users/login
[2025-08-02 01:17:51][INFO] Registering route: GET /api/auth/captcha
[2025-08-02 01:17:51][INFO] Registering route: GET /api/auth/validate
[2025-08-02 01:17:51][INFO] Registering route: GET /api/users
[2025-08-02 01:17:51][INFO] Registering route: POST /api/users
[2025-08-02 01:17:51][INFO] Registering route: GET /api/users/profile
[2025-08-02 01:17:51][INFO] Registering route: GET /api/users/:id
[2025-08-02 01:17:51][INFO] Registering route: PUT /api/users/:id
[2025-08-02 01:17:51][INFO] Registering route: DELETE /api/users/:id
[2025-08-02 01:17:51][INFO] Registering route: GET /api/stats
[2025-08-02 01:17:51][INFO] Registering route: GET /api/stats/:type
[2025-08-02 01:17:51][INFO] Dispatching request: GET /api/auth/validate
[2025-08-02 01:17:51][INFO] 数据库连接成功: 127.0.0.1:3306/plb_kj
[2025-08-02 01:17:51][ERROR] Token validation error: 无效的认证令牌
[2025-08-02 01:17:51][INFO] Request: GET /api/auth/captcha
[2025-08-02 01:17:51][INFO] Registering route: POST /api/admin/login
[2025-08-02 01:17:51][INFO] Registering route: POST /api/users/login
[2025-08-02 01:17:51][INFO] Registering route: GET /api/auth/captcha
[2025-08-02 01:17:51][INFO] Registering route: GET /api/auth/validate
[2025-08-02 01:17:51][INFO] Registering route: GET /api/users
[2025-08-02 01:17:51][INFO] Registering route: POST /api/users
[2025-08-02 01:17:51][INFO] Registering route: GET /api/users/profile
[2025-08-02 01:17:51][INFO] Registering route: GET /api/users/:id
[2025-08-02 01:17:51][INFO] Registering route: PUT /api/users/:id
[2025-08-02 01:17:51][INFO] Registering route: DELETE /api/users/:id
[2025-08-02 01:17:51][INFO] Registering route: GET /api/stats
[2025-08-02 01:17:51][INFO] Registering route: GET /api/stats/:type
[2025-08-02 01:17:51][INFO] Dispatching request: GET /api/auth/captcha
[2025-08-02 01:17:51][INFO] 数据库连接成功: 127.0.0.1:3306/plb_kj
[2025-08-02 01:32:24][INFO] Request: GET /api/auth/captcha
[2025-08-02 01:32:24][INFO] Registering route: POST /api/admin/login
[2025-08-02 01:32:24][INFO] Registering route: POST /api/users/login
[2025-08-02 01:32:24][INFO] Registering route: GET /api/auth/captcha
[2025-08-02 01:32:24][INFO] Registering route: GET /api/auth/validate
[2025-08-02 01:32:24][INFO] Registering route: GET /api/users
[2025-08-02 01:32:24][INFO] Registering route: POST /api/users
[2025-08-02 01:32:24][INFO] Registering route: GET /api/users/profile
[2025-08-02 01:32:24][INFO] Registering route: GET /api/users/:id
[2025-08-02 01:32:24][INFO] Registering route: PUT /api/users/:id
[2025-08-02 01:32:24][INFO] Registering route: DELETE /api/users/:id
[2025-08-02 01:32:24][INFO] Registering route: GET /api/stats
[2025-08-02 01:32:24][INFO] Registering route: GET /api/stats/:type
[2025-08-02 01:32:24][INFO] Dispatching request: GET /api/auth/captcha
[2025-08-02 01:32:24][INFO] 数据库连接成功: 127.0.0.1:3306/plb_kj
[2025-08-02 01:32:26][INFO] Request: GET /api/auth/validate
[2025-08-02 01:32:26][INFO] Registering route: POST /api/admin/login
[2025-08-02 01:32:26][INFO] Registering route: POST /api/users/login
[2025-08-02 01:32:26][INFO] Registering route: GET /api/auth/captcha
[2025-08-02 01:32:26][INFO] Registering route: GET /api/auth/validate
[2025-08-02 01:32:26][INFO] Registering route: GET /api/users
[2025-08-02 01:32:26][INFO] Registering route: POST /api/users
[2025-08-02 01:32:26][INFO] Registering route: GET /api/users/profile
[2025-08-02 01:32:26][INFO] Registering route: GET /api/users/:id
[2025-08-02 01:32:26][INFO] Registering route: PUT /api/users/:id
[2025-08-02 01:32:26][INFO] Registering route: DELETE /api/users/:id
[2025-08-02 01:32:26][INFO] Registering route: GET /api/stats
[2025-08-02 01:32:26][INFO] Registering route: GET /api/stats/:type
[2025-08-02 01:32:26][INFO] Dispatching request: GET /api/auth/validate
[2025-08-02 01:32:26][INFO] 数据库连接成功: 127.0.0.1:3306/plb_kj
[2025-08-02 01:32:26][ERROR] Token validation error: 无效的认证令牌
[2025-08-02 01:32:26][INFO] Request: GET /api/auth/captcha
[2025-08-02 01:32:26][INFO] Registering route: POST /api/admin/login
[2025-08-02 01:32:26][INFO] Registering route: POST /api/users/login
[2025-08-02 01:32:26][INFO] Registering route: GET /api/auth/captcha
[2025-08-02 01:32:26][INFO] Registering route: GET /api/auth/validate
[2025-08-02 01:32:26][INFO] Registering route: GET /api/users
[2025-08-02 01:32:26][INFO] Registering route: POST /api/users
[2025-08-02 01:32:26][INFO] Registering route: GET /api/users/profile
[2025-08-02 01:32:26][INFO] Registering route: GET /api/users/:id
[2025-08-02 01:32:26][INFO] Registering route: PUT /api/users/:id
[2025-08-02 01:32:26][INFO] Registering route: DELETE /api/users/:id
[2025-08-02 01:32:26][INFO] Registering route: GET /api/stats
[2025-08-02 01:32:26][INFO] Registering route: GET /api/stats/:type
[2025-08-02 01:32:26][INFO] Dispatching request: GET /api/auth/captcha
[2025-08-02 01:32:26][INFO] 数据库连接成功: 127.0.0.1:3306/plb_kj
[2025-08-02 01:34:06][INFO] Request: GET /?ide_webview_request_time=1754069646649
[2025-08-02 01:34:06][INFO] Registering route: POST /api/admin/login
[2025-08-02 01:34:06][INFO] Registering route: POST /api/users/login
[2025-08-02 01:34:06][INFO] Registering route: GET /api/auth/captcha
[2025-08-02 01:34:06][INFO] Registering route: GET /api/auth/validate
[2025-08-02 01:34:06][INFO] Registering route: GET /api/users
[2025-08-02 01:34:06][INFO] Registering route: POST /api/users
[2025-08-02 01:34:06][INFO] Registering route: GET /api/users/profile
[2025-08-02 01:34:06][INFO] Registering route: GET /api/users/:id
[2025-08-02 01:34:06][INFO] Registering route: PUT /api/users/:id
[2025-08-02 01:34:06][INFO] Registering route: DELETE /api/users/:id
[2025-08-02 01:34:06][INFO] Registering route: GET /api/stats
[2025-08-02 01:34:06][INFO] Registering route: GET /api/stats/:type
[2025-08-02 01:34:06][INFO] Dispatching request: GET /
[2025-08-02 01:34:06][WARNING] Route not found: GET /
[2025-08-02 01:34:07][INFO] Request: GET /@vite/client
[2025-08-02 01:34:07][INFO] Registering route: POST /api/admin/login
[2025-08-02 01:34:07][INFO] Registering route: POST /api/users/login
[2025-08-02 01:34:07][INFO] Registering route: GET /api/auth/captcha
[2025-08-02 01:34:07][INFO] Registering route: GET /api/auth/validate
[2025-08-02 01:34:07][INFO] Registering route: GET /api/users
[2025-08-02 01:34:07][INFO] Registering route: POST /api/users
[2025-08-02 01:34:07][INFO] Registering route: GET /api/users/profile
[2025-08-02 01:34:07][INFO] Registering route: GET /api/users/:id
[2025-08-02 01:34:07][INFO] Registering route: PUT /api/users/:id
[2025-08-02 01:34:07][INFO] Registering route: DELETE /api/users/:id
[2025-08-02 01:34:07][INFO] Registering route: GET /api/stats
[2025-08-02 01:34:07][INFO] Registering route: GET /api/stats/:type
[2025-08-02 01:34:07][INFO] Dispatching request: GET /@vite/client
[2025-08-02 01:34:07][WARNING] Route not found: GET /@vite/client
[2025-08-02 01:34:11][INFO] Request: GET /?ide_webview_request_time=1754069651410
[2025-08-02 01:34:11][INFO] Registering route: POST /api/admin/login
[2025-08-02 01:34:11][INFO] Registering route: POST /api/users/login
[2025-08-02 01:34:11][INFO] Registering route: GET /api/auth/captcha
[2025-08-02 01:34:11][INFO] Registering route: GET /api/auth/validate
[2025-08-02 01:34:11][INFO] Registering route: GET /api/users
[2025-08-02 01:34:11][INFO] Registering route: POST /api/users
[2025-08-02 01:34:11][INFO] Registering route: GET /api/users/profile
[2025-08-02 01:34:11][INFO] Registering route: GET /api/users/:id
[2025-08-02 01:34:11][INFO] Registering route: PUT /api/users/:id
[2025-08-02 01:34:11][INFO] Registering route: DELETE /api/users/:id
[2025-08-02 01:34:11][INFO] Registering route: GET /api/stats
[2025-08-02 01:34:11][INFO] Registering route: GET /api/stats/:type
[2025-08-02 01:34:11][INFO] Dispatching request: GET /
[2025-08-02 01:34:11][WARNING] Route not found: GET /
[2025-08-02 01:34:11][INFO] Request: GET /@vite/client
[2025-08-02 01:34:11][INFO] Registering route: POST /api/admin/login
[2025-08-02 01:34:11][INFO] Registering route: POST /api/users/login
[2025-08-02 01:34:11][INFO] Registering route: GET /api/auth/captcha
[2025-08-02 01:34:11][INFO] Registering route: GET /api/auth/validate
[2025-08-02 01:34:11][INFO] Registering route: GET /api/users
[2025-08-02 01:34:11][INFO] Registering route: POST /api/users
[2025-08-02 01:34:11][INFO] Registering route: GET /api/users/profile
[2025-08-02 01:34:11][INFO] Registering route: GET /api/users/:id
[2025-08-02 01:34:11][INFO] Registering route: PUT /api/users/:id
[2025-08-02 01:34:11][INFO] Registering route: DELETE /api/users/:id
[2025-08-02 01:34:11][INFO] Registering route: GET /api/stats
[2025-08-02 01:34:11][INFO] Registering route: GET /api/stats/:type
[2025-08-02 01:34:11][INFO] Dispatching request: GET /@vite/client
[2025-08-02 01:34:11][WARNING] Route not found: GET /@vite/client
[2025-08-02 01:34:28][INFO] Request: GET /api/dashboard/stats
[2025-08-02 01:34:28][INFO] Registering route: POST /api/admin/login
[2025-08-02 01:34:28][INFO] Registering route: POST /api/users/login
[2025-08-02 01:34:28][INFO] Registering route: GET /api/auth/captcha
[2025-08-02 01:34:28][INFO] Registering route: GET /api/auth/validate
[2025-08-02 01:34:28][INFO] Registering route: GET /api/users
[2025-08-02 01:34:28][INFO] Registering route: POST /api/users
[2025-08-02 01:34:28][INFO] Registering route: GET /api/users/profile
[2025-08-02 01:34:28][INFO] Registering route: GET /api/users/:id
[2025-08-02 01:34:28][INFO] Registering route: PUT /api/users/:id
[2025-08-02 01:34:28][INFO] Registering route: DELETE /api/users/:id
[2025-08-02 01:34:28][INFO] Registering route: GET /api/stats
[2025-08-02 01:34:28][INFO] Registering route: GET /api/stats/:type
[2025-08-02 01:34:28][INFO] Dispatching request: GET /api/dashboard/stats
[2025-08-02 01:34:28][WARNING] Route not found: GET /api/dashboard/stats
[2025-08-02 01:34:31][INFO] Request: GET /
[2025-08-02 01:34:31][INFO] Registering route: POST /api/admin/login
[2025-08-02 01:34:31][INFO] Registering route: POST /api/users/login
[2025-08-02 01:34:31][INFO] Registering route: GET /api/auth/captcha
[2025-08-02 01:34:31][INFO] Registering route: GET /api/auth/validate
[2025-08-02 01:34:31][INFO] Registering route: GET /api/users
[2025-08-02 01:34:31][INFO] Registering route: POST /api/users
[2025-08-02 01:34:31][INFO] Registering route: GET /api/users/profile
[2025-08-02 01:34:31][INFO] Registering route: GET /api/users/:id
[2025-08-02 01:34:31][INFO] Registering route: PUT /api/users/:id
[2025-08-02 01:34:31][INFO] Registering route: DELETE /api/users/:id
[2025-08-02 01:34:31][INFO] Registering route: GET /api/stats
[2025-08-02 01:34:31][INFO] Registering route: GET /api/stats/:type
[2025-08-02 01:34:31][INFO] Dispatching request: GET /
[2025-08-02 01:34:31][WARNING] Route not found: GET /
[2025-08-02 01:34:41][INFO] Request: GET /api/media
[2025-08-02 01:34:41][INFO] Registering route: POST /api/admin/login
[2025-08-02 01:34:41][INFO] Registering route: POST /api/users/login
[2025-08-02 01:34:41][INFO] Registering route: GET /api/auth/captcha
[2025-08-02 01:34:41][INFO] Registering route: GET /api/auth/validate
[2025-08-02 01:34:41][INFO] Registering route: GET /api/users
[2025-08-02 01:34:41][INFO] Registering route: POST /api/users
[2025-08-02 01:34:41][INFO] Registering route: GET /api/users/profile
[2025-08-02 01:34:41][INFO] Registering route: GET /api/users/:id
[2025-08-02 01:34:41][INFO] Registering route: PUT /api/users/:id
[2025-08-02 01:34:41][INFO] Registering route: DELETE /api/users/:id
[2025-08-02 01:34:41][INFO] Registering route: GET /api/stats
[2025-08-02 01:34:41][INFO] Registering route: GET /api/stats/:type
[2025-08-02 01:34:41][INFO] Dispatching request: GET /api/media
[2025-08-02 01:34:41][WARNING] Route not found: GET /api/media
[2025-08-02 01:34:43][INFO] Request: GET /api/users
[2025-08-02 01:34:43][INFO] Registering route: POST /api/admin/login
[2025-08-02 01:34:43][INFO] Registering route: POST /api/users/login
[2025-08-02 01:34:43][INFO] Registering route: GET /api/auth/captcha
[2025-08-02 01:34:43][INFO] Registering route: GET /api/auth/validate
[2025-08-02 01:34:43][INFO] Registering route: GET /api/users
[2025-08-02 01:34:43][INFO] Registering route: POST /api/users
[2025-08-02 01:34:43][INFO] Registering route: GET /api/users/profile
[2025-08-02 01:34:43][INFO] Registering route: GET /api/users/:id
[2025-08-02 01:34:43][INFO] Registering route: PUT /api/users/:id
[2025-08-02 01:34:43][INFO] Registering route: DELETE /api/users/:id
[2025-08-02 01:34:43][INFO] Registering route: GET /api/stats
[2025-08-02 01:34:43][INFO] Registering route: GET /api/stats/:type
[2025-08-02 01:34:43][INFO] Dispatching request: GET /api/users
[2025-08-02 01:34:43][INFO] 数据库连接成功: 127.0.0.1:3306/plb_kj
[2025-08-02 01:34:51][INFO] Request: GET /health
[2025-08-02 01:34:51][INFO] Registering route: POST /api/admin/login
[2025-08-02 01:34:51][INFO] Registering route: POST /api/users/login
[2025-08-02 01:34:51][INFO] Registering route: GET /api/auth/captcha
[2025-08-02 01:34:51][INFO] Registering route: GET /api/auth/validate
[2025-08-02 01:34:51][INFO] Registering route: GET /api/users
[2025-08-02 01:34:51][INFO] Registering route: POST /api/users
[2025-08-02 01:34:51][INFO] Registering route: GET /api/users/profile
[2025-08-02 01:34:51][INFO] Registering route: GET /api/users/:id
[2025-08-02 01:34:51][INFO] Registering route: PUT /api/users/:id
[2025-08-02 01:34:51][INFO] Registering route: DELETE /api/users/:id
[2025-08-02 01:34:51][INFO] Registering route: GET /api/stats
[2025-08-02 01:34:51][INFO] Registering route: GET /api/stats/:type
[2025-08-02 01:34:51][INFO] Dispatching request: GET /health
[2025-08-02 01:34:51][WARNING] Route not found: GET /health
[2025-08-02 01:34:55][INFO] Request: GET /health
[2025-08-02 01:34:55][INFO] Registering route: POST /api/admin/login
[2025-08-02 01:34:55][INFO] Registering route: POST /api/users/login
[2025-08-02 01:34:55][INFO] Registering route: GET /api/auth/captcha
[2025-08-02 01:34:55][INFO] Registering route: GET /api/auth/validate
[2025-08-02 01:34:55][INFO] Registering route: GET /api/users
[2025-08-02 01:34:55][INFO] Registering route: POST /api/users
[2025-08-02 01:34:55][INFO] Registering route: GET /api/users/profile
[2025-08-02 01:34:55][INFO] Registering route: GET /api/users/:id
[2025-08-02 01:34:55][INFO] Registering route: PUT /api/users/:id
[2025-08-02 01:34:55][INFO] Registering route: DELETE /api/users/:id
[2025-08-02 01:34:55][INFO] Registering route: GET /api/stats
[2025-08-02 01:34:55][INFO] Registering route: GET /api/stats/:type
[2025-08-02 01:34:55][INFO] Dispatching request: GET /health
[2025-08-02 01:34:55][WARNING] Route not found: GET /health
[2025-08-02 01:35:03][INFO] Request: GET /api/health
[2025-08-02 01:35:03][INFO] Registering route: POST /api/admin/login
[2025-08-02 01:35:03][INFO] Registering route: POST /api/users/login
[2025-08-02 01:35:03][INFO] Registering route: GET /api/auth/captcha
[2025-08-02 01:35:03][INFO] Registering route: GET /api/auth/validate
[2025-08-02 01:35:03][INFO] Registering route: GET /api/users
[2025-08-02 01:35:03][INFO] Registering route: POST /api/users
[2025-08-02 01:35:03][INFO] Registering route: GET /api/users/profile
[2025-08-02 01:35:03][INFO] Registering route: GET /api/users/:id
[2025-08-02 01:35:03][INFO] Registering route: PUT /api/users/:id
[2025-08-02 01:35:03][INFO] Registering route: DELETE /api/users/:id
[2025-08-02 01:35:03][INFO] Registering route: GET /api/stats
[2025-08-02 01:35:03][INFO] Registering route: GET /api/stats/:type
[2025-08-02 01:35:03][INFO] Dispatching request: GET /api/health
[2025-08-02 01:35:03][WARNING] Route not found: GET /api/health
[2025-08-02 01:40:51][INFO] Request: GET /api/media
[2025-08-02 01:40:51][INFO] Registering route: POST /api/admin/login
[2025-08-02 01:40:51][INFO] Registering route: POST /api/users/login
[2025-08-02 01:40:51][INFO] Registering route: GET /api/auth/captcha
[2025-08-02 01:40:51][INFO] Registering route: GET /api/auth/validate
[2025-08-02 01:40:51][INFO] Registering route: GET /api/users
[2025-08-02 01:40:51][INFO] Registering route: POST /api/users
[2025-08-02 01:40:51][INFO] Registering route: GET /api/users/profile
[2025-08-02 01:40:51][INFO] Registering route: GET /api/users/:id
[2025-08-02 01:40:51][INFO] Registering route: PUT /api/users/:id
[2025-08-02 01:40:51][INFO] Registering route: DELETE /api/users/:id
[2025-08-02 01:40:51][INFO] Registering route: GET /api/stats
[2025-08-02 01:40:51][INFO] Registering route: GET /api/stats/:type
[2025-08-02 01:40:51][INFO] Dispatching request: GET /api/media
[2025-08-02 01:40:51][WARNING] Route not found: GET /api/media
[2025-08-02 01:41:17][INFO] Request: GET /api/health
[2025-08-02 01:41:17][INFO] Registering route: POST /api/admin/login
[2025-08-02 01:41:17][INFO] Registering route: POST /api/users/login
[2025-08-02 01:41:17][INFO] Registering route: GET /api/auth/captcha
[2025-08-02 01:41:17][INFO] Registering route: GET /api/auth/validate
[2025-08-02 01:41:17][INFO] Registering route: GET /api/users
[2025-08-02 01:41:17][INFO] Registering route: POST /api/users
[2025-08-02 01:41:17][INFO] Registering route: GET /api/users/profile
[2025-08-02 01:41:17][INFO] Registering route: GET /api/users/:id
[2025-08-02 01:41:17][INFO] Registering route: PUT /api/users/:id
[2025-08-02 01:41:17][INFO] Registering route: DELETE /api/users/:id
[2025-08-02 01:41:17][INFO] Registering route: GET /api/stats
[2025-08-02 01:41:17][INFO] Registering route: GET /api/stats/:type
[2025-08-02 01:41:17][INFO] Dispatching request: GET /api/health
[2025-08-02 01:41:17][WARNING] Route not found: GET /api/health
[2025-08-02 01:41:43][INFO] Request: GET /api/health
[2025-08-02 01:41:43][INFO] Registering route: POST /api/admin/login
[2025-08-02 01:41:43][INFO] Registering route: POST /api/users/login
[2025-08-02 01:41:43][INFO] Registering route: GET /api/auth/captcha
[2025-08-02 01:41:43][INFO] Registering route: GET /api/auth/validate
[2025-08-02 01:41:43][INFO] Registering route: GET /api/users
[2025-08-02 01:41:43][INFO] Registering route: POST /api/users
[2025-08-02 01:41:43][INFO] Registering route: GET /api/users/profile
[2025-08-02 01:41:43][INFO] Registering route: GET /api/users/:id
[2025-08-02 01:41:43][INFO] Registering route: PUT /api/users/:id
[2025-08-02 01:41:43][INFO] Registering route: DELETE /api/users/:id
[2025-08-02 01:41:43][INFO] Registering route: GET /api/stats
[2025-08-02 01:41:43][INFO] Registering route: GET /api/stats/:type
[2025-08-02 01:41:43][INFO] Dispatching request: GET /api/health
[2025-08-02 01:41:43][WARNING] Route not found: GET /api/health
[2025-08-02 01:42:26][INFO] Request: GET /api/users
[2025-08-02 01:42:26][INFO] Registering route: POST /api/admin/login
[2025-08-02 01:42:26][INFO] Registering route: POST /api/users/login
[2025-08-02 01:42:26][INFO] Registering route: GET /api/auth/captcha
[2025-08-02 01:42:26][INFO] Registering route: GET /api/auth/validate
[2025-08-02 01:42:26][INFO] Registering route: GET /api/users
[2025-08-02 01:42:26][INFO] Registering route: POST /api/users
[2025-08-02 01:42:26][INFO] Registering route: GET /api/users/profile
[2025-08-02 01:42:26][INFO] Registering route: GET /api/users/:id
[2025-08-02 01:42:26][INFO] Registering route: PUT /api/users/:id
[2025-08-02 01:42:26][INFO] Registering route: DELETE /api/users/:id
[2025-08-02 01:42:26][INFO] Registering route: GET /api/stats
[2025-08-02 01:42:26][INFO] Registering route: GET /api/stats/:type
[2025-08-02 01:42:26][INFO] Dispatching request: GET /api/users
[2025-08-02 01:42:26][INFO] 数据库连接成功: 127.0.0.1:3306/plb_kj
[2025-08-02 01:43:22][INFO] Request: GET /api/health
[2025-08-02 01:43:22][INFO] Registering route: POST /api/admin/login
[2025-08-02 01:43:22][INFO] Registering route: POST /api/users/login
[2025-08-02 01:43:22][INFO] Registering route: GET /api/auth/captcha
[2025-08-02 01:43:22][INFO] Registering route: GET /api/auth/validate
[2025-08-02 01:43:22][INFO] Registering route: GET /api/users
[2025-08-02 01:43:22][INFO] Registering route: POST /api/users
[2025-08-02 01:43:22][INFO] Registering route: GET /api/users/profile
[2025-08-02 01:43:22][INFO] Registering route: GET /api/users/:id
[2025-08-02 01:43:22][INFO] Registering route: PUT /api/users/:id
[2025-08-02 01:43:22][INFO] Registering route: DELETE /api/users/:id
[2025-08-02 01:43:22][INFO] Registering route: GET /api/stats
[2025-08-02 01:43:22][INFO] Registering route: GET /api/stats/:type
[2025-08-02 01:43:22][INFO] Dispatching request: GET /api/health
[2025-08-02 01:43:22][WARNING] Route not found: GET /api/health
[2025-08-02 01:43:36][INFO] Request: GET /api/health
[2025-08-02 01:43:36][INFO] Registering route: POST /api/admin/login
[2025-08-02 01:43:36][INFO] Registering route: POST /api/users/login
[2025-08-02 01:43:36][INFO] Registering route: GET /api/auth/captcha
[2025-08-02 01:43:36][INFO] Registering route: GET /api/auth/validate
[2025-08-02 01:43:36][INFO] Registering route: GET /api/users
[2025-08-02 01:43:36][INFO] Registering route: POST /api/users
[2025-08-02 01:43:36][INFO] Registering route: GET /api/users/profile
[2025-08-02 01:43:36][INFO] Registering route: GET /api/users/:id
[2025-08-02 01:43:36][INFO] Registering route: PUT /api/users/:id
[2025-08-02 01:43:36][INFO] Registering route: DELETE /api/users/:id
[2025-08-02 01:43:36][INFO] Registering route: GET /api/stats
[2025-08-02 01:43:36][INFO] Registering route: GET /api/stats/:type
[2025-08-02 01:43:36][INFO] Dispatching request: GET /api/health
[2025-08-02 01:43:36][WARNING] Route not found: GET /api/health
[2025-08-02 01:43:41][INFO] Request: GET /api/health
[2025-08-02 01:43:41][INFO] Registering route: POST /api/admin/login
[2025-08-02 01:43:41][INFO] Registering route: POST /api/users/login
[2025-08-02 01:43:41][INFO] Registering route: GET /api/auth/captcha
[2025-08-02 01:43:41][INFO] Registering route: GET /api/auth/validate
[2025-08-02 01:43:41][INFO] Registering route: GET /api/users
[2025-08-02 01:43:41][INFO] Registering route: POST /api/users
[2025-08-02 01:43:41][INFO] Registering route: GET /api/users/profile
[2025-08-02 01:43:41][INFO] Registering route: GET /api/users/:id
[2025-08-02 01:43:41][INFO] Registering route: PUT /api/users/:id
[2025-08-02 01:43:41][INFO] Registering route: DELETE /api/users/:id
[2025-08-02 01:43:41][INFO] Registering route: GET /api/stats
[2025-08-02 01:43:41][INFO] Registering route: GET /api/stats/:type
[2025-08-02 01:43:41][INFO] Dispatching request: GET /api/health
[2025-08-02 01:43:41][WARNING] Route not found: GET /api/health
[2025-08-02 01:44:41][INFO] Request: GET /api/health
[2025-08-02 01:44:41][INFO] Registering route: POST /api/admin/login
[2025-08-02 01:44:41][INFO] Registering route: POST /api/users/login
[2025-08-02 01:44:41][INFO] Registering route: GET /api/auth/captcha
[2025-08-02 01:44:41][INFO] Registering route: GET /api/auth/validate
[2025-08-02 01:44:41][INFO] Registering route: GET /api/users
[2025-08-02 01:44:41][INFO] Registering route: POST /api/users
[2025-08-02 01:44:41][INFO] Registering route: GET /api/users/profile
[2025-08-02 01:44:41][INFO] Registering route: GET /api/users/:id
[2025-08-02 01:44:41][INFO] Registering route: PUT /api/users/:id
[2025-08-02 01:44:41][INFO] Registering route: DELETE /api/users/:id
[2025-08-02 01:44:41][INFO] Registering route: GET /api/stats
[2025-08-02 01:44:41][INFO] Registering route: GET /api/stats/:type
[2025-08-02 01:44:41][INFO] Dispatching request: GET /api/health
[2025-08-02 01:44:41][WARNING] Route not found: GET /api/health
[2025-08-02 01:53:48][INFO] Request: GET /health
[2025-08-02 01:53:48][INFO] Registering route: POST /api/admin/login
[2025-08-02 01:53:48][INFO] Registering route: POST /api/users/login
[2025-08-02 01:53:48][INFO] Registering route: GET /api/auth/captcha
[2025-08-02 01:53:48][INFO] Registering route: GET /api/auth/validate
[2025-08-02 01:53:48][INFO] Registering route: GET /api/users
[2025-08-02 01:53:48][INFO] Registering route: POST /api/users
[2025-08-02 01:53:48][INFO] Registering route: GET /api/users/profile
[2025-08-02 01:53:48][INFO] Registering route: GET /api/users/:id
[2025-08-02 01:53:48][INFO] Registering route: PUT /api/users/:id
[2025-08-02 01:53:48][INFO] Registering route: DELETE /api/users/:id
[2025-08-02 01:53:48][INFO] Registering route: GET /api/stats
[2025-08-02 01:53:48][INFO] Registering route: GET /api/stats/:type
[2025-08-02 01:53:48][INFO] Dispatching request: GET /health
[2025-08-02 01:53:48][WARNING] Route not found: GET /health
[2025-08-02 01:54:26][INFO] Request: GET /api/health
[2025-08-02 01:54:26][INFO] Registering route: POST /api/admin/login
[2025-08-02 01:54:26][INFO] Registering route: POST /api/users/login
[2025-08-02 01:54:26][INFO] Registering route: GET /api/auth/captcha
[2025-08-02 01:54:26][INFO] Registering route: GET /api/auth/validate
[2025-08-02 01:54:26][INFO] Registering route: GET /api/users
[2025-08-02 01:54:26][INFO] Registering route: POST /api/users
[2025-08-02 01:54:26][INFO] Registering route: GET /api/users/profile
[2025-08-02 01:54:26][INFO] Registering route: GET /api/users/:id
[2025-08-02 01:54:26][INFO] Registering route: PUT /api/users/:id
[2025-08-02 01:54:26][INFO] Registering route: DELETE /api/users/:id
[2025-08-02 01:54:26][INFO] Registering route: GET /api/stats
[2025-08-02 01:54:26][INFO] Registering route: GET /api/stats/:type
[2025-08-02 01:54:26][INFO] Dispatching request: GET /api/health
[2025-08-02 01:54:26][WARNING] Route not found: GET /api/health
[2025-08-02 01:55:35][INFO] Request: GET /api/health
[2025-08-02 01:55:35][INFO] Registering route: POST /api/admin/login
[2025-08-02 01:55:35][INFO] Registering route: POST /api/users/login
[2025-08-02 01:55:35][INFO] Registering route: GET /api/auth/captcha
[2025-08-02 01:55:35][INFO] Registering route: GET /api/auth/validate
[2025-08-02 01:55:35][INFO] Registering route: GET /api/users
[2025-08-02 01:55:35][INFO] Registering route: POST /api/users
[2025-08-02 01:55:35][INFO] Registering route: GET /api/users/profile
[2025-08-02 01:55:35][INFO] Registering route: GET /api/users/:id
[2025-08-02 01:55:35][INFO] Registering route: PUT /api/users/:id
[2025-08-02 01:55:35][INFO] Registering route: DELETE /api/users/:id
[2025-08-02 01:55:35][INFO] Registering route: GET /api/stats
[2025-08-02 01:55:35][INFO] Registering route: GET /api/stats/:type
[2025-08-02 01:55:35][INFO] Dispatching request: GET /api/health
[2025-08-02 01:55:35][WARNING] Route not found: GET /api/health
[2025-08-02 01:56:19][INFO] Request: GET /api/dashboard/stats
[2025-08-02 01:56:19][INFO] Registering route: POST /api/admin/login
[2025-08-02 01:56:19][INFO] Registering route: POST /api/users/login
[2025-08-02 01:56:19][INFO] Registering route: GET /api/auth/captcha
[2025-08-02 01:56:19][INFO] Registering route: GET /api/auth/validate
[2025-08-02 01:56:19][INFO] Registering route: GET /api/users
[2025-08-02 01:56:19][INFO] Registering route: POST /api/users
[2025-08-02 01:56:19][INFO] Registering route: GET /api/users/profile
[2025-08-02 01:56:19][INFO] Registering route: GET /api/users/:id
[2025-08-02 01:56:19][INFO] Registering route: PUT /api/users/:id
[2025-08-02 01:56:19][INFO] Registering route: DELETE /api/users/:id
[2025-08-02 01:56:19][INFO] Registering route: GET /api/stats
[2025-08-02 01:56:19][INFO] Registering route: GET /api/stats/:type
[2025-08-02 01:56:19][INFO] Dispatching request: GET /api/dashboard/stats
[2025-08-02 01:56:19][WARNING] Route not found: GET /api/dashboard/stats
[2025-08-02 01:56:22][INFO] Request: GET /api/health
[2025-08-02 01:56:22][INFO] Registering route: POST /api/admin/login
[2025-08-02 01:56:22][INFO] Registering route: POST /api/users/login
[2025-08-02 01:56:22][INFO] Registering route: GET /api/auth/captcha
[2025-08-02 01:56:22][INFO] Registering route: GET /api/auth/validate
[2025-08-02 01:56:22][INFO] Registering route: GET /api/users
[2025-08-02 01:56:22][INFO] Registering route: POST /api/users
[2025-08-02 01:56:22][INFO] Registering route: GET /api/users/profile
[2025-08-02 01:56:22][INFO] Registering route: GET /api/users/:id
[2025-08-02 01:56:22][INFO] Registering route: PUT /api/users/:id
[2025-08-02 01:56:22][INFO] Registering route: DELETE /api/users/:id
[2025-08-02 01:56:22][INFO] Registering route: GET /api/stats
[2025-08-02 01:56:22][INFO] Registering route: GET /api/stats/:type
[2025-08-02 01:56:22][INFO] Dispatching request: GET /api/health
[2025-08-02 01:56:22][WARNING] Route not found: GET /api/health
[2025-08-02 01:56:25][INFO] Request: GET /api/users
[2025-08-02 01:56:25][INFO] Registering route: POST /api/admin/login
[2025-08-02 01:56:25][INFO] Registering route: POST /api/users/login
[2025-08-02 01:56:25][INFO] Registering route: GET /api/auth/captcha
[2025-08-02 01:56:25][INFO] Registering route: GET /api/auth/validate
[2025-08-02 01:56:25][INFO] Registering route: GET /api/users
[2025-08-02 01:56:25][INFO] Registering route: POST /api/users
[2025-08-02 01:56:25][INFO] Registering route: GET /api/users/profile
[2025-08-02 01:56:25][INFO] Registering route: GET /api/users/:id
[2025-08-02 01:56:25][INFO] Registering route: PUT /api/users/:id
[2025-08-02 01:56:25][INFO] Registering route: DELETE /api/users/:id
[2025-08-02 01:56:25][INFO] Registering route: GET /api/stats
[2025-08-02 01:56:25][INFO] Registering route: GET /api/stats/:type
[2025-08-02 01:56:25][INFO] Dispatching request: GET /api/users
[2025-08-02 01:56:25][INFO] 数据库连接成功: 127.0.0.1:3306/plb_kj
[2025-08-02 01:56:29][INFO] Request: GET /api/dashboard/stats
[2025-08-02 01:56:29][INFO] Registering route: POST /api/admin/login
[2025-08-02 01:56:29][INFO] Registering route: POST /api/users/login
[2025-08-02 01:56:29][INFO] Registering route: GET /api/auth/captcha
[2025-08-02 01:56:29][INFO] Registering route: GET /api/auth/validate
[2025-08-02 01:56:29][INFO] Registering route: GET /api/users
[2025-08-02 01:56:29][INFO] Registering route: POST /api/users
[2025-08-02 01:56:29][INFO] Registering route: GET /api/users/profile
[2025-08-02 01:56:29][INFO] Registering route: GET /api/users/:id
[2025-08-02 01:56:29][INFO] Registering route: PUT /api/users/:id
[2025-08-02 01:56:29][INFO] Registering route: DELETE /api/users/:id
[2025-08-02 01:56:29][INFO] Registering route: GET /api/stats
[2025-08-02 01:56:29][INFO] Registering route: GET /api/stats/:type
[2025-08-02 01:56:29][INFO] Dispatching request: GET /api/dashboard/stats
[2025-08-02 01:56:29][WARNING] Route not found: GET /api/dashboard/stats
[2025-08-02 02:15:42][INFO] Request: GET /api/health
[2025-08-02 02:15:42][INFO] Registering route: GET /api/health
[2025-08-02 02:15:42][INFO] Registering route: POST /api/admin/login
[2025-08-02 02:15:42][INFO] Registering route: POST /api/users/login
[2025-08-02 02:15:42][INFO] Registering route: GET /api/auth/captcha
[2025-08-02 02:15:42][INFO] Registering route: GET /api/auth/validate
[2025-08-02 02:15:42][INFO] Registering route: GET /api/users
[2025-08-02 02:15:42][INFO] Registering route: POST /api/users
[2025-08-02 02:15:42][INFO] Registering route: GET /api/users/profile
[2025-08-02 02:15:42][INFO] Registering route: GET /api/users/:id
[2025-08-02 02:15:42][INFO] Registering route: PUT /api/users/:id
[2025-08-02 02:15:42][INFO] Registering route: DELETE /api/users/:id
[2025-08-02 02:15:42][INFO] Registering route: GET /api/stats
[2025-08-02 02:15:42][INFO] Registering route: GET /api/stats/:type
[2025-08-02 02:15:42][INFO] Dispatching request: GET /api/health
[2025-08-02 02:15:42][INFO] 数据库连接成功: 127.0.0.1:3306/plb_kj
[2025-08-02 02:22:27][INFO] Request: GET /api/auth/captcha
[2025-08-02 02:22:27][INFO] Registering route: GET /api/health
[2025-08-02 02:22:27][INFO] Registering route: POST /api/admin/login
[2025-08-02 02:22:27][INFO] Registering route: POST /api/users/login
[2025-08-02 02:22:27][INFO] Registering route: GET /api/auth/captcha
[2025-08-02 02:22:27][INFO] Registering route: GET /api/auth/validate
[2025-08-02 02:22:27][INFO] Registering route: GET /api/users
[2025-08-02 02:22:27][INFO] Registering route: POST /api/users
[2025-08-02 02:22:27][INFO] Registering route: GET /api/users/profile
[2025-08-02 02:22:27][INFO] Registering route: GET /api/users/:id
[2025-08-02 02:22:27][INFO] Registering route: PUT /api/users/:id
[2025-08-02 02:22:27][INFO] Registering route: DELETE /api/users/:id
[2025-08-02 02:22:27][INFO] Registering route: GET /api/stats
[2025-08-02 02:22:27][INFO] Registering route: GET /api/stats/:type
[2025-08-02 02:22:27][INFO] Dispatching request: GET /api/auth/captcha
[2025-08-02 02:22:27][INFO] 数据库连接成功: 127.0.0.1:3306/plb_kj
[2025-08-02 02:22:29][INFO] Request: GET /api/auth/validate
[2025-08-02 02:22:29][INFO] Registering route: GET /api/health
[2025-08-02 02:22:29][INFO] Registering route: POST /api/admin/login
[2025-08-02 02:22:29][INFO] Registering route: POST /api/users/login
[2025-08-02 02:22:29][INFO] Registering route: GET /api/auth/captcha
[2025-08-02 02:22:29][INFO] Registering route: GET /api/auth/validate
[2025-08-02 02:22:29][INFO] Registering route: GET /api/users
[2025-08-02 02:22:29][INFO] Registering route: POST /api/users
[2025-08-02 02:22:29][INFO] Registering route: GET /api/users/profile
[2025-08-02 02:22:29][INFO] Registering route: GET /api/users/:id
[2025-08-02 02:22:29][INFO] Registering route: PUT /api/users/:id
[2025-08-02 02:22:29][INFO] Registering route: DELETE /api/users/:id
[2025-08-02 02:22:29][INFO] Registering route: GET /api/stats
[2025-08-02 02:22:29][INFO] Registering route: GET /api/stats/:type
[2025-08-02 02:22:29][INFO] Dispatching request: GET /api/auth/validate
[2025-08-02 02:22:29][INFO] 数据库连接成功: 127.0.0.1:3306/plb_kj
[2025-08-02 02:22:29][ERROR] Token validation error: 无效的认证令牌
[2025-08-02 02:22:29][INFO] Request: GET /api/auth/captcha
[2025-08-02 02:22:29][INFO] Registering route: GET /api/health
[2025-08-02 02:22:29][INFO] Registering route: POST /api/admin/login
[2025-08-02 02:22:29][INFO] Registering route: POST /api/users/login
[2025-08-02 02:22:29][INFO] Registering route: GET /api/auth/captcha
[2025-08-02 02:22:29][INFO] Registering route: GET /api/auth/validate
[2025-08-02 02:22:29][INFO] Registering route: GET /api/users
[2025-08-02 02:22:29][INFO] Registering route: POST /api/users
[2025-08-02 02:22:29][INFO] Registering route: GET /api/users/profile
[2025-08-02 02:22:29][INFO] Registering route: GET /api/users/:id
[2025-08-02 02:22:29][INFO] Registering route: PUT /api/users/:id
[2025-08-02 02:22:29][INFO] Registering route: DELETE /api/users/:id
[2025-08-02 02:22:29][INFO] Registering route: GET /api/stats
[2025-08-02 02:22:29][INFO] Registering route: GET /api/stats/:type
[2025-08-02 02:22:29][INFO] Dispatching request: GET /api/auth/captcha
[2025-08-02 02:22:29][INFO] 数据库连接成功: 127.0.0.1:3306/plb_kj
[2025-08-02 02:23:00][INFO] Request: GET /?ide_webview_request_time=1754071160934
[2025-08-02 02:23:00][INFO] Registering route: GET /api/health
[2025-08-02 02:23:00][INFO] Registering route: POST /api/admin/login
[2025-08-02 02:23:00][INFO] Registering route: POST /api/users/login
[2025-08-02 02:23:00][INFO] Registering route: GET /api/auth/captcha
[2025-08-02 02:23:00][INFO] Registering route: GET /api/auth/validate
[2025-08-02 02:23:00][INFO] Registering route: GET /api/users
[2025-08-02 02:23:00][INFO] Registering route: POST /api/users
[2025-08-02 02:23:00][INFO] Registering route: GET /api/users/profile
[2025-08-02 02:23:00][INFO] Registering route: GET /api/users/:id
[2025-08-02 02:23:00][INFO] Registering route: PUT /api/users/:id
[2025-08-02 02:23:00][INFO] Registering route: DELETE /api/users/:id
[2025-08-02 02:23:00][INFO] Registering route: GET /api/stats
[2025-08-02 02:23:00][INFO] Registering route: GET /api/stats/:type
[2025-08-02 02:23:00][INFO] Dispatching request: GET /
[2025-08-02 02:23:00][WARNING] Route not found: GET /
[2025-08-02 02:23:00][INFO] Request: GET /@vite/client
[2025-08-02 02:23:00][INFO] Registering route: GET /api/health
[2025-08-02 02:23:00][INFO] Registering route: POST /api/admin/login
[2025-08-02 02:23:00][INFO] Registering route: POST /api/users/login
[2025-08-02 02:23:00][INFO] Registering route: GET /api/auth/captcha
[2025-08-02 02:23:00][INFO] Registering route: GET /api/auth/validate
[2025-08-02 02:23:00][INFO] Registering route: GET /api/users
[2025-08-02 02:23:00][INFO] Registering route: POST /api/users
[2025-08-02 02:23:00][INFO] Registering route: GET /api/users/profile
[2025-08-02 02:23:00][INFO] Registering route: GET /api/users/:id
[2025-08-02 02:23:00][INFO] Registering route: PUT /api/users/:id
[2025-08-02 02:23:00][INFO] Registering route: DELETE /api/users/:id
[2025-08-02 02:23:00][INFO] Registering route: GET /api/stats
[2025-08-02 02:23:00][INFO] Registering route: GET /api/stats/:type
[2025-08-02 02:23:00][INFO] Dispatching request: GET /@vite/client
[2025-08-02 02:23:00][WARNING] Route not found: GET /@vite/client
[2025-08-02 02:24:45][INFO] Request: GET /api/auth/captcha
[2025-08-02 02:24:45][INFO] Registering route: GET /api/health
[2025-08-02 02:24:45][INFO] Registering route: POST /api/admin/login
[2025-08-02 02:24:45][INFO] Registering route: POST /api/users/login
[2025-08-02 02:24:45][INFO] Registering route: GET /api/auth/captcha
[2025-08-02 02:24:45][INFO] Registering route: GET /api/auth/validate
[2025-08-02 02:24:45][INFO] Registering route: GET /api/users
[2025-08-02 02:24:45][INFO] Registering route: POST /api/users
[2025-08-02 02:24:45][INFO] Registering route: GET /api/users/profile
[2025-08-02 02:24:45][INFO] Registering route: GET /api/users/:id
[2025-08-02 02:24:45][INFO] Registering route: PUT /api/users/:id
[2025-08-02 02:24:45][INFO] Registering route: DELETE /api/users/:id
[2025-08-02 02:24:45][INFO] Registering route: GET /api/stats
[2025-08-02 02:24:45][INFO] Registering route: GET /api/stats/:type
[2025-08-02 02:24:45][INFO] Dispatching request: GET /api/auth/captcha
[2025-08-02 02:24:45][INFO] 数据库连接成功: 127.0.0.1:3306/plb_kj
[2025-08-02 02:24:46][INFO] Request: GET /api/auth/validate
[2025-08-02 02:24:46][INFO] Registering route: GET /api/health
[2025-08-02 02:24:46][INFO] Registering route: POST /api/admin/login
[2025-08-02 02:24:46][INFO] Registering route: POST /api/users/login
[2025-08-02 02:24:46][INFO] Registering route: GET /api/auth/captcha
[2025-08-02 02:24:46][INFO] Registering route: GET /api/auth/validate
[2025-08-02 02:24:46][INFO] Registering route: GET /api/users
[2025-08-02 02:24:46][INFO] Registering route: POST /api/users
[2025-08-02 02:24:46][INFO] Registering route: GET /api/users/profile
[2025-08-02 02:24:46][INFO] Registering route: GET /api/users/:id
[2025-08-02 02:24:46][INFO] Registering route: PUT /api/users/:id
[2025-08-02 02:24:46][INFO] Registering route: DELETE /api/users/:id
[2025-08-02 02:24:46][INFO] Registering route: GET /api/stats
[2025-08-02 02:24:46][INFO] Registering route: GET /api/stats/:type
[2025-08-02 02:24:46][INFO] Dispatching request: GET /api/auth/validate
[2025-08-02 02:24:46][INFO] 数据库连接成功: 127.0.0.1:3306/plb_kj
[2025-08-02 02:24:46][ERROR] Token validation error: 无效的认证令牌
[2025-08-02 02:24:46][INFO] Request: GET /api/auth/captcha
[2025-08-02 02:24:46][INFO] Registering route: GET /api/health
[2025-08-02 02:24:46][INFO] Registering route: POST /api/admin/login
[2025-08-02 02:24:46][INFO] Registering route: POST /api/users/login
[2025-08-02 02:24:46][INFO] Registering route: GET /api/auth/captcha
[2025-08-02 02:24:46][INFO] Registering route: GET /api/auth/validate
[2025-08-02 02:24:46][INFO] Registering route: GET /api/users
[2025-08-02 02:24:46][INFO] Registering route: POST /api/users
[2025-08-02 02:24:46][INFO] Registering route: GET /api/users/profile
[2025-08-02 02:24:46][INFO] Registering route: GET /api/users/:id
[2025-08-02 02:24:46][INFO] Registering route: PUT /api/users/:id
[2025-08-02 02:24:46][INFO] Registering route: DELETE /api/users/:id
[2025-08-02 02:24:46][INFO] Registering route: GET /api/stats
[2025-08-02 02:24:46][INFO] Registering route: GET /api/stats/:type
[2025-08-02 02:24:46][INFO] Dispatching request: GET /api/auth/captcha
[2025-08-02 02:24:46][INFO] 数据库连接成功: 127.0.0.1:3306/plb_kj
[2025-08-02 02:25:00][INFO] Request: GET /?ide_webview_request_time=1754072700353
[2025-08-02 02:25:00][INFO] Registering route: GET /api/health
[2025-08-02 02:25:00][INFO] Registering route: POST /api/admin/login
[2025-08-02 02:25:00][INFO] Registering route: POST /api/users/login
[2025-08-02 02:25:00][INFO] Registering route: GET /api/auth/captcha
[2025-08-02 02:25:00][INFO] Registering route: GET /api/auth/validate
[2025-08-02 02:25:00][INFO] Registering route: GET /api/users
[2025-08-02 02:25:00][INFO] Registering route: POST /api/users
[2025-08-02 02:25:00][INFO] Registering route: GET /api/users/profile
[2025-08-02 02:25:00][INFO] Registering route: GET /api/users/:id
[2025-08-02 02:25:00][INFO] Registering route: PUT /api/users/:id
[2025-08-02 02:25:00][INFO] Registering route: DELETE /api/users/:id
[2025-08-02 02:25:00][INFO] Registering route: GET /api/stats
[2025-08-02 02:25:00][INFO] Registering route: GET /api/stats/:type
[2025-08-02 02:25:00][INFO] Dispatching request: GET /
[2025-08-02 02:25:00][WARNING] Route not found: GET /
[2025-08-02 02:25:00][INFO] Request: GET /@vite/client
[2025-08-02 02:25:00][INFO] Registering route: GET /api/health
[2025-08-02 02:25:00][INFO] Registering route: POST /api/admin/login
[2025-08-02 02:25:00][INFO] Registering route: POST /api/users/login
[2025-08-02 02:25:00][INFO] Registering route: GET /api/auth/captcha
[2025-08-02 02:25:00][INFO] Registering route: GET /api/auth/validate
[2025-08-02 02:25:00][INFO] Registering route: GET /api/users
[2025-08-02 02:25:00][INFO] Registering route: POST /api/users
[2025-08-02 02:25:00][INFO] Registering route: GET /api/users/profile
[2025-08-02 02:25:00][INFO] Registering route: GET /api/users/:id
[2025-08-02 02:25:00][INFO] Registering route: PUT /api/users/:id
[2025-08-02 02:25:00][INFO] Registering route: DELETE /api/users/:id
[2025-08-02 02:25:00][INFO] Registering route: GET /api/stats
[2025-08-02 02:25:00][INFO] Registering route: GET /api/stats/:type
[2025-08-02 02:25:00][INFO] Dispatching request: GET /@vite/client
[2025-08-02 02:25:00][WARNING] Route not found: GET /@vite/client
[2025-08-02 02:25:23][INFO] Request: POST /api/admin/login
[2025-08-02 02:25:23][INFO] Registering route: GET /api/health
[2025-08-02 02:25:23][INFO] Registering route: POST /api/admin/login
[2025-08-02 02:25:23][INFO] Registering route: POST /api/users/login
[2025-08-02 02:25:23][INFO] Registering route: GET /api/auth/captcha
[2025-08-02 02:25:23][INFO] Registering route: GET /api/auth/validate
[2025-08-02 02:25:23][INFO] Registering route: GET /api/users
[2025-08-02 02:25:23][INFO] Registering route: POST /api/users
[2025-08-02 02:25:23][INFO] Registering route: GET /api/users/profile
[2025-08-02 02:25:23][INFO] Registering route: GET /api/users/:id
[2025-08-02 02:25:23][INFO] Registering route: PUT /api/users/:id
[2025-08-02 02:25:23][INFO] Registering route: DELETE /api/users/:id
[2025-08-02 02:25:23][INFO] Registering route: GET /api/stats
[2025-08-02 02:25:23][INFO] Registering route: GET /api/stats/:type
[2025-08-02 02:25:23][INFO] Dispatching request: POST /api/admin/login
[2025-08-02 02:25:23][INFO] 数据库连接成功: 127.0.0.1:3306/plb_kj
[2025-08-02 02:25:23][INFO] Admin login attempt for username: admin
[2025-08-02 02:25:23][INFO] Admin user logged in successfully: admin
[2025-08-02 02:25:51][INFO] Request: GET /api/auth/captcha
[2025-08-02 02:25:51][INFO] Registering route: GET /api/health
[2025-08-02 02:25:51][INFO] Registering route: POST /api/admin/login
[2025-08-02 02:25:51][INFO] Registering route: POST /api/users/login
[2025-08-02 02:25:51][INFO] Registering route: GET /api/auth/captcha
[2025-08-02 02:25:51][INFO] Registering route: GET /api/auth/validate
[2025-08-02 02:25:51][INFO] Registering route: GET /api/users
[2025-08-02 02:25:51][INFO] Registering route: POST /api/users
[2025-08-02 02:25:51][INFO] Registering route: GET /api/users/profile
[2025-08-02 02:25:51][INFO] Registering route: GET /api/users/:id
[2025-08-02 02:25:51][INFO] Registering route: PUT /api/users/:id
[2025-08-02 02:25:51][INFO] Registering route: DELETE /api/users/:id
[2025-08-02 02:25:51][INFO] Registering route: GET /api/stats
[2025-08-02 02:25:51][INFO] Registering route: GET /api/stats/:type
[2025-08-02 02:25:51][INFO] Dispatching request: GET /api/auth/captcha
[2025-08-02 02:25:51][INFO] 数据库连接成功: 127.0.0.1:3306/plb_kj
[2025-08-02 02:25:53][INFO] Request: GET /api/auth/validate
[2025-08-02 02:25:53][INFO] Registering route: GET /api/health
[2025-08-02 02:25:53][INFO] Registering route: POST /api/admin/login
[2025-08-02 02:25:53][INFO] Registering route: POST /api/users/login
[2025-08-02 02:25:53][INFO] Registering route: GET /api/auth/captcha
[2025-08-02 02:25:53][INFO] Registering route: GET /api/auth/validate
[2025-08-02 02:25:53][INFO] Registering route: GET /api/users
[2025-08-02 02:25:53][INFO] Registering route: POST /api/users
[2025-08-02 02:25:53][INFO] Registering route: GET /api/users/profile
[2025-08-02 02:25:53][INFO] Registering route: GET /api/users/:id
[2025-08-02 02:25:53][INFO] Registering route: PUT /api/users/:id
[2025-08-02 02:25:53][INFO] Registering route: DELETE /api/users/:id
[2025-08-02 02:25:53][INFO] Registering route: GET /api/stats
[2025-08-02 02:25:53][INFO] Registering route: GET /api/stats/:type
[2025-08-02 02:25:53][INFO] Dispatching request: GET /api/auth/validate
[2025-08-02 02:25:53][INFO] 数据库连接成功: 127.0.0.1:3306/plb_kj
[2025-08-02 02:25:53][ERROR] Token validation error: 无效的认证令牌
[2025-08-02 02:25:53][INFO] Request: GET /api/auth/captcha
[2025-08-02 02:25:53][INFO] Registering route: GET /api/health
[2025-08-02 02:25:53][INFO] Registering route: POST /api/admin/login
[2025-08-02 02:25:53][INFO] Registering route: POST /api/users/login
[2025-08-02 02:25:53][INFO] Registering route: GET /api/auth/captcha
[2025-08-02 02:25:53][INFO] Registering route: GET /api/auth/validate
[2025-08-02 02:25:53][INFO] Registering route: GET /api/users
[2025-08-02 02:25:53][INFO] Registering route: POST /api/users
[2025-08-02 02:25:53][INFO] Registering route: GET /api/users/profile
[2025-08-02 02:25:53][INFO] Registering route: GET /api/users/:id
[2025-08-02 02:25:53][INFO] Registering route: PUT /api/users/:id
[2025-08-02 02:25:53][INFO] Registering route: DELETE /api/users/:id
[2025-08-02 02:25:53][INFO] Registering route: GET /api/stats
[2025-08-02 02:25:53][INFO] Registering route: GET /api/stats/:type
[2025-08-02 02:25:53][INFO] Dispatching request: GET /api/auth/captcha
[2025-08-02 02:25:53][INFO] 数据库连接成功: 127.0.0.1:3306/plb_kj
[2025-08-02 02:26:04][INFO] Request: GET /?ide_webview_request_time=1754072764277
[2025-08-02 02:26:04][INFO] Registering route: GET /api/health
[2025-08-02 02:26:04][INFO] Registering route: POST /api/admin/login
[2025-08-02 02:26:04][INFO] Registering route: POST /api/users/login
[2025-08-02 02:26:04][INFO] Registering route: GET /api/auth/captcha
[2025-08-02 02:26:04][INFO] Registering route: GET /api/auth/validate
[2025-08-02 02:26:04][INFO] Registering route: GET /api/users
[2025-08-02 02:26:04][INFO] Registering route: POST /api/users
[2025-08-02 02:26:04][INFO] Registering route: GET /api/users/profile
[2025-08-02 02:26:04][INFO] Registering route: GET /api/users/:id
[2025-08-02 02:26:04][INFO] Registering route: PUT /api/users/:id
[2025-08-02 02:26:04][INFO] Registering route: DELETE /api/users/:id
[2025-08-02 02:26:04][INFO] Registering route: GET /api/stats
[2025-08-02 02:26:04][INFO] Registering route: GET /api/stats/:type
[2025-08-02 02:26:04][INFO] Dispatching request: GET /
[2025-08-02 02:26:04][WARNING] Route not found: GET /
[2025-08-02 02:26:04][INFO] Request: GET /@vite/client
[2025-08-02 02:26:04][INFO] Registering route: GET /api/health
[2025-08-02 02:26:04][INFO] Registering route: POST /api/admin/login
[2025-08-02 02:26:04][INFO] Registering route: POST /api/users/login
[2025-08-02 02:26:04][INFO] Registering route: GET /api/auth/captcha
[2025-08-02 02:26:04][INFO] Registering route: GET /api/auth/validate
[2025-08-02 02:26:04][INFO] Registering route: GET /api/users
[2025-08-02 02:26:04][INFO] Registering route: POST /api/users
[2025-08-02 02:26:04][INFO] Registering route: GET /api/users/profile
[2025-08-02 02:26:04][INFO] Registering route: GET /api/users/:id
[2025-08-02 02:26:04][INFO] Registering route: PUT /api/users/:id
[2025-08-02 02:26:04][INFO] Registering route: DELETE /api/users/:id
[2025-08-02 02:26:04][INFO] Registering route: GET /api/stats
[2025-08-02 02:26:04][INFO] Registering route: GET /api/stats/:type
[2025-08-02 02:26:04][INFO] Dispatching request: GET /@vite/client
[2025-08-02 02:26:04][WARNING] Route not found: GET /@vite/client
[2025-08-02 02:26:07][INFO] Request: POST /api/admin/login
[2025-08-02 02:26:07][INFO] Registering route: GET /api/health
[2025-08-02 02:26:07][INFO] Registering route: POST /api/admin/login
[2025-08-02 02:26:07][INFO] Registering route: POST /api/users/login
[2025-08-02 02:26:07][INFO] Registering route: GET /api/auth/captcha
[2025-08-02 02:26:07][INFO] Registering route: GET /api/auth/validate
[2025-08-02 02:26:07][INFO] Registering route: GET /api/users
[2025-08-02 02:26:07][INFO] Registering route: POST /api/users
[2025-08-02 02:26:07][INFO] Registering route: GET /api/users/profile
[2025-08-02 02:26:07][INFO] Registering route: GET /api/users/:id
[2025-08-02 02:26:07][INFO] Registering route: PUT /api/users/:id
[2025-08-02 02:26:07][INFO] Registering route: DELETE /api/users/:id
[2025-08-02 02:26:07][INFO] Registering route: GET /api/stats
[2025-08-02 02:26:07][INFO] Registering route: GET /api/stats/:type
[2025-08-02 02:26:07][INFO] Dispatching request: POST /api/admin/login
[2025-08-02 02:26:07][INFO] 数据库连接成功: 127.0.0.1:3306/plb_kj
[2025-08-02 02:26:07][INFO] Admin login attempt for username: admin
[2025-08-02 02:26:07][INFO] Admin user logged in successfully: admin
[2025-08-02 02:29:48][INFO] Request: GET /api/auth/captcha
[2025-08-02 02:29:48][INFO] Registering route: GET /api/health
[2025-08-02 02:29:48][INFO] Registering route: POST /api/admin/login
[2025-08-02 02:29:48][INFO] Registering route: POST /api/users/login
[2025-08-02 02:29:48][INFO] Registering route: GET /api/auth/captcha
[2025-08-02 02:29:48][INFO] Registering route: GET /api/auth/validate
[2025-08-02 02:29:48][INFO] Registering route: GET /api/users
[2025-08-02 02:29:48][INFO] Registering route: POST /api/users
[2025-08-02 02:29:48][INFO] Registering route: GET /api/users/profile
[2025-08-02 02:29:48][INFO] Registering route: GET /api/users/:id
[2025-08-02 02:29:48][INFO] Registering route: PUT /api/users/:id
[2025-08-02 02:29:48][INFO] Registering route: DELETE /api/users/:id
[2025-08-02 02:29:48][INFO] Registering route: GET /api/stats
[2025-08-02 02:29:48][INFO] Registering route: GET /api/stats/:type
[2025-08-02 02:29:48][INFO] Dispatching request: GET /api/auth/captcha
[2025-08-02 02:29:48][INFO] 数据库连接成功: 127.0.0.1:3306/plb_kj
[2025-08-02 02:29:50][INFO] Request: GET /api/auth/validate
[2025-08-02 02:29:50][INFO] Registering route: GET /api/health
[2025-08-02 02:29:50][INFO] Registering route: POST /api/admin/login
[2025-08-02 02:29:50][INFO] Registering route: POST /api/users/login
[2025-08-02 02:29:50][INFO] Registering route: GET /api/auth/captcha
[2025-08-02 02:29:50][INFO] Registering route: GET /api/auth/validate
[2025-08-02 02:29:50][INFO] Registering route: GET /api/users
[2025-08-02 02:29:50][INFO] Registering route: POST /api/users
[2025-08-02 02:29:50][INFO] Registering route: GET /api/users/profile
[2025-08-02 02:29:50][INFO] Registering route: GET /api/users/:id
[2025-08-02 02:29:50][INFO] Registering route: PUT /api/users/:id
[2025-08-02 02:29:50][INFO] Registering route: DELETE /api/users/:id
[2025-08-02 02:29:50][INFO] Registering route: GET /api/stats
[2025-08-02 02:29:50][INFO] Registering route: GET /api/stats/:type
[2025-08-02 02:29:50][INFO] Dispatching request: GET /api/auth/validate
[2025-08-02 02:29:50][INFO] 数据库连接成功: 127.0.0.1:3306/plb_kj
[2025-08-02 02:29:50][ERROR] Token validation error: 无效的认证令牌
[2025-08-02 02:29:50][INFO] Request: GET /api/auth/captcha
[2025-08-02 02:29:50][INFO] Registering route: GET /api/health
[2025-08-02 02:29:50][INFO] Registering route: POST /api/admin/login
[2025-08-02 02:29:50][INFO] Registering route: POST /api/users/login
[2025-08-02 02:29:50][INFO] Registering route: GET /api/auth/captcha
[2025-08-02 02:29:50][INFO] Registering route: GET /api/auth/validate
[2025-08-02 02:29:50][INFO] Registering route: GET /api/users
[2025-08-02 02:29:50][INFO] Registering route: POST /api/users
[2025-08-02 02:29:50][INFO] Registering route: GET /api/users/profile
[2025-08-02 02:29:50][INFO] Registering route: GET /api/users/:id
[2025-08-02 02:29:50][INFO] Registering route: PUT /api/users/:id
[2025-08-02 02:29:50][INFO] Registering route: DELETE /api/users/:id
[2025-08-02 02:29:50][INFO] Registering route: GET /api/stats
[2025-08-02 02:29:50][INFO] Registering route: GET /api/stats/:type
[2025-08-02 02:29:50][INFO] Dispatching request: GET /api/auth/captcha
[2025-08-02 02:29:50][INFO] 数据库连接成功: 127.0.0.1:3306/plb_kj
[2025-08-02 02:30:00][INFO] Request: GET /?ide_webview_request_time=1754073000035
[2025-08-02 02:30:00][INFO] Registering route: GET /api/health
[2025-08-02 02:30:00][INFO] Registering route: POST /api/admin/login
[2025-08-02 02:30:00][INFO] Registering route: POST /api/users/login
[2025-08-02 02:30:00][INFO] Registering route: GET /api/auth/captcha
[2025-08-02 02:30:00][INFO] Registering route: GET /api/auth/validate
[2025-08-02 02:30:00][INFO] Registering route: GET /api/users
[2025-08-02 02:30:00][INFO] Registering route: POST /api/users
[2025-08-02 02:30:00][INFO] Registering route: GET /api/users/profile
[2025-08-02 02:30:00][INFO] Registering route: GET /api/users/:id
[2025-08-02 02:30:00][INFO] Registering route: PUT /api/users/:id
[2025-08-02 02:30:00][INFO] Registering route: DELETE /api/users/:id
[2025-08-02 02:30:00][INFO] Registering route: GET /api/stats
[2025-08-02 02:30:00][INFO] Registering route: GET /api/stats/:type
[2025-08-02 02:30:00][INFO] Dispatching request: GET /
[2025-08-02 02:30:00][WARNING] Route not found: GET /
[2025-08-02 02:30:00][INFO] Request: GET /@vite/client
[2025-08-02 02:30:00][INFO] Registering route: GET /api/health
[2025-08-02 02:30:00][INFO] Registering route: POST /api/admin/login
[2025-08-02 02:30:00][INFO] Registering route: POST /api/users/login
[2025-08-02 02:30:00][INFO] Registering route: GET /api/auth/captcha
[2025-08-02 02:30:00][INFO] Registering route: GET /api/auth/validate
[2025-08-02 02:30:00][INFO] Registering route: GET /api/users
[2025-08-02 02:30:00][INFO] Registering route: POST /api/users
[2025-08-02 02:30:00][INFO] Registering route: GET /api/users/profile
[2025-08-02 02:30:00][INFO] Registering route: GET /api/users/:id
[2025-08-02 02:30:00][INFO] Registering route: PUT /api/users/:id
[2025-08-02 02:30:00][INFO] Registering route: DELETE /api/users/:id
[2025-08-02 02:30:00][INFO] Registering route: GET /api/stats
[2025-08-02 02:30:00][INFO] Registering route: GET /api/stats/:type
[2025-08-02 02:30:00][INFO] Dispatching request: GET /@vite/client
[2025-08-02 02:30:00][WARNING] Route not found: GET /@vite/client
[2025-08-02 02:30:03][INFO] Request: POST /api/admin/login
[2025-08-02 02:30:03][INFO] Registering route: GET /api/health
[2025-08-02 02:30:03][INFO] Registering route: POST /api/admin/login
[2025-08-02 02:30:03][INFO] Registering route: POST /api/users/login
[2025-08-02 02:30:03][INFO] Registering route: GET /api/auth/captcha
[2025-08-02 02:30:03][INFO] Registering route: GET /api/auth/validate
[2025-08-02 02:30:03][INFO] Registering route: GET /api/users
[2025-08-02 02:30:03][INFO] Registering route: POST /api/users
[2025-08-02 02:30:03][INFO] Registering route: GET /api/users/profile
[2025-08-02 02:30:03][INFO] Registering route: GET /api/users/:id
[2025-08-02 02:30:03][INFO] Registering route: PUT /api/users/:id
[2025-08-02 02:30:03][INFO] Registering route: DELETE /api/users/:id
[2025-08-02 02:30:03][INFO] Registering route: GET /api/stats
[2025-08-02 02:30:03][INFO] Registering route: GET /api/stats/:type
[2025-08-02 02:30:03][INFO] Dispatching request: POST /api/admin/login
[2025-08-02 02:30:03][INFO] 数据库连接成功: 127.0.0.1:3306/plb_kj
[2025-08-02 02:30:03][INFO] Admin login attempt for username: admin
[2025-08-02 02:30:03][WARNING] Invalid captcha attempt for user: admin
[2025-08-02 02:30:03][ERROR] Admin login error: 验证码错误
[2025-08-02 02:30:03][INFO] Request: GET /api/auth/captcha
[2025-08-02 02:30:03][INFO] Registering route: GET /api/health
[2025-08-02 02:30:03][INFO] Registering route: POST /api/admin/login
[2025-08-02 02:30:03][INFO] Registering route: POST /api/users/login
[2025-08-02 02:30:03][INFO] Registering route: GET /api/auth/captcha
[2025-08-02 02:30:03][INFO] Registering route: GET /api/auth/validate
[2025-08-02 02:30:03][INFO] Registering route: GET /api/users
[2025-08-02 02:30:03][INFO] Registering route: POST /api/users
[2025-08-02 02:30:03][INFO] Registering route: GET /api/users/profile
[2025-08-02 02:30:03][INFO] Registering route: GET /api/users/:id
[2025-08-02 02:30:03][INFO] Registering route: PUT /api/users/:id
[2025-08-02 02:30:03][INFO] Registering route: DELETE /api/users/:id
[2025-08-02 02:30:03][INFO] Registering route: GET /api/stats
[2025-08-02 02:30:03][INFO] Registering route: GET /api/stats/:type
[2025-08-02 02:30:03][INFO] Dispatching request: GET /api/auth/captcha
[2025-08-02 02:30:03][INFO] 数据库连接成功: 127.0.0.1:3306/plb_kj
[2025-08-02 02:30:11][INFO] Request: POST /api/admin/login
[2025-08-02 02:30:11][INFO] Registering route: GET /api/health
[2025-08-02 02:30:11][INFO] Registering route: POST /api/admin/login
[2025-08-02 02:30:11][INFO] Registering route: POST /api/users/login
[2025-08-02 02:30:11][INFO] Registering route: GET /api/auth/captcha
[2025-08-02 02:30:11][INFO] Registering route: GET /api/auth/validate
[2025-08-02 02:30:11][INFO] Registering route: GET /api/users
[2025-08-02 02:30:11][INFO] Registering route: POST /api/users
[2025-08-02 02:30:11][INFO] Registering route: GET /api/users/profile
[2025-08-02 02:30:11][INFO] Registering route: GET /api/users/:id
[2025-08-02 02:30:11][INFO] Registering route: PUT /api/users/:id
[2025-08-02 02:30:11][INFO] Registering route: DELETE /api/users/:id
[2025-08-02 02:30:11][INFO] Registering route: GET /api/stats
[2025-08-02 02:30:11][INFO] Registering route: GET /api/stats/:type
[2025-08-02 02:30:11][INFO] Dispatching request: POST /api/admin/login
[2025-08-02 02:30:11][INFO] 数据库连接成功: 127.0.0.1:3306/plb_kj
[2025-08-02 02:30:11][INFO] Admin login attempt for username: admin
[2025-08-02 02:30:11][WARNING] Invalid captcha attempt for user: admin
[2025-08-02 02:30:11][ERROR] Admin login error: 验证码错误
[2025-08-02 02:30:11][INFO] Request: GET /api/auth/captcha
[2025-08-02 02:30:11][INFO] Registering route: GET /api/health
[2025-08-02 02:30:11][INFO] Registering route: POST /api/admin/login
[2025-08-02 02:30:11][INFO] Registering route: POST /api/users/login
[2025-08-02 02:30:11][INFO] Registering route: GET /api/auth/captcha
[2025-08-02 02:30:11][INFO] Registering route: GET /api/auth/validate
[2025-08-02 02:30:11][INFO] Registering route: GET /api/users
[2025-08-02 02:30:11][INFO] Registering route: POST /api/users
[2025-08-02 02:30:11][INFO] Registering route: GET /api/users/profile
[2025-08-02 02:30:11][INFO] Registering route: GET /api/users/:id
[2025-08-02 02:30:11][INFO] Registering route: PUT /api/users/:id
[2025-08-02 02:30:11][INFO] Registering route: DELETE /api/users/:id
[2025-08-02 02:30:11][INFO] Registering route: GET /api/stats
[2025-08-02 02:30:11][INFO] Registering route: GET /api/stats/:type
[2025-08-02 02:30:11][INFO] Dispatching request: GET /api/auth/captcha
[2025-08-02 02:30:11][INFO] 数据库连接成功: 127.0.0.1:3306/plb_kj
[2025-08-02 02:30:40][INFO] Request: POST /api/admin/login
[2025-08-02 02:30:40][INFO] Registering route: GET /api/health
[2025-08-02 02:30:40][INFO] Registering route: POST /api/admin/login
[2025-08-02 02:30:40][INFO] Registering route: POST /api/users/login
[2025-08-02 02:30:40][INFO] Registering route: GET /api/auth/captcha
[2025-08-02 02:30:40][INFO] Registering route: GET /api/auth/validate
[2025-08-02 02:30:40][INFO] Registering route: GET /api/users
[2025-08-02 02:30:40][INFO] Registering route: POST /api/users
[2025-08-02 02:30:40][INFO] Registering route: GET /api/users/profile
[2025-08-02 02:30:40][INFO] Registering route: GET /api/users/:id
[2025-08-02 02:30:40][INFO] Registering route: PUT /api/users/:id
[2025-08-02 02:30:40][INFO] Registering route: DELETE /api/users/:id
[2025-08-02 02:30:40][INFO] Registering route: GET /api/stats
[2025-08-02 02:30:40][INFO] Registering route: GET /api/stats/:type
[2025-08-02 02:30:40][INFO] Dispatching request: POST /api/admin/login
[2025-08-02 02:30:40][INFO] 数据库连接成功: 127.0.0.1:3306/plb_kj
[2025-08-02 02:30:40][INFO] Admin login attempt for username: admin
[2025-08-02 02:30:40][INFO] Admin user logged in successfully: admin
[2025-08-02 02:35:41][INFO] Request: POST /api/users
[2025-08-02 02:35:41][INFO] Registering route: GET /api/health
[2025-08-02 02:35:41][INFO] Registering route: POST /api/admin/login
[2025-08-02 02:35:41][INFO] Registering route: POST /api/users/login
[2025-08-02 02:35:41][INFO] Registering route: GET /api/auth/captcha
[2025-08-02 02:35:41][INFO] Registering route: GET /api/auth/validate
[2025-08-02 02:35:41][INFO] Registering route: GET /api/users
[2025-08-02 02:35:41][INFO] Registering route: POST /api/users
[2025-08-02 02:35:41][INFO] Registering route: GET /api/users/profile
[2025-08-02 02:35:41][INFO] Registering route: GET /api/users/:id
[2025-08-02 02:35:41][INFO] Registering route: PUT /api/users/:id
[2025-08-02 02:35:41][INFO] Registering route: DELETE /api/users/:id
[2025-08-02 02:35:41][INFO] Registering route: GET /api/stats
[2025-08-02 02:35:41][INFO] Registering route: GET /api/stats/:type
[2025-08-02 02:35:41][INFO] Dispatching request: POST /api/users
[2025-08-02 02:35:41][INFO] 数据库连接成功: 127.0.0.1:3306/plb_kj
[2025-08-02 02:35:56][INFO] Request: POST /api/users
[2025-08-02 02:35:56][INFO] Registering route: GET /api/health
[2025-08-02 02:35:56][INFO] Registering route: POST /api/admin/login
[2025-08-02 02:35:56][INFO] Registering route: POST /api/users/login
[2025-08-02 02:35:56][INFO] Registering route: GET /api/auth/captcha
[2025-08-02 02:35:56][INFO] Registering route: GET /api/auth/validate
[2025-08-02 02:35:56][INFO] Registering route: GET /api/users
[2025-08-02 02:35:56][INFO] Registering route: POST /api/users
[2025-08-02 02:35:56][INFO] Registering route: GET /api/users/profile
[2025-08-02 02:35:56][INFO] Registering route: GET /api/users/:id
[2025-08-02 02:35:56][INFO] Registering route: PUT /api/users/:id
[2025-08-02 02:35:56][INFO] Registering route: DELETE /api/users/:id
[2025-08-02 02:35:56][INFO] Registering route: GET /api/stats
[2025-08-02 02:35:56][INFO] Registering route: GET /api/stats/:type
[2025-08-02 02:35:56][INFO] Dispatching request: POST /api/users
[2025-08-02 02:35:56][INFO] 数据库连接成功: 127.0.0.1:3306/plb_kj
[2025-08-02 02:36:26][INFO] Request: GET /api/health
[2025-08-02 02:36:26][INFO] Registering route: GET /api/health
[2025-08-02 02:36:26][INFO] Registering route: POST /api/admin/login
[2025-08-02 02:36:26][INFO] Registering route: POST /api/users/login
[2025-08-02 02:36:26][INFO] Registering route: GET /api/auth/captcha
[2025-08-02 02:36:26][INFO] Registering route: GET /api/auth/validate
[2025-08-02 02:36:26][INFO] Registering route: GET /api/users
[2025-08-02 02:36:26][INFO] Registering route: POST /api/users
[2025-08-02 02:36:26][INFO] Registering route: GET /api/users/profile
[2025-08-02 02:36:26][INFO] Registering route: GET /api/users/:id
[2025-08-02 02:36:26][INFO] Registering route: PUT /api/users/:id
[2025-08-02 02:36:26][INFO] Registering route: DELETE /api/users/:id
[2025-08-02 02:36:26][INFO] Registering route: GET /api/stats
[2025-08-02 02:36:26][INFO] Registering route: GET /api/stats/:type
[2025-08-02 02:36:26][INFO] Dispatching request: GET /api/health
[2025-08-02 02:36:26][INFO] 数据库连接成功: 127.0.0.1:3306/plb_kj
[2025-08-02 02:48:17][INFO] Request: GET /api/auth/captcha
[2025-08-02 02:48:17][INFO] Registering route: GET /api/health
[2025-08-02 02:48:17][INFO] Registering route: POST /api/admin/login
[2025-08-02 02:48:17][INFO] Registering route: POST /api/users/login
[2025-08-02 02:48:17][INFO] Registering route: GET /api/auth/captcha
[2025-08-02 02:48:17][INFO] Registering route: GET /api/auth/validate
[2025-08-02 02:48:17][INFO] Registering route: GET /api/users
[2025-08-02 02:48:17][INFO] Registering route: POST /api/users
[2025-08-02 02:48:17][INFO] Registering route: GET /api/users/profile
[2025-08-02 02:48:17][INFO] Registering route: GET /api/users/:id
[2025-08-02 02:48:17][INFO] Registering route: PUT /api/users/:id
[2025-08-02 02:48:17][INFO] Registering route: DELETE /api/users/:id
[2025-08-02 02:48:17][INFO] Registering route: GET /api/stats
[2025-08-02 02:48:17][INFO] Registering route: GET /api/stats/:type
[2025-08-02 02:48:17][INFO] Dispatching request: GET /api/auth/captcha
[2025-08-02 02:48:17][INFO] 数据库连接成功: 127.0.0.1:3306/plb_kj
[2025-08-02 02:48:19][INFO] Request: GET /api/auth/validate
[2025-08-02 02:48:19][INFO] Registering route: GET /api/health
[2025-08-02 02:48:19][INFO] Registering route: POST /api/admin/login
[2025-08-02 02:48:19][INFO] Registering route: POST /api/users/login
[2025-08-02 02:48:19][INFO] Registering route: GET /api/auth/captcha
[2025-08-02 02:48:19][INFO] Registering route: GET /api/auth/validate
[2025-08-02 02:48:19][INFO] Registering route: GET /api/users
[2025-08-02 02:48:19][INFO] Registering route: POST /api/users
[2025-08-02 02:48:19][INFO] Registering route: GET /api/users/profile
[2025-08-02 02:48:19][INFO] Registering route: GET /api/users/:id
[2025-08-02 02:48:19][INFO] Registering route: PUT /api/users/:id
[2025-08-02 02:48:19][INFO] Registering route: DELETE /api/users/:id
[2025-08-02 02:48:19][INFO] Registering route: GET /api/stats
[2025-08-02 02:48:19][INFO] Registering route: GET /api/stats/:type
[2025-08-02 02:48:19][INFO] Dispatching request: GET /api/auth/validate
[2025-08-02 02:48:19][INFO] 数据库连接成功: 127.0.0.1:3306/plb_kj
[2025-08-02 02:48:19][ERROR] Token validation error: 无效的认证令牌
[2025-08-02 02:48:19][INFO] Request: GET /api/auth/captcha
[2025-08-02 02:48:19][INFO] Registering route: GET /api/health
[2025-08-02 02:48:19][INFO] Registering route: POST /api/admin/login
[2025-08-02 02:48:19][INFO] Registering route: POST /api/users/login
[2025-08-02 02:48:19][INFO] Registering route: GET /api/auth/captcha
[2025-08-02 02:48:19][INFO] Registering route: GET /api/auth/validate
[2025-08-02 02:48:19][INFO] Registering route: GET /api/users
[2025-08-02 02:48:19][INFO] Registering route: POST /api/users
[2025-08-02 02:48:19][INFO] Registering route: GET /api/users/profile
[2025-08-02 02:48:19][INFO] Registering route: GET /api/users/:id
[2025-08-02 02:48:19][INFO] Registering route: PUT /api/users/:id
[2025-08-02 02:48:19][INFO] Registering route: DELETE /api/users/:id
[2025-08-02 02:48:19][INFO] Registering route: GET /api/stats
[2025-08-02 02:48:19][INFO] Registering route: GET /api/stats/:type
[2025-08-02 02:48:19][INFO] Dispatching request: GET /api/auth/captcha
[2025-08-02 02:48:19][INFO] 数据库连接成功: 127.0.0.1:3306/plb_kj
[2025-08-02 02:48:41][INFO] Request: POST /api/users
[2025-08-02 02:48:41][INFO] Registering route: GET /api/health
[2025-08-02 02:48:41][INFO] Registering route: POST /api/admin/login
[2025-08-02 02:48:41][INFO] Registering route: POST /api/users/login
[2025-08-02 02:48:41][INFO] Registering route: GET /api/auth/captcha
[2025-08-02 02:48:41][INFO] Registering route: GET /api/auth/validate
[2025-08-02 02:48:41][INFO] Registering route: GET /api/users
[2025-08-02 02:48:41][INFO] Registering route: POST /api/users
[2025-08-02 02:48:41][INFO] Registering route: GET /api/users/profile
[2025-08-02 02:48:41][INFO] Registering route: GET /api/users/:id
[2025-08-02 02:48:41][INFO] Registering route: PUT /api/users/:id
[2025-08-02 02:48:41][INFO] Registering route: DELETE /api/users/:id
[2025-08-02 02:48:41][INFO] Registering route: GET /api/stats
[2025-08-02 02:48:41][INFO] Registering route: GET /api/stats/:type
[2025-08-02 02:48:41][INFO] Dispatching request: POST /api/users
[2025-08-02 02:48:41][INFO] 数据库连接成功: 127.0.0.1:3306/plb_kj
[2025-08-02 02:48:57][INFO] Request: POST /api/admin/login
[2025-08-02 02:48:57][INFO] Registering route: GET /api/health
[2025-08-02 02:48:57][INFO] Registering route: POST /api/admin/login
[2025-08-02 02:48:57][INFO] Registering route: POST /api/users/login
[2025-08-02 02:48:57][INFO] Registering route: GET /api/auth/captcha
[2025-08-02 02:48:57][INFO] Registering route: GET /api/auth/validate
[2025-08-02 02:48:57][INFO] Registering route: GET /api/users
[2025-08-02 02:48:57][INFO] Registering route: POST /api/users
[2025-08-02 02:48:57][INFO] Registering route: GET /api/users/profile
[2025-08-02 02:48:57][INFO] Registering route: GET /api/users/:id
[2025-08-02 02:48:57][INFO] Registering route: PUT /api/users/:id
[2025-08-02 02:48:57][INFO] Registering route: DELETE /api/users/:id
[2025-08-02 02:48:57][INFO] Registering route: GET /api/stats
[2025-08-02 02:48:57][INFO] Registering route: GET /api/stats/:type
[2025-08-02 02:48:57][INFO] Dispatching request: POST /api/admin/login
[2025-08-02 02:48:57][INFO] 数据库连接成功: 127.0.0.1:3306/plb_kj
[2025-08-02 02:48:57][INFO] Admin login attempt for username: test01
[2025-08-02 02:48:57][WARNING] Failed login attempt for admin user: test01
[2025-08-02 02:48:57][ERROR] Admin login error: 用户名或密码错误
[2025-08-02 02:48:57][INFO] Request: GET /api/auth/captcha
[2025-08-02 02:48:57][INFO] Registering route: GET /api/health
[2025-08-02 02:48:57][INFO] Registering route: POST /api/admin/login
[2025-08-02 02:48:57][INFO] Registering route: POST /api/users/login
[2025-08-02 02:48:57][INFO] Registering route: GET /api/auth/captcha
[2025-08-02 02:48:57][INFO] Registering route: GET /api/auth/validate
[2025-08-02 02:48:57][INFO] Registering route: GET /api/users
[2025-08-02 02:48:57][INFO] Registering route: POST /api/users
[2025-08-02 02:48:57][INFO] Registering route: GET /api/users/profile
[2025-08-02 02:48:57][INFO] Registering route: GET /api/users/:id
[2025-08-02 02:48:57][INFO] Registering route: PUT /api/users/:id
[2025-08-02 02:48:57][INFO] Registering route: DELETE /api/users/:id
[2025-08-02 02:48:57][INFO] Registering route: GET /api/stats
[2025-08-02 02:48:57][INFO] Registering route: GET /api/stats/:type
[2025-08-02 02:48:57][INFO] Dispatching request: GET /api/auth/captcha
[2025-08-02 02:48:57][INFO] 数据库连接成功: 127.0.0.1:3306/plb_kj
[2025-08-02 02:49:11][INFO] Request: POST /api/admin/login
[2025-08-02 02:49:11][INFO] Registering route: GET /api/health
[2025-08-02 02:49:11][INFO] Registering route: POST /api/admin/login
[2025-08-02 02:49:11][INFO] Registering route: POST /api/users/login
[2025-08-02 02:49:11][INFO] Registering route: GET /api/auth/captcha
[2025-08-02 02:49:11][INFO] Registering route: GET /api/auth/validate
[2025-08-02 02:49:11][INFO] Registering route: GET /api/users
[2025-08-02 02:49:11][INFO] Registering route: POST /api/users
[2025-08-02 02:49:11][INFO] Registering route: GET /api/users/profile
[2025-08-02 02:49:11][INFO] Registering route: GET /api/users/:id
[2025-08-02 02:49:11][INFO] Registering route: PUT /api/users/:id
[2025-08-02 02:49:11][INFO] Registering route: DELETE /api/users/:id
[2025-08-02 02:49:11][INFO] Registering route: GET /api/stats
[2025-08-02 02:49:11][INFO] Registering route: GET /api/stats/:type
[2025-08-02 02:49:11][INFO] Dispatching request: POST /api/admin/login
[2025-08-02 02:49:11][INFO] 数据库连接成功: 127.0.0.1:3306/plb_kj
[2025-08-02 02:49:11][INFO] Admin login attempt for username: test01
[2025-08-02 02:49:11][WARNING] Failed login attempt for admin user: test01
[2025-08-02 02:49:11][ERROR] Admin login error: 用户名或密码错误
[2025-08-02 02:49:11][INFO] Request: GET /api/auth/captcha
[2025-08-02 02:49:11][INFO] Registering route: GET /api/health
[2025-08-02 02:49:11][INFO] Registering route: POST /api/admin/login
[2025-08-02 02:49:11][INFO] Registering route: POST /api/users/login
[2025-08-02 02:49:11][INFO] Registering route: GET /api/auth/captcha
[2025-08-02 02:49:11][INFO] Registering route: GET /api/auth/validate
[2025-08-02 02:49:11][INFO] Registering route: GET /api/users
[2025-08-02 02:49:11][INFO] Registering route: POST /api/users
[2025-08-02 02:49:11][INFO] Registering route: GET /api/users/profile
[2025-08-02 02:49:11][INFO] Registering route: GET /api/users/:id
[2025-08-02 02:49:11][INFO] Registering route: PUT /api/users/:id
[2025-08-02 02:49:11][INFO] Registering route: DELETE /api/users/:id
[2025-08-02 02:49:11][INFO] Registering route: GET /api/stats
[2025-08-02 02:49:11][INFO] Registering route: GET /api/stats/:type
[2025-08-02 02:49:11][INFO] Dispatching request: GET /api/auth/captcha
[2025-08-02 02:49:11][INFO] 数据库连接成功: 127.0.0.1:3306/plb_kj
[2025-08-02 02:49:21][INFO] Request: POST /api/admin/login
[2025-08-02 02:49:21][INFO] Registering route: GET /api/health
[2025-08-02 02:49:21][INFO] Registering route: POST /api/admin/login
[2025-08-02 02:49:21][INFO] Registering route: POST /api/users/login
[2025-08-02 02:49:21][INFO] Registering route: GET /api/auth/captcha
[2025-08-02 02:49:21][INFO] Registering route: GET /api/auth/validate
[2025-08-02 02:49:21][INFO] Registering route: GET /api/users
[2025-08-02 02:49:21][INFO] Registering route: POST /api/users
[2025-08-02 02:49:21][INFO] Registering route: GET /api/users/profile
[2025-08-02 02:49:21][INFO] Registering route: GET /api/users/:id
[2025-08-02 02:49:21][INFO] Registering route: PUT /api/users/:id
[2025-08-02 02:49:21][INFO] Registering route: DELETE /api/users/:id
[2025-08-02 02:49:21][INFO] Registering route: GET /api/stats
[2025-08-02 02:49:21][INFO] Registering route: GET /api/stats/:type
[2025-08-02 02:49:21][INFO] Dispatching request: POST /api/admin/login
[2025-08-02 02:49:21][INFO] 数据库连接成功: 127.0.0.1:3306/plb_kj
[2025-08-02 02:49:21][INFO] Admin login attempt for username: test01
[2025-08-02 02:49:21][WARNING] Invalid captcha attempt for user: test01
[2025-08-02 02:49:21][ERROR] Admin login error: 验证码错误
[2025-08-02 02:49:21][INFO] Request: GET /api/auth/captcha
[2025-08-02 02:49:21][INFO] Registering route: GET /api/health
[2025-08-02 02:49:21][INFO] Registering route: POST /api/admin/login
[2025-08-02 02:49:21][INFO] Registering route: POST /api/users/login
[2025-08-02 02:49:21][INFO] Registering route: GET /api/auth/captcha
[2025-08-02 02:49:21][INFO] Registering route: GET /api/auth/validate
[2025-08-02 02:49:21][INFO] Registering route: GET /api/users
[2025-08-02 02:49:21][INFO] Registering route: POST /api/users
[2025-08-02 02:49:21][INFO] Registering route: GET /api/users/profile
[2025-08-02 02:49:21][INFO] Registering route: GET /api/users/:id
[2025-08-02 02:49:21][INFO] Registering route: PUT /api/users/:id
[2025-08-02 02:49:21][INFO] Registering route: DELETE /api/users/:id
[2025-08-02 02:49:21][INFO] Registering route: GET /api/stats
[2025-08-02 02:49:21][INFO] Registering route: GET /api/stats/:type
[2025-08-02 02:49:21][INFO] Dispatching request: GET /api/auth/captcha
[2025-08-02 02:49:21][INFO] 数据库连接成功: 127.0.0.1:3306/plb_kj
[2025-08-02 02:53:16][INFO] Request: GET /api/auth/captcha
[2025-08-02 02:53:16][INFO] Registering route: GET /api/health
[2025-08-02 02:53:16][INFO] Registering route: POST /api/admin/login
[2025-08-02 02:53:16][INFO] Registering route: POST /api/users/login
[2025-08-02 02:53:16][INFO] Registering route: GET /api/auth/captcha
[2025-08-02 02:53:16][INFO] Registering route: GET /api/auth/validate
[2025-08-02 02:53:16][INFO] Registering route: GET /api/users
[2025-08-02 02:53:16][INFO] Registering route: POST /api/users
[2025-08-02 02:53:16][INFO] Registering route: GET /api/users/profile
[2025-08-02 02:53:16][INFO] Registering route: GET /api/users/:id
[2025-08-02 02:53:16][INFO] Registering route: PUT /api/users/:id
[2025-08-02 02:53:16][INFO] Registering route: DELETE /api/users/:id
[2025-08-02 02:53:16][INFO] Registering route: GET /api/stats
[2025-08-02 02:53:16][INFO] Registering route: GET /api/stats/:type
[2025-08-02 02:53:16][INFO] Dispatching request: GET /api/auth/captcha
[2025-08-02 02:53:16][INFO] 数据库连接成功: 127.0.0.1:3306/plb_kj
[2025-08-02 02:53:17][INFO] Request: GET /api/auth/validate
[2025-08-02 02:53:17][INFO] Registering route: GET /api/health
[2025-08-02 02:53:17][INFO] Registering route: POST /api/admin/login
[2025-08-02 02:53:17][INFO] Registering route: POST /api/users/login
[2025-08-02 02:53:17][INFO] Registering route: GET /api/auth/captcha
[2025-08-02 02:53:17][INFO] Registering route: GET /api/auth/validate
[2025-08-02 02:53:17][INFO] Registering route: GET /api/users
[2025-08-02 02:53:17][INFO] Registering route: POST /api/users
[2025-08-02 02:53:17][INFO] Registering route: GET /api/users/profile
[2025-08-02 02:53:17][INFO] Registering route: GET /api/users/:id
[2025-08-02 02:53:17][INFO] Registering route: PUT /api/users/:id
[2025-08-02 02:53:17][INFO] Registering route: DELETE /api/users/:id
[2025-08-02 02:53:17][INFO] Registering route: GET /api/stats
[2025-08-02 02:53:17][INFO] Registering route: GET /api/stats/:type
[2025-08-02 02:53:17][INFO] Dispatching request: GET /api/auth/validate
[2025-08-02 02:53:17][INFO] 数据库连接成功: 127.0.0.1:3306/plb_kj
[2025-08-02 02:53:17][ERROR] Token validation error: 无效的认证令牌
[2025-08-02 02:53:18][INFO] Request: GET /api/auth/captcha
[2025-08-02 02:53:18][INFO] Registering route: GET /api/health
[2025-08-02 02:53:18][INFO] Registering route: POST /api/admin/login
[2025-08-02 02:53:18][INFO] Registering route: POST /api/users/login
[2025-08-02 02:53:18][INFO] Registering route: GET /api/auth/captcha
[2025-08-02 02:53:18][INFO] Registering route: GET /api/auth/validate
[2025-08-02 02:53:18][INFO] Registering route: GET /api/users
[2025-08-02 02:53:18][INFO] Registering route: POST /api/users
[2025-08-02 02:53:18][INFO] Registering route: GET /api/users/profile
[2025-08-02 02:53:18][INFO] Registering route: GET /api/users/:id
[2025-08-02 02:53:18][INFO] Registering route: PUT /api/users/:id
[2025-08-02 02:53:18][INFO] Registering route: DELETE /api/users/:id
[2025-08-02 02:53:18][INFO] Registering route: GET /api/stats
[2025-08-02 02:53:18][INFO] Registering route: GET /api/stats/:type
[2025-08-02 02:53:18][INFO] Dispatching request: GET /api/auth/captcha
[2025-08-02 02:53:18][INFO] 数据库连接成功: 127.0.0.1:3306/plb_kj
[2025-08-02 02:54:38][INFO] Request: GET /api/auth/captcha
[2025-08-02 02:54:38][INFO] Registering route: GET /api/health
[2025-08-02 02:54:38][INFO] Registering route: POST /api/admin/login
[2025-08-02 02:54:38][INFO] Registering route: POST /api/users/login
[2025-08-02 02:54:38][INFO] Registering route: GET /api/auth/captcha
[2025-08-02 02:54:38][INFO] Registering route: GET /api/auth/validate
[2025-08-02 02:54:38][INFO] Registering route: GET /api/users
[2025-08-02 02:54:38][INFO] Registering route: POST /api/users
[2025-08-02 02:54:38][INFO] Registering route: GET /api/users/profile
[2025-08-02 02:54:38][INFO] Registering route: GET /api/users/:id
[2025-08-02 02:54:38][INFO] Registering route: PUT /api/users/:id
[2025-08-02 02:54:38][INFO] Registering route: DELETE /api/users/:id
[2025-08-02 02:54:38][INFO] Registering route: GET /api/stats
[2025-08-02 02:54:38][INFO] Registering route: GET /api/stats/:type
[2025-08-02 02:54:38][INFO] Dispatching request: GET /api/auth/captcha
[2025-08-02 02:54:38][INFO] 数据库连接成功: 127.0.0.1:3306/plb_kj
[2025-08-02 02:54:41][INFO] Request: GET /api/auth/validate
[2025-08-02 02:54:41][INFO] Registering route: GET /api/health
[2025-08-02 02:54:41][INFO] Registering route: POST /api/admin/login
[2025-08-02 02:54:41][INFO] Registering route: POST /api/users/login
[2025-08-02 02:54:41][INFO] Registering route: GET /api/auth/captcha
[2025-08-02 02:54:41][INFO] Registering route: GET /api/auth/validate
[2025-08-02 02:54:41][INFO] Registering route: GET /api/users
[2025-08-02 02:54:41][INFO] Registering route: POST /api/users
[2025-08-02 02:54:41][INFO] Registering route: GET /api/users/profile
[2025-08-02 02:54:41][INFO] Registering route: GET /api/users/:id
[2025-08-02 02:54:41][INFO] Registering route: PUT /api/users/:id
[2025-08-02 02:54:41][INFO] Registering route: DELETE /api/users/:id
[2025-08-02 02:54:41][INFO] Registering route: GET /api/stats
[2025-08-02 02:54:41][INFO] Registering route: GET /api/stats/:type
[2025-08-02 02:54:41][INFO] Dispatching request: GET /api/auth/validate
[2025-08-02 02:54:41][INFO] 数据库连接成功: 127.0.0.1:3306/plb_kj
[2025-08-02 02:54:41][ERROR] Token validation error: 无效的认证令牌
[2025-08-02 02:54:41][INFO] Request: GET /api/auth/captcha
[2025-08-02 02:54:41][INFO] Registering route: GET /api/health
[2025-08-02 02:54:41][INFO] Registering route: POST /api/admin/login
[2025-08-02 02:54:41][INFO] Registering route: POST /api/users/login
[2025-08-02 02:54:41][INFO] Registering route: GET /api/auth/captcha
[2025-08-02 02:54:41][INFO] Registering route: GET /api/auth/validate
[2025-08-02 02:54:41][INFO] Registering route: GET /api/users
[2025-08-02 02:54:41][INFO] Registering route: POST /api/users
[2025-08-02 02:54:41][INFO] Registering route: GET /api/users/profile
[2025-08-02 02:54:41][INFO] Registering route: GET /api/users/:id
[2025-08-02 02:54:41][INFO] Registering route: PUT /api/users/:id
[2025-08-02 02:54:41][INFO] Registering route: DELETE /api/users/:id
[2025-08-02 02:54:41][INFO] Registering route: GET /api/stats
[2025-08-02 02:54:41][INFO] Registering route: GET /api/stats/:type
[2025-08-02 02:54:41][INFO] Dispatching request: GET /api/auth/captcha
[2025-08-02 02:54:41][INFO] 数据库连接成功: 127.0.0.1:3306/plb_kj
[2025-08-02 02:55:14][INFO] Request: GET /api/auth/captcha
[2025-08-02 02:55:14][INFO] Registering route: GET /api/health
[2025-08-02 02:55:14][INFO] Registering route: POST /api/admin/login
[2025-08-02 02:55:14][INFO] Registering route: POST /api/users/login
[2025-08-02 02:55:14][INFO] Registering route: GET /api/auth/captcha
[2025-08-02 02:55:14][INFO] Registering route: GET /api/auth/validate
[2025-08-02 02:55:14][INFO] Registering route: GET /api/users
[2025-08-02 02:55:14][INFO] Registering route: POST /api/users
[2025-08-02 02:55:14][INFO] Registering route: GET /api/users/profile
[2025-08-02 02:55:14][INFO] Registering route: GET /api/users/:id
[2025-08-02 02:55:14][INFO] Registering route: PUT /api/users/:id
[2025-08-02 02:55:14][INFO] Registering route: DELETE /api/users/:id
[2025-08-02 02:55:14][INFO] Registering route: GET /api/stats
[2025-08-02 02:55:14][INFO] Registering route: GET /api/stats/:type
[2025-08-02 02:55:14][INFO] Dispatching request: GET /api/auth/captcha
[2025-08-02 02:55:14][INFO] 数据库连接成功: 127.0.0.1:3306/plb_kj
[2025-08-02 02:55:16][INFO] Request: GET /api/auth/validate
[2025-08-02 02:55:16][INFO] Registering route: GET /api/health
[2025-08-02 02:55:16][INFO] Registering route: POST /api/admin/login
[2025-08-02 02:55:16][INFO] Registering route: POST /api/users/login
[2025-08-02 02:55:16][INFO] Registering route: GET /api/auth/captcha
[2025-08-02 02:55:16][INFO] Registering route: GET /api/auth/validate
[2025-08-02 02:55:16][INFO] Registering route: GET /api/users
[2025-08-02 02:55:16][INFO] Registering route: POST /api/users
[2025-08-02 02:55:16][INFO] Registering route: GET /api/users/profile
[2025-08-02 02:55:16][INFO] Registering route: GET /api/users/:id
[2025-08-02 02:55:16][INFO] Registering route: PUT /api/users/:id
[2025-08-02 02:55:16][INFO] Registering route: DELETE /api/users/:id
[2025-08-02 02:55:16][INFO] Registering route: GET /api/stats
[2025-08-02 02:55:16][INFO] Registering route: GET /api/stats/:type
[2025-08-02 02:55:16][INFO] Dispatching request: GET /api/auth/validate
[2025-08-02 02:55:16][INFO] 数据库连接成功: 127.0.0.1:3306/plb_kj
[2025-08-02 02:55:16][ERROR] Token validation error: 无效的认证令牌
[2025-08-02 02:55:17][INFO] Request: GET /api/auth/captcha
[2025-08-02 02:55:17][INFO] Registering route: GET /api/health
[2025-08-02 02:55:17][INFO] Registering route: POST /api/admin/login
[2025-08-02 02:55:17][INFO] Registering route: POST /api/users/login
[2025-08-02 02:55:17][INFO] Registering route: GET /api/auth/captcha
[2025-08-02 02:55:17][INFO] Registering route: GET /api/auth/validate
[2025-08-02 02:55:17][INFO] Registering route: GET /api/users
[2025-08-02 02:55:17][INFO] Registering route: POST /api/users
[2025-08-02 02:55:17][INFO] Registering route: GET /api/users/profile
[2025-08-02 02:55:17][INFO] Registering route: GET /api/users/:id
[2025-08-02 02:55:17][INFO] Registering route: PUT /api/users/:id
[2025-08-02 02:55:17][INFO] Registering route: DELETE /api/users/:id
[2025-08-02 02:55:17][INFO] Registering route: GET /api/stats
[2025-08-02 02:55:17][INFO] Registering route: GET /api/stats/:type
[2025-08-02 02:55:17][INFO] Dispatching request: GET /api/auth/captcha
[2025-08-02 02:55:17][INFO] 数据库连接成功: 127.0.0.1:3306/plb_kj
[2025-08-02 02:56:03][INFO] Request: GET /api/auth/captcha
[2025-08-02 02:56:03][INFO] Registering route: GET /api/health
[2025-08-02 02:56:03][INFO] Registering route: POST /api/admin/login
[2025-08-02 02:56:03][INFO] Registering route: POST /api/users/login
[2025-08-02 02:56:03][INFO] Registering route: GET /api/auth/captcha
[2025-08-02 02:56:03][INFO] Registering route: GET /api/auth/validate
[2025-08-02 02:56:03][INFO] Registering route: GET /api/users
[2025-08-02 02:56:03][INFO] Registering route: POST /api/users
[2025-08-02 02:56:03][INFO] Registering route: GET /api/users/profile
[2025-08-02 02:56:03][INFO] Registering route: GET /api/users/:id
[2025-08-02 02:56:03][INFO] Registering route: PUT /api/users/:id
[2025-08-02 02:56:03][INFO] Registering route: DELETE /api/users/:id
[2025-08-02 02:56:03][INFO] Registering route: GET /api/stats
[2025-08-02 02:56:03][INFO] Registering route: GET /api/stats/:type
[2025-08-02 02:56:03][INFO] Dispatching request: GET /api/auth/captcha
[2025-08-02 02:56:03][INFO] 数据库连接成功: 127.0.0.1:3306/plb_kj
[2025-08-02 02:56:05][INFO] Request: GET /api/auth/validate
[2025-08-02 02:56:05][INFO] Registering route: GET /api/health
[2025-08-02 02:56:05][INFO] Registering route: POST /api/admin/login
[2025-08-02 02:56:05][INFO] Registering route: POST /api/users/login
[2025-08-02 02:56:05][INFO] Registering route: GET /api/auth/captcha
[2025-08-02 02:56:05][INFO] Registering route: GET /api/auth/validate
[2025-08-02 02:56:05][INFO] Registering route: GET /api/users
[2025-08-02 02:56:05][INFO] Registering route: POST /api/users
[2025-08-02 02:56:05][INFO] Registering route: GET /api/users/profile
[2025-08-02 02:56:05][INFO] Registering route: GET /api/users/:id
[2025-08-02 02:56:05][INFO] Registering route: PUT /api/users/:id
[2025-08-02 02:56:05][INFO] Registering route: DELETE /api/users/:id
[2025-08-02 02:56:05][INFO] Registering route: GET /api/stats
[2025-08-02 02:56:05][INFO] Registering route: GET /api/stats/:type
[2025-08-02 02:56:05][INFO] Dispatching request: GET /api/auth/validate
[2025-08-02 02:56:05][INFO] 数据库连接成功: 127.0.0.1:3306/plb_kj
[2025-08-02 02:56:05][ERROR] Token validation error: 无效的认证令牌
[2025-08-02 02:56:05][INFO] Request: GET /api/auth/captcha
[2025-08-02 02:56:05][INFO] Registering route: GET /api/health
[2025-08-02 02:56:05][INFO] Registering route: POST /api/admin/login
[2025-08-02 02:56:05][INFO] Registering route: POST /api/users/login
[2025-08-02 02:56:05][INFO] Registering route: GET /api/auth/captcha
[2025-08-02 02:56:05][INFO] Registering route: GET /api/auth/validate
[2025-08-02 02:56:05][INFO] Registering route: GET /api/users
[2025-08-02 02:56:05][INFO] Registering route: POST /api/users
[2025-08-02 02:56:05][INFO] Registering route: GET /api/users/profile
[2025-08-02 02:56:05][INFO] Registering route: GET /api/users/:id
[2025-08-02 02:56:05][INFO] Registering route: PUT /api/users/:id
[2025-08-02 02:56:05][INFO] Registering route: DELETE /api/users/:id
[2025-08-02 02:56:05][INFO] Registering route: GET /api/stats
[2025-08-02 02:56:05][INFO] Registering route: GET /api/stats/:type
[2025-08-02 02:56:05][INFO] Dispatching request: GET /api/auth/captcha
[2025-08-02 02:56:05][INFO] 数据库连接成功: 127.0.0.1:3306/plb_kj
[2025-08-02 02:56:58][INFO] Request: GET /api/auth/captcha
[2025-08-02 02:56:58][INFO] Registering route: GET /api/health
[2025-08-02 02:56:58][INFO] Registering route: POST /api/admin/login
[2025-08-02 02:56:58][INFO] Registering route: POST /api/users/login
[2025-08-02 02:56:58][INFO] Registering route: GET /api/auth/captcha
[2025-08-02 02:56:58][INFO] Registering route: GET /api/auth/validate
[2025-08-02 02:56:58][INFO] Registering route: GET /api/users
[2025-08-02 02:56:58][INFO] Registering route: POST /api/users
[2025-08-02 02:56:58][INFO] Registering route: GET /api/users/profile
[2025-08-02 02:56:58][INFO] Registering route: GET /api/users/:id
[2025-08-02 02:56:58][INFO] Registering route: PUT /api/users/:id
[2025-08-02 02:56:58][INFO] Registering route: DELETE /api/users/:id
[2025-08-02 02:56:58][INFO] Registering route: GET /api/stats
[2025-08-02 02:56:58][INFO] Registering route: GET /api/stats/:type
[2025-08-02 02:56:58][INFO] Dispatching request: GET /api/auth/captcha
[2025-08-02 02:56:58][INFO] 数据库连接成功: 127.0.0.1:3306/plb_kj
[2025-08-02 02:57:00][INFO] Request: GET /api/auth/validate
[2025-08-02 02:57:00][INFO] Registering route: GET /api/health
[2025-08-02 02:57:00][INFO] Registering route: POST /api/admin/login
[2025-08-02 02:57:00][INFO] Registering route: POST /api/users/login
[2025-08-02 02:57:00][INFO] Registering route: GET /api/auth/captcha
[2025-08-02 02:57:00][INFO] Registering route: GET /api/auth/validate
[2025-08-02 02:57:00][INFO] Registering route: GET /api/users
[2025-08-02 02:57:00][INFO] Registering route: POST /api/users
[2025-08-02 02:57:00][INFO] Registering route: GET /api/users/profile
[2025-08-02 02:57:00][INFO] Registering route: GET /api/users/:id
[2025-08-02 02:57:00][INFO] Registering route: PUT /api/users/:id
[2025-08-02 02:57:00][INFO] Registering route: DELETE /api/users/:id
[2025-08-02 02:57:00][INFO] Registering route: GET /api/stats
[2025-08-02 02:57:00][INFO] Registering route: GET /api/stats/:type
[2025-08-02 02:57:00][INFO] Dispatching request: GET /api/auth/validate
[2025-08-02 02:57:00][INFO] 数据库连接成功: 127.0.0.1:3306/plb_kj
[2025-08-02 02:57:00][ERROR] Token validation error: 无效的认证令牌
[2025-08-02 02:57:00][INFO] Request: GET /api/auth/captcha
[2025-08-02 02:57:00][INFO] Registering route: GET /api/health
[2025-08-02 02:57:00][INFO] Registering route: POST /api/admin/login
[2025-08-02 02:57:00][INFO] Registering route: POST /api/users/login
[2025-08-02 02:57:00][INFO] Registering route: GET /api/auth/captcha
[2025-08-02 02:57:00][INFO] Registering route: GET /api/auth/validate
[2025-08-02 02:57:00][INFO] Registering route: GET /api/users
[2025-08-02 02:57:00][INFO] Registering route: POST /api/users
[2025-08-02 02:57:00][INFO] Registering route: GET /api/users/profile
[2025-08-02 02:57:00][INFO] Registering route: GET /api/users/:id
[2025-08-02 02:57:00][INFO] Registering route: PUT /api/users/:id
[2025-08-02 02:57:00][INFO] Registering route: DELETE /api/users/:id
[2025-08-02 02:57:00][INFO] Registering route: GET /api/stats
[2025-08-02 02:57:00][INFO] Registering route: GET /api/stats/:type
[2025-08-02 02:57:00][INFO] Dispatching request: GET /api/auth/captcha
[2025-08-02 02:57:00][INFO] 数据库连接成功: 127.0.0.1:3306/plb_kj
[2025-08-02 03:05:48][INFO] Request: GET /api/settings/background
[2025-08-02 03:05:48][INFO] Registering route: GET /api/health
[2025-08-02 03:05:48][INFO] Registering route: POST /api/admin/login
[2025-08-02 03:05:48][INFO] Registering route: POST /api/users/login
[2025-08-02 03:05:48][INFO] Registering route: GET /api/auth/captcha
[2025-08-02 03:05:48][INFO] Registering route: GET /api/auth/validate
[2025-08-02 03:05:48][INFO] Registering route: GET /api/users
[2025-08-02 03:05:48][INFO] Registering route: POST /api/users
[2025-08-02 03:05:48][INFO] Registering route: GET /api/users/profile
[2025-08-02 03:05:48][INFO] Registering route: GET /api/users/:id
[2025-08-02 03:05:48][INFO] Registering route: PUT /api/users/:id
[2025-08-02 03:05:48][INFO] Registering route: DELETE /api/users/:id
[2025-08-02 03:05:48][INFO] Registering route: GET /api/stats
[2025-08-02 03:05:48][INFO] Registering route: GET /api/stats/:type
[2025-08-02 03:05:48][INFO] Registering route: GET /api/settings/background
[2025-08-02 03:05:48][INFO] Registering route: POST /api/settings/background
[2025-08-02 03:05:48][INFO] Dispatching request: GET /api/settings/background
[2025-08-02 03:05:48][INFO] 数据库连接成功: 127.0.0.1:3306/plb_kj
[2025-08-02 03:05:48][INFO] Request: GET /api/auth/captcha
[2025-08-02 03:05:48][INFO] Registering route: GET /api/health
[2025-08-02 03:05:48][INFO] Registering route: POST /api/admin/login
[2025-08-02 03:05:48][INFO] Registering route: POST /api/users/login
[2025-08-02 03:05:48][INFO] Registering route: GET /api/auth/captcha
[2025-08-02 03:05:48][INFO] Registering route: GET /api/auth/validate
[2025-08-02 03:05:48][INFO] Registering route: GET /api/users
[2025-08-02 03:05:48][INFO] Registering route: POST /api/users
[2025-08-02 03:05:48][INFO] Registering route: GET /api/users/profile
[2025-08-02 03:05:48][INFO] Registering route: GET /api/users/:id
[2025-08-02 03:05:48][INFO] Registering route: PUT /api/users/:id
[2025-08-02 03:05:48][INFO] Registering route: DELETE /api/users/:id
[2025-08-02 03:05:48][INFO] Registering route: GET /api/stats
[2025-08-02 03:05:48][INFO] Registering route: GET /api/stats/:type
[2025-08-02 03:05:48][INFO] Registering route: GET /api/settings/background
[2025-08-02 03:05:48][INFO] Registering route: POST /api/settings/background
[2025-08-02 03:05:48][INFO] Dispatching request: GET /api/auth/captcha
[2025-08-02 03:05:48][INFO] 数据库连接成功: 127.0.0.1:3306/plb_kj
[2025-08-02 03:05:49][INFO] Request: GET /api/auth/validate
[2025-08-02 03:05:49][INFO] Registering route: GET /api/health
[2025-08-02 03:05:49][INFO] Registering route: POST /api/admin/login
[2025-08-02 03:05:49][INFO] Registering route: POST /api/users/login
[2025-08-02 03:05:49][INFO] Registering route: GET /api/auth/captcha
[2025-08-02 03:05:49][INFO] Registering route: GET /api/auth/validate
[2025-08-02 03:05:49][INFO] Registering route: GET /api/users
[2025-08-02 03:05:49][INFO] Registering route: POST /api/users
[2025-08-02 03:05:49][INFO] Registering route: GET /api/users/profile
[2025-08-02 03:05:49][INFO] Registering route: GET /api/users/:id
[2025-08-02 03:05:49][INFO] Registering route: PUT /api/users/:id
[2025-08-02 03:05:49][INFO] Registering route: DELETE /api/users/:id
[2025-08-02 03:05:49][INFO] Registering route: GET /api/stats
[2025-08-02 03:05:49][INFO] Registering route: GET /api/stats/:type
[2025-08-02 03:05:49][INFO] Registering route: GET /api/settings/background
[2025-08-02 03:05:49][INFO] Registering route: POST /api/settings/background
[2025-08-02 03:05:49][INFO] Dispatching request: GET /api/auth/validate
[2025-08-02 03:05:49][INFO] 数据库连接成功: 127.0.0.1:3306/plb_kj
[2025-08-02 03:05:49][ERROR] Token validation error: 无效的认证令牌
[2025-08-02 03:05:50][INFO] Request: GET /api/settings/background
[2025-08-02 03:05:50][INFO] Registering route: GET /api/health
[2025-08-02 03:05:50][INFO] Registering route: POST /api/admin/login
[2025-08-02 03:05:50][INFO] Registering route: POST /api/users/login
[2025-08-02 03:05:50][INFO] Registering route: GET /api/auth/captcha
[2025-08-02 03:05:50][INFO] Registering route: GET /api/auth/validate
[2025-08-02 03:05:50][INFO] Registering route: GET /api/users
[2025-08-02 03:05:50][INFO] Registering route: POST /api/users
[2025-08-02 03:05:50][INFO] Registering route: GET /api/users/profile
[2025-08-02 03:05:50][INFO] Registering route: GET /api/users/:id
[2025-08-02 03:05:50][INFO] Registering route: PUT /api/users/:id
[2025-08-02 03:05:50][INFO] Registering route: DELETE /api/users/:id
[2025-08-02 03:05:50][INFO] Registering route: GET /api/stats
[2025-08-02 03:05:50][INFO] Registering route: GET /api/stats/:type
[2025-08-02 03:05:50][INFO] Registering route: GET /api/settings/background
[2025-08-02 03:05:50][INFO] Registering route: POST /api/settings/background
[2025-08-02 03:05:50][INFO] Dispatching request: GET /api/settings/background
[2025-08-02 03:05:50][INFO] 数据库连接成功: 127.0.0.1:3306/plb_kj
[2025-08-02 03:05:50][INFO] Request: GET /api/auth/captcha
[2025-08-02 03:05:50][INFO] Registering route: GET /api/health
[2025-08-02 03:05:50][INFO] Registering route: POST /api/admin/login
[2025-08-02 03:05:50][INFO] Registering route: POST /api/users/login
[2025-08-02 03:05:50][INFO] Registering route: GET /api/auth/captcha
[2025-08-02 03:05:50][INFO] Registering route: GET /api/auth/validate
[2025-08-02 03:05:50][INFO] Registering route: GET /api/users
[2025-08-02 03:05:50][INFO] Registering route: POST /api/users
[2025-08-02 03:05:50][INFO] Registering route: GET /api/users/profile
[2025-08-02 03:05:50][INFO] Registering route: GET /api/users/:id
[2025-08-02 03:05:50][INFO] Registering route: PUT /api/users/:id
[2025-08-02 03:05:50][INFO] Registering route: DELETE /api/users/:id
[2025-08-02 03:05:50][INFO] Registering route: GET /api/stats
[2025-08-02 03:05:50][INFO] Registering route: GET /api/stats/:type
[2025-08-02 03:05:50][INFO] Registering route: GET /api/settings/background
[2025-08-02 03:05:50][INFO] Registering route: POST /api/settings/background
[2025-08-02 03:05:50][INFO] Dispatching request: GET /api/auth/captcha
[2025-08-02 03:05:50][INFO] 数据库连接成功: 127.0.0.1:3306/plb_kj
[2025-08-02 03:06:14][INFO] Request: POST /api/admin/login
[2025-08-02 03:06:14][INFO] Registering route: GET /api/health
[2025-08-02 03:06:14][INFO] Registering route: POST /api/admin/login
[2025-08-02 03:06:14][INFO] Registering route: POST /api/users/login
[2025-08-02 03:06:14][INFO] Registering route: GET /api/auth/captcha
[2025-08-02 03:06:14][INFO] Registering route: GET /api/auth/validate
[2025-08-02 03:06:14][INFO] Registering route: GET /api/users
[2025-08-02 03:06:14][INFO] Registering route: POST /api/users
[2025-08-02 03:06:14][INFO] Registering route: GET /api/users/profile
[2025-08-02 03:06:14][INFO] Registering route: GET /api/users/:id
[2025-08-02 03:06:14][INFO] Registering route: PUT /api/users/:id
[2025-08-02 03:06:14][INFO] Registering route: DELETE /api/users/:id
[2025-08-02 03:06:14][INFO] Registering route: GET /api/stats
[2025-08-02 03:06:14][INFO] Registering route: GET /api/stats/:type
[2025-08-02 03:06:14][INFO] Registering route: GET /api/settings/background
[2025-08-02 03:06:14][INFO] Registering route: POST /api/settings/background
[2025-08-02 03:06:14][INFO] Dispatching request: POST /api/admin/login
[2025-08-02 03:06:14][INFO] 数据库连接成功: 127.0.0.1:3306/plb_kj
[2025-08-02 03:06:14][INFO] Admin login attempt for username: admin
[2025-08-02 03:06:14][INFO] Admin user logged in successfully: admin
[2025-08-02 03:07:32][INFO] Request: GET /api/settings/background
[2025-08-02 03:07:32][INFO] Registering route: GET /api/health
[2025-08-02 03:07:32][INFO] Registering route: POST /api/admin/login
[2025-08-02 03:07:32][INFO] Registering route: POST /api/users/login
[2025-08-02 03:07:32][INFO] Registering route: GET /api/auth/captcha
[2025-08-02 03:07:32][INFO] Registering route: GET /api/auth/validate
[2025-08-02 03:07:32][INFO] Registering route: GET /api/users
[2025-08-02 03:07:32][INFO] Registering route: POST /api/users
[2025-08-02 03:07:32][INFO] Registering route: GET /api/users/profile
[2025-08-02 03:07:32][INFO] Registering route: GET /api/users/:id
[2025-08-02 03:07:32][INFO] Registering route: PUT /api/users/:id
[2025-08-02 03:07:32][INFO] Registering route: DELETE /api/users/:id
[2025-08-02 03:07:32][INFO] Registering route: GET /api/stats
[2025-08-02 03:07:32][INFO] Registering route: GET /api/stats/:type
[2025-08-02 03:07:32][INFO] Registering route: GET /api/settings/background
[2025-08-02 03:07:32][INFO] Registering route: POST /api/settings/background
[2025-08-02 03:07:32][INFO] Dispatching request: GET /api/settings/background
[2025-08-02 03:07:32][INFO] 数据库连接成功: 127.0.0.1:3306/plb_kj
[2025-08-02 03:07:32][INFO] Request: GET /api/auth/captcha
[2025-08-02 03:07:32][INFO] Registering route: GET /api/health
[2025-08-02 03:07:32][INFO] Registering route: POST /api/admin/login
[2025-08-02 03:07:32][INFO] Registering route: POST /api/users/login
[2025-08-02 03:07:32][INFO] Registering route: GET /api/auth/captcha
[2025-08-02 03:07:32][INFO] Registering route: GET /api/auth/validate
[2025-08-02 03:07:32][INFO] Registering route: GET /api/users
[2025-08-02 03:07:32][INFO] Registering route: POST /api/users
[2025-08-02 03:07:32][INFO] Registering route: GET /api/users/profile
[2025-08-02 03:07:32][INFO] Registering route: GET /api/users/:id
[2025-08-02 03:07:32][INFO] Registering route: PUT /api/users/:id
[2025-08-02 03:07:32][INFO] Registering route: DELETE /api/users/:id
[2025-08-02 03:07:32][INFO] Registering route: GET /api/stats
[2025-08-02 03:07:32][INFO] Registering route: GET /api/stats/:type
[2025-08-02 03:07:32][INFO] Registering route: GET /api/settings/background
[2025-08-02 03:07:32][INFO] Registering route: POST /api/settings/background
[2025-08-02 03:07:32][INFO] Dispatching request: GET /api/auth/captcha
[2025-08-02 03:07:32][INFO] 数据库连接成功: 127.0.0.1:3306/plb_kj
[2025-08-02 03:07:34][INFO] Request: GET /api/auth/validate
[2025-08-02 03:07:34][INFO] Registering route: GET /api/health
[2025-08-02 03:07:34][INFO] Registering route: POST /api/admin/login
[2025-08-02 03:07:34][INFO] Registering route: POST /api/users/login
[2025-08-02 03:07:34][INFO] Registering route: GET /api/auth/captcha
[2025-08-02 03:07:34][INFO] Registering route: GET /api/auth/validate
[2025-08-02 03:07:34][INFO] Registering route: GET /api/users
[2025-08-02 03:07:34][INFO] Registering route: POST /api/users
[2025-08-02 03:07:34][INFO] Registering route: GET /api/users/profile
[2025-08-02 03:07:34][INFO] Registering route: GET /api/users/:id
[2025-08-02 03:07:34][INFO] Registering route: PUT /api/users/:id
[2025-08-02 03:07:34][INFO] Registering route: DELETE /api/users/:id
[2025-08-02 03:07:34][INFO] Registering route: GET /api/stats
[2025-08-02 03:07:34][INFO] Registering route: GET /api/stats/:type
[2025-08-02 03:07:34][INFO] Registering route: GET /api/settings/background
[2025-08-02 03:07:34][INFO] Registering route: POST /api/settings/background
[2025-08-02 03:07:34][INFO] Dispatching request: GET /api/auth/validate
[2025-08-02 03:07:34][INFO] 数据库连接成功: 127.0.0.1:3306/plb_kj
[2025-08-02 03:07:34][ERROR] Token validation error: 无效的认证令牌
[2025-08-02 03:07:34][INFO] Request: GET /api/settings/background
[2025-08-02 03:07:34][INFO] Registering route: GET /api/health
[2025-08-02 03:07:34][INFO] Registering route: POST /api/admin/login
[2025-08-02 03:07:34][INFO] Registering route: POST /api/users/login
[2025-08-02 03:07:34][INFO] Registering route: GET /api/auth/captcha
[2025-08-02 03:07:34][INFO] Registering route: GET /api/auth/validate
[2025-08-02 03:07:34][INFO] Registering route: GET /api/users
[2025-08-02 03:07:34][INFO] Registering route: POST /api/users
[2025-08-02 03:07:34][INFO] Registering route: GET /api/users/profile
[2025-08-02 03:07:34][INFO] Registering route: GET /api/users/:id
[2025-08-02 03:07:34][INFO] Registering route: PUT /api/users/:id
[2025-08-02 03:07:34][INFO] Registering route: DELETE /api/users/:id
[2025-08-02 03:07:34][INFO] Registering route: GET /api/stats
[2025-08-02 03:07:34][INFO] Registering route: GET /api/stats/:type
[2025-08-02 03:07:34][INFO] Registering route: GET /api/settings/background
[2025-08-02 03:07:34][INFO] Registering route: POST /api/settings/background
[2025-08-02 03:07:34][INFO] Dispatching request: GET /api/settings/background
[2025-08-02 03:07:34][INFO] 数据库连接成功: 127.0.0.1:3306/plb_kj
[2025-08-02 03:07:34][INFO] Request: GET /api/auth/captcha
[2025-08-02 03:07:34][INFO] Registering route: GET /api/health
[2025-08-02 03:07:34][INFO] Registering route: POST /api/admin/login
[2025-08-02 03:07:34][INFO] Registering route: POST /api/users/login
[2025-08-02 03:07:34][INFO] Registering route: GET /api/auth/captcha
[2025-08-02 03:07:34][INFO] Registering route: GET /api/auth/validate
[2025-08-02 03:07:34][INFO] Registering route: GET /api/users
[2025-08-02 03:07:34][INFO] Registering route: POST /api/users
[2025-08-02 03:07:34][INFO] Registering route: GET /api/users/profile
[2025-08-02 03:07:34][INFO] Registering route: GET /api/users/:id
[2025-08-02 03:07:34][INFO] Registering route: PUT /api/users/:id
[2025-08-02 03:07:34][INFO] Registering route: DELETE /api/users/:id
[2025-08-02 03:07:34][INFO] Registering route: GET /api/stats
[2025-08-02 03:07:34][INFO] Registering route: GET /api/stats/:type
[2025-08-02 03:07:34][INFO] Registering route: GET /api/settings/background
[2025-08-02 03:07:34][INFO] Registering route: POST /api/settings/background
[2025-08-02 03:07:34][INFO] Dispatching request: GET /api/auth/captcha
[2025-08-02 03:07:34][INFO] 数据库连接成功: 127.0.0.1:3306/plb_kj
[2025-08-02 03:07:53][INFO] Request: GET /?ide_webview_request_time=1754075272764
[2025-08-02 03:07:53][INFO] Registering route: GET /api/health
[2025-08-02 03:07:53][INFO] Registering route: POST /api/admin/login
[2025-08-02 03:07:53][INFO] Registering route: POST /api/users/login
[2025-08-02 03:07:53][INFO] Registering route: GET /api/auth/captcha
[2025-08-02 03:07:53][INFO] Registering route: GET /api/auth/validate
[2025-08-02 03:07:53][INFO] Registering route: GET /api/users
[2025-08-02 03:07:53][INFO] Registering route: POST /api/users
[2025-08-02 03:07:53][INFO] Registering route: GET /api/users/profile
[2025-08-02 03:07:53][INFO] Registering route: GET /api/users/:id
[2025-08-02 03:07:53][INFO] Registering route: PUT /api/users/:id
[2025-08-02 03:07:53][INFO] Registering route: DELETE /api/users/:id
[2025-08-02 03:07:53][INFO] Registering route: GET /api/stats
[2025-08-02 03:07:53][INFO] Registering route: GET /api/stats/:type
[2025-08-02 03:07:53][INFO] Registering route: GET /api/settings/background
[2025-08-02 03:07:53][INFO] Registering route: POST /api/settings/background
[2025-08-02 03:07:53][INFO] Dispatching request: GET /
[2025-08-02 03:07:53][WARNING] Route not found: GET /
[2025-08-02 03:07:53][INFO] Request: GET /@vite/client
[2025-08-02 03:07:53][INFO] Registering route: GET /api/health
[2025-08-02 03:07:53][INFO] Registering route: POST /api/admin/login
[2025-08-02 03:07:53][INFO] Registering route: POST /api/users/login
[2025-08-02 03:07:53][INFO] Registering route: GET /api/auth/captcha
[2025-08-02 03:07:53][INFO] Registering route: GET /api/auth/validate
[2025-08-02 03:07:53][INFO] Registering route: GET /api/users
[2025-08-02 03:07:53][INFO] Registering route: POST /api/users
[2025-08-02 03:07:53][INFO] Registering route: GET /api/users/profile
[2025-08-02 03:07:53][INFO] Registering route: GET /api/users/:id
[2025-08-02 03:07:53][INFO] Registering route: PUT /api/users/:id
[2025-08-02 03:07:53][INFO] Registering route: DELETE /api/users/:id
[2025-08-02 03:07:53][INFO] Registering route: GET /api/stats
[2025-08-02 03:07:53][INFO] Registering route: GET /api/stats/:type
[2025-08-02 03:07:53][INFO] Registering route: GET /api/settings/background
[2025-08-02 03:07:53][INFO] Registering route: POST /api/settings/background
[2025-08-02 03:07:53][INFO] Dispatching request: GET /@vite/client
[2025-08-02 03:07:53][WARNING] Route not found: GET /@vite/client
[2025-08-02 03:08:30][INFO] Request: GET /api/settings/background
[2025-08-02 03:08:30][INFO] Registering route: GET /api/health
[2025-08-02 03:08:30][INFO] Registering route: POST /api/admin/login
[2025-08-02 03:08:30][INFO] Registering route: POST /api/users/login
[2025-08-02 03:08:30][INFO] Registering route: GET /api/auth/captcha
[2025-08-02 03:08:30][INFO] Registering route: GET /api/auth/validate
[2025-08-02 03:08:30][INFO] Registering route: GET /api/users
[2025-08-02 03:08:30][INFO] Registering route: POST /api/users
[2025-08-02 03:08:30][INFO] Registering route: GET /api/users/profile
[2025-08-02 03:08:30][INFO] Registering route: GET /api/users/:id
[2025-08-02 03:08:30][INFO] Registering route: PUT /api/users/:id
[2025-08-02 03:08:30][INFO] Registering route: DELETE /api/users/:id
[2025-08-02 03:08:30][INFO] Registering route: GET /api/stats
[2025-08-02 03:08:30][INFO] Registering route: GET /api/stats/:type
[2025-08-02 03:08:30][INFO] Registering route: GET /api/settings/background
[2025-08-02 03:08:30][INFO] Registering route: POST /api/settings/background
[2025-08-02 03:08:30][INFO] Dispatching request: GET /api/settings/background
[2025-08-02 03:08:30][INFO] 数据库连接成功: 127.0.0.1:3306/plb_kj
[2025-08-02 03:08:30][INFO] Request: GET /api/auth/captcha
[2025-08-02 03:08:30][INFO] Registering route: GET /api/health
[2025-08-02 03:08:30][INFO] Registering route: POST /api/admin/login
[2025-08-02 03:08:30][INFO] Registering route: POST /api/users/login
[2025-08-02 03:08:30][INFO] Registering route: GET /api/auth/captcha
[2025-08-02 03:08:30][INFO] Registering route: GET /api/auth/validate
[2025-08-02 03:08:30][INFO] Registering route: GET /api/users
[2025-08-02 03:08:30][INFO] Registering route: POST /api/users
[2025-08-02 03:08:30][INFO] Registering route: GET /api/users/profile
[2025-08-02 03:08:30][INFO] Registering route: GET /api/users/:id
[2025-08-02 03:08:30][INFO] Registering route: PUT /api/users/:id
[2025-08-02 03:08:30][INFO] Registering route: DELETE /api/users/:id
[2025-08-02 03:08:30][INFO] Registering route: GET /api/stats
[2025-08-02 03:08:30][INFO] Registering route: GET /api/stats/:type
[2025-08-02 03:08:30][INFO] Registering route: GET /api/settings/background
[2025-08-02 03:08:30][INFO] Registering route: POST /api/settings/background
[2025-08-02 03:08:30][INFO] Dispatching request: GET /api/auth/captcha
[2025-08-02 03:08:30][INFO] 数据库连接成功: 127.0.0.1:3306/plb_kj
[2025-08-02 03:08:31][INFO] Request: GET /api/auth/validate
[2025-08-02 03:08:31][INFO] Registering route: GET /api/health
[2025-08-02 03:08:31][INFO] Registering route: POST /api/admin/login
[2025-08-02 03:08:31][INFO] Registering route: POST /api/users/login
[2025-08-02 03:08:31][INFO] Registering route: GET /api/auth/captcha
[2025-08-02 03:08:31][INFO] Registering route: GET /api/auth/validate
[2025-08-02 03:08:31][INFO] Registering route: GET /api/users
[2025-08-02 03:08:31][INFO] Registering route: POST /api/users
[2025-08-02 03:08:31][INFO] Registering route: GET /api/users/profile
[2025-08-02 03:08:31][INFO] Registering route: GET /api/users/:id
[2025-08-02 03:08:31][INFO] Registering route: PUT /api/users/:id
[2025-08-02 03:08:31][INFO] Registering route: DELETE /api/users/:id
[2025-08-02 03:08:31][INFO] Registering route: GET /api/stats
[2025-08-02 03:08:31][INFO] Registering route: GET /api/stats/:type
[2025-08-02 03:08:31][INFO] Registering route: GET /api/settings/background
[2025-08-02 03:08:31][INFO] Registering route: POST /api/settings/background
[2025-08-02 03:08:31][INFO] Dispatching request: GET /api/auth/validate
[2025-08-02 03:08:31][INFO] 数据库连接成功: 127.0.0.1:3306/plb_kj
[2025-08-02 03:08:31][ERROR] Token validation error: 无效的认证令牌
[2025-08-02 03:08:32][INFO] Request: GET /api/settings/background
[2025-08-02 03:08:32][INFO] Registering route: GET /api/health
[2025-08-02 03:08:32][INFO] Registering route: POST /api/admin/login
[2025-08-02 03:08:32][INFO] Registering route: POST /api/users/login
[2025-08-02 03:08:32][INFO] Registering route: GET /api/auth/captcha
[2025-08-02 03:08:32][INFO] Registering route: GET /api/auth/validate
[2025-08-02 03:08:32][INFO] Registering route: GET /api/users
[2025-08-02 03:08:32][INFO] Registering route: POST /api/users
[2025-08-02 03:08:32][INFO] Registering route: GET /api/users/profile
[2025-08-02 03:08:32][INFO] Registering route: GET /api/users/:id
[2025-08-02 03:08:32][INFO] Registering route: PUT /api/users/:id
[2025-08-02 03:08:32][INFO] Registering route: DELETE /api/users/:id
[2025-08-02 03:08:32][INFO] Registering route: GET /api/stats
[2025-08-02 03:08:32][INFO] Registering route: GET /api/stats/:type
[2025-08-02 03:08:32][INFO] Registering route: GET /api/settings/background
[2025-08-02 03:08:32][INFO] Registering route: POST /api/settings/background
[2025-08-02 03:08:32][INFO] Dispatching request: GET /api/settings/background
[2025-08-02 03:08:32][INFO] 数据库连接成功: 127.0.0.1:3306/plb_kj
[2025-08-02 03:08:32][INFO] Request: GET /api/auth/captcha
[2025-08-02 03:08:32][INFO] Registering route: GET /api/health
[2025-08-02 03:08:32][INFO] Registering route: POST /api/admin/login
[2025-08-02 03:08:32][INFO] Registering route: POST /api/users/login
[2025-08-02 03:08:32][INFO] Registering route: GET /api/auth/captcha
[2025-08-02 03:08:32][INFO] Registering route: GET /api/auth/validate
[2025-08-02 03:08:32][INFO] Registering route: GET /api/users
[2025-08-02 03:08:32][INFO] Registering route: POST /api/users
[2025-08-02 03:08:32][INFO] Registering route: GET /api/users/profile
[2025-08-02 03:08:32][INFO] Registering route: GET /api/users/:id
[2025-08-02 03:08:32][INFO] Registering route: PUT /api/users/:id
[2025-08-02 03:08:32][INFO] Registering route: DELETE /api/users/:id
[2025-08-02 03:08:32][INFO] Registering route: GET /api/stats
[2025-08-02 03:08:32][INFO] Registering route: GET /api/stats/:type
[2025-08-02 03:08:32][INFO] Registering route: GET /api/settings/background
[2025-08-02 03:08:32][INFO] Registering route: POST /api/settings/background
[2025-08-02 03:08:32][INFO] Dispatching request: GET /api/auth/captcha
[2025-08-02 03:08:32][INFO] 数据库连接成功: 127.0.0.1:3306/plb_kj
[2025-08-02 03:08:55][INFO] Request: GET /api/settings/background
[2025-08-02 03:08:55][INFO] Registering route: GET /api/health
[2025-08-02 03:08:55][INFO] Registering route: POST /api/admin/login
[2025-08-02 03:08:55][INFO] Registering route: POST /api/users/login
[2025-08-02 03:08:55][INFO] Registering route: GET /api/auth/captcha
[2025-08-02 03:08:55][INFO] Registering route: GET /api/auth/validate
[2025-08-02 03:08:55][INFO] Registering route: GET /api/users
[2025-08-02 03:08:55][INFO] Registering route: POST /api/users
[2025-08-02 03:08:55][INFO] Registering route: GET /api/users/profile
[2025-08-02 03:08:55][INFO] Registering route: GET /api/users/:id
[2025-08-02 03:08:55][INFO] Registering route: PUT /api/users/:id
[2025-08-02 03:08:55][INFO] Registering route: DELETE /api/users/:id
[2025-08-02 03:08:55][INFO] Registering route: GET /api/stats
[2025-08-02 03:08:55][INFO] Registering route: GET /api/stats/:type
[2025-08-02 03:08:55][INFO] Registering route: GET /api/settings/background
[2025-08-02 03:08:55][INFO] Registering route: POST /api/settings/background
[2025-08-02 03:08:55][INFO] Dispatching request: GET /api/settings/background
[2025-08-02 03:08:55][INFO] 数据库连接成功: 127.0.0.1:3306/plb_kj
[2025-08-02 03:08:55][INFO] Request: GET /api/auth/captcha
[2025-08-02 03:08:55][INFO] Registering route: GET /api/health
[2025-08-02 03:08:55][INFO] Registering route: POST /api/admin/login
[2025-08-02 03:08:55][INFO] Registering route: POST /api/users/login
[2025-08-02 03:08:55][INFO] Registering route: GET /api/auth/captcha
[2025-08-02 03:08:55][INFO] Registering route: GET /api/auth/validate
[2025-08-02 03:08:55][INFO] Registering route: GET /api/users
[2025-08-02 03:08:55][INFO] Registering route: POST /api/users
[2025-08-02 03:08:55][INFO] Registering route: GET /api/users/profile
[2025-08-02 03:08:55][INFO] Registering route: GET /api/users/:id
[2025-08-02 03:08:55][INFO] Registering route: PUT /api/users/:id
[2025-08-02 03:08:55][INFO] Registering route: DELETE /api/users/:id
[2025-08-02 03:08:55][INFO] Registering route: GET /api/stats
[2025-08-02 03:08:55][INFO] Registering route: GET /api/stats/:type
[2025-08-02 03:08:55][INFO] Registering route: GET /api/settings/background
[2025-08-02 03:08:55][INFO] Registering route: POST /api/settings/background
[2025-08-02 03:08:55][INFO] Dispatching request: GET /api/auth/captcha
[2025-08-02 03:08:55][INFO] 数据库连接成功: 127.0.0.1:3306/plb_kj
[2025-08-02 03:08:57][INFO] Request: GET /api/auth/validate
[2025-08-02 03:08:57][INFO] Registering route: GET /api/health
[2025-08-02 03:08:57][INFO] Registering route: POST /api/admin/login
[2025-08-02 03:08:57][INFO] Registering route: POST /api/users/login
[2025-08-02 03:08:57][INFO] Registering route: GET /api/auth/captcha
[2025-08-02 03:08:57][INFO] Registering route: GET /api/auth/validate
[2025-08-02 03:08:57][INFO] Registering route: GET /api/users
[2025-08-02 03:08:57][INFO] Registering route: POST /api/users
[2025-08-02 03:08:57][INFO] Registering route: GET /api/users/profile
[2025-08-02 03:08:57][INFO] Registering route: GET /api/users/:id
[2025-08-02 03:08:57][INFO] Registering route: PUT /api/users/:id
[2025-08-02 03:08:57][INFO] Registering route: DELETE /api/users/:id
[2025-08-02 03:08:57][INFO] Registering route: GET /api/stats
[2025-08-02 03:08:57][INFO] Registering route: GET /api/stats/:type
[2025-08-02 03:08:57][INFO] Registering route: GET /api/settings/background
[2025-08-02 03:08:57][INFO] Registering route: POST /api/settings/background
[2025-08-02 03:08:57][INFO] Dispatching request: GET /api/auth/validate
[2025-08-02 03:08:57][INFO] 数据库连接成功: 127.0.0.1:3306/plb_kj
[2025-08-02 03:08:57][ERROR] Token validation error: 无效的认证令牌
[2025-08-02 03:08:57][INFO] Request: GET /api/settings/background
[2025-08-02 03:08:57][INFO] Registering route: GET /api/health
[2025-08-02 03:08:57][INFO] Registering route: POST /api/admin/login
[2025-08-02 03:08:57][INFO] Registering route: POST /api/users/login
[2025-08-02 03:08:57][INFO] Registering route: GET /api/auth/captcha
[2025-08-02 03:08:57][INFO] Registering route: GET /api/auth/validate
[2025-08-02 03:08:57][INFO] Registering route: GET /api/users
[2025-08-02 03:08:57][INFO] Registering route: POST /api/users
[2025-08-02 03:08:57][INFO] Registering route: GET /api/users/profile
[2025-08-02 03:08:57][INFO] Registering route: GET /api/users/:id
[2025-08-02 03:08:57][INFO] Registering route: PUT /api/users/:id
[2025-08-02 03:08:57][INFO] Registering route: DELETE /api/users/:id
[2025-08-02 03:08:57][INFO] Registering route: GET /api/stats
[2025-08-02 03:08:57][INFO] Registering route: GET /api/stats/:type
[2025-08-02 03:08:57][INFO] Registering route: GET /api/settings/background
[2025-08-02 03:08:57][INFO] Registering route: POST /api/settings/background
[2025-08-02 03:08:57][INFO] Dispatching request: GET /api/settings/background
[2025-08-02 03:08:57][INFO] 数据库连接成功: 127.0.0.1:3306/plb_kj
[2025-08-02 03:08:57][INFO] Request: GET /api/auth/captcha
[2025-08-02 03:08:57][INFO] Registering route: GET /api/health
[2025-08-02 03:08:57][INFO] Registering route: POST /api/admin/login
[2025-08-02 03:08:57][INFO] Registering route: POST /api/users/login
[2025-08-02 03:08:57][INFO] Registering route: GET /api/auth/captcha
[2025-08-02 03:08:57][INFO] Registering route: GET /api/auth/validate
[2025-08-02 03:08:57][INFO] Registering route: GET /api/users
[2025-08-02 03:08:57][INFO] Registering route: POST /api/users
[2025-08-02 03:08:57][INFO] Registering route: GET /api/users/profile
[2025-08-02 03:08:57][INFO] Registering route: GET /api/users/:id
[2025-08-02 03:08:57][INFO] Registering route: PUT /api/users/:id
[2025-08-02 03:08:57][INFO] Registering route: DELETE /api/users/:id
[2025-08-02 03:08:57][INFO] Registering route: GET /api/stats
[2025-08-02 03:08:57][INFO] Registering route: GET /api/stats/:type
[2025-08-02 03:08:57][INFO] Registering route: GET /api/settings/background
[2025-08-02 03:08:57][INFO] Registering route: POST /api/settings/background
[2025-08-02 03:08:57][INFO] Dispatching request: GET /api/auth/captcha
[2025-08-02 03:08:57][INFO] 数据库连接成功: 127.0.0.1:3306/plb_kj
[2025-08-02 03:09:50][INFO] Request: GET /api/settings/background
[2025-08-02 03:09:50][INFO] Registering route: GET /api/health
[2025-08-02 03:09:50][INFO] Registering route: POST /api/admin/login
[2025-08-02 03:09:50][INFO] Registering route: POST /api/users/login
[2025-08-02 03:09:50][INFO] Registering route: GET /api/auth/captcha
[2025-08-02 03:09:50][INFO] Registering route: GET /api/auth/validate
[2025-08-02 03:09:50][INFO] Registering route: GET /api/users
[2025-08-02 03:09:50][INFO] Registering route: POST /api/users
[2025-08-02 03:09:50][INFO] Registering route: GET /api/users/profile
[2025-08-02 03:09:50][INFO] Registering route: GET /api/users/:id
[2025-08-02 03:09:50][INFO] Registering route: PUT /api/users/:id
[2025-08-02 03:09:50][INFO] Registering route: DELETE /api/users/:id
[2025-08-02 03:09:50][INFO] Registering route: GET /api/stats
[2025-08-02 03:09:50][INFO] Registering route: GET /api/stats/:type
[2025-08-02 03:09:50][INFO] Registering route: GET /api/settings/background
[2025-08-02 03:09:50][INFO] Registering route: POST /api/settings/background
[2025-08-02 03:09:50][INFO] Dispatching request: GET /api/settings/background
[2025-08-02 03:09:50][INFO] 数据库连接成功: 127.0.0.1:3306/plb_kj
[2025-08-02 03:09:50][INFO] Request: GET /api/auth/captcha
[2025-08-02 03:09:50][INFO] Registering route: GET /api/health
[2025-08-02 03:09:50][INFO] Registering route: POST /api/admin/login
[2025-08-02 03:09:50][INFO] Registering route: POST /api/users/login
[2025-08-02 03:09:50][INFO] Registering route: GET /api/auth/captcha
[2025-08-02 03:09:50][INFO] Registering route: GET /api/auth/validate
[2025-08-02 03:09:50][INFO] Registering route: GET /api/users
[2025-08-02 03:09:50][INFO] Registering route: POST /api/users
[2025-08-02 03:09:50][INFO] Registering route: GET /api/users/profile
[2025-08-02 03:09:50][INFO] Registering route: GET /api/users/:id
[2025-08-02 03:09:50][INFO] Registering route: PUT /api/users/:id
[2025-08-02 03:09:50][INFO] Registering route: DELETE /api/users/:id
[2025-08-02 03:09:50][INFO] Registering route: GET /api/stats
[2025-08-02 03:09:50][INFO] Registering route: GET /api/stats/:type
[2025-08-02 03:09:50][INFO] Registering route: GET /api/settings/background
[2025-08-02 03:09:50][INFO] Registering route: POST /api/settings/background
[2025-08-02 03:09:50][INFO] Dispatching request: GET /api/auth/captcha
[2025-08-02 03:09:50][INFO] 数据库连接成功: 127.0.0.1:3306/plb_kj
[2025-08-02 03:09:51][INFO] Request: GET /api/auth/validate
[2025-08-02 03:09:51][INFO] Registering route: GET /api/health
[2025-08-02 03:09:51][INFO] Registering route: POST /api/admin/login
[2025-08-02 03:09:51][INFO] Registering route: POST /api/users/login
[2025-08-02 03:09:51][INFO] Registering route: GET /api/auth/captcha
[2025-08-02 03:09:51][INFO] Registering route: GET /api/auth/validate
[2025-08-02 03:09:51][INFO] Registering route: GET /api/users
[2025-08-02 03:09:51][INFO] Registering route: POST /api/users
[2025-08-02 03:09:51][INFO] Registering route: GET /api/users/profile
[2025-08-02 03:09:51][INFO] Registering route: GET /api/users/:id
[2025-08-02 03:09:51][INFO] Registering route: PUT /api/users/:id
[2025-08-02 03:09:51][INFO] Registering route: DELETE /api/users/:id
[2025-08-02 03:09:51][INFO] Registering route: GET /api/stats
[2025-08-02 03:09:51][INFO] Registering route: GET /api/stats/:type
[2025-08-02 03:09:51][INFO] Registering route: GET /api/settings/background
[2025-08-02 03:09:51][INFO] Registering route: POST /api/settings/background
[2025-08-02 03:09:51][INFO] Dispatching request: GET /api/auth/validate
[2025-08-02 03:09:51][INFO] 数据库连接成功: 127.0.0.1:3306/plb_kj
[2025-08-02 03:09:51][ERROR] Token validation error: 无效的认证令牌
[2025-08-02 03:09:52][INFO] Request: GET /api/settings/background
[2025-08-02 03:09:52][INFO] Registering route: GET /api/health
[2025-08-02 03:09:52][INFO] Registering route: POST /api/admin/login
[2025-08-02 03:09:52][INFO] Registering route: POST /api/users/login
[2025-08-02 03:09:52][INFO] Registering route: GET /api/auth/captcha
[2025-08-02 03:09:52][INFO] Registering route: GET /api/auth/validate
[2025-08-02 03:09:52][INFO] Registering route: GET /api/users
[2025-08-02 03:09:52][INFO] Registering route: POST /api/users
[2025-08-02 03:09:52][INFO] Registering route: GET /api/users/profile
[2025-08-02 03:09:52][INFO] Registering route: GET /api/users/:id
[2025-08-02 03:09:52][INFO] Registering route: PUT /api/users/:id
[2025-08-02 03:09:52][INFO] Registering route: DELETE /api/users/:id
[2025-08-02 03:09:52][INFO] Registering route: GET /api/stats
[2025-08-02 03:09:52][INFO] Registering route: GET /api/stats/:type
[2025-08-02 03:09:52][INFO] Registering route: GET /api/settings/background
[2025-08-02 03:09:52][INFO] Registering route: POST /api/settings/background
[2025-08-02 03:09:52][INFO] Dispatching request: GET /api/settings/background
[2025-08-02 03:09:52][INFO] 数据库连接成功: 127.0.0.1:3306/plb_kj
[2025-08-02 03:09:52][INFO] Request: GET /api/auth/captcha
[2025-08-02 03:09:52][INFO] Registering route: GET /api/health
[2025-08-02 03:09:52][INFO] Registering route: POST /api/admin/login
[2025-08-02 03:09:52][INFO] Registering route: POST /api/users/login
[2025-08-02 03:09:52][INFO] Registering route: GET /api/auth/captcha
[2025-08-02 03:09:52][INFO] Registering route: GET /api/auth/validate
[2025-08-02 03:09:52][INFO] Registering route: GET /api/users
[2025-08-02 03:09:52][INFO] Registering route: POST /api/users
[2025-08-02 03:09:52][INFO] Registering route: GET /api/users/profile
[2025-08-02 03:09:52][INFO] Registering route: GET /api/users/:id
[2025-08-02 03:09:52][INFO] Registering route: PUT /api/users/:id
[2025-08-02 03:09:52][INFO] Registering route: DELETE /api/users/:id
[2025-08-02 03:09:52][INFO] Registering route: GET /api/stats
[2025-08-02 03:09:52][INFO] Registering route: GET /api/stats/:type
[2025-08-02 03:09:52][INFO] Registering route: GET /api/settings/background
[2025-08-02 03:09:52][INFO] Registering route: POST /api/settings/background
[2025-08-02 03:09:52][INFO] Dispatching request: GET /api/auth/captcha
[2025-08-02 03:09:52][INFO] 数据库连接成功: 127.0.0.1:3306/plb_kj
[2025-08-02 03:12:15][INFO] Request: GET /api/settings/background
[2025-08-02 03:12:15][INFO] Registering route: GET /api/health
[2025-08-02 03:12:15][INFO] Registering route: POST /api/admin/login
[2025-08-02 03:12:15][INFO] Registering route: POST /api/users/login
[2025-08-02 03:12:15][INFO] Registering route: GET /api/auth/captcha
[2025-08-02 03:12:15][INFO] Registering route: GET /api/auth/validate
[2025-08-02 03:12:15][INFO] Registering route: GET /api/users
[2025-08-02 03:12:15][INFO] Registering route: POST /api/users
[2025-08-02 03:12:15][INFO] Registering route: GET /api/users/profile
[2025-08-02 03:12:15][INFO] Registering route: GET /api/users/:id
[2025-08-02 03:12:15][INFO] Registering route: PUT /api/users/:id
[2025-08-02 03:12:15][INFO] Registering route: DELETE /api/users/:id
[2025-08-02 03:12:15][INFO] Registering route: GET /api/stats
[2025-08-02 03:12:15][INFO] Registering route: GET /api/stats/:type
[2025-08-02 03:12:15][INFO] Registering route: GET /api/settings/background
[2025-08-02 03:12:15][INFO] Registering route: POST /api/settings/background
[2025-08-02 03:12:15][INFO] Dispatching request: GET /api/settings/background
[2025-08-02 03:12:15][INFO] 数据库连接成功: 127.0.0.1:3306/plb_kj
[2025-08-02 03:12:15][INFO] Request: GET /api/auth/captcha
[2025-08-02 03:12:15][INFO] Registering route: GET /api/health
[2025-08-02 03:12:15][INFO] Registering route: POST /api/admin/login
[2025-08-02 03:12:15][INFO] Registering route: POST /api/users/login
[2025-08-02 03:12:15][INFO] Registering route: GET /api/auth/captcha
[2025-08-02 03:12:15][INFO] Registering route: GET /api/auth/validate
[2025-08-02 03:12:15][INFO] Registering route: GET /api/users
[2025-08-02 03:12:15][INFO] Registering route: POST /api/users
[2025-08-02 03:12:15][INFO] Registering route: GET /api/users/profile
[2025-08-02 03:12:15][INFO] Registering route: GET /api/users/:id
[2025-08-02 03:12:15][INFO] Registering route: PUT /api/users/:id
[2025-08-02 03:12:15][INFO] Registering route: DELETE /api/users/:id
[2025-08-02 03:12:15][INFO] Registering route: GET /api/stats
[2025-08-02 03:12:15][INFO] Registering route: GET /api/stats/:type
[2025-08-02 03:12:15][INFO] Registering route: GET /api/settings/background
[2025-08-02 03:12:15][INFO] Registering route: POST /api/settings/background
[2025-08-02 03:12:15][INFO] Dispatching request: GET /api/auth/captcha
[2025-08-02 03:12:15][INFO] 数据库连接成功: 127.0.0.1:3306/plb_kj
[2025-08-02 03:12:16][INFO] Request: GET /api/auth/validate
[2025-08-02 03:12:16][INFO] Registering route: GET /api/health
[2025-08-02 03:12:16][INFO] Registering route: POST /api/admin/login
[2025-08-02 03:12:16][INFO] Registering route: POST /api/users/login
[2025-08-02 03:12:16][INFO] Registering route: GET /api/auth/captcha
[2025-08-02 03:12:16][INFO] Registering route: GET /api/auth/validate
[2025-08-02 03:12:16][INFO] Registering route: GET /api/users
[2025-08-02 03:12:16][INFO] Registering route: POST /api/users
[2025-08-02 03:12:16][INFO] Registering route: GET /api/users/profile
[2025-08-02 03:12:16][INFO] Registering route: GET /api/users/:id
[2025-08-02 03:12:16][INFO] Registering route: PUT /api/users/:id
[2025-08-02 03:12:16][INFO] Registering route: DELETE /api/users/:id
[2025-08-02 03:12:16][INFO] Registering route: GET /api/stats
[2025-08-02 03:12:16][INFO] Registering route: GET /api/stats/:type
[2025-08-02 03:12:16][INFO] Registering route: GET /api/settings/background
[2025-08-02 03:12:16][INFO] Registering route: POST /api/settings/background
[2025-08-02 03:12:16][INFO] Dispatching request: GET /api/auth/validate
[2025-08-02 03:12:16][INFO] 数据库连接成功: 127.0.0.1:3306/plb_kj
[2025-08-02 03:12:16][ERROR] Token validation error: 无效的认证令牌
[2025-08-02 03:12:16][INFO] Request: GET /api/settings/background
[2025-08-02 03:12:16][INFO] Registering route: GET /api/health
[2025-08-02 03:12:16][INFO] Registering route: POST /api/admin/login
[2025-08-02 03:12:16][INFO] Registering route: POST /api/users/login
[2025-08-02 03:12:16][INFO] Registering route: GET /api/auth/captcha
[2025-08-02 03:12:16][INFO] Registering route: GET /api/auth/validate
[2025-08-02 03:12:16][INFO] Registering route: GET /api/users
[2025-08-02 03:12:16][INFO] Registering route: POST /api/users
[2025-08-02 03:12:16][INFO] Registering route: GET /api/users/profile
[2025-08-02 03:12:16][INFO] Registering route: GET /api/users/:id
[2025-08-02 03:12:16][INFO] Registering route: PUT /api/users/:id
[2025-08-02 03:12:16][INFO] Registering route: DELETE /api/users/:id
[2025-08-02 03:12:16][INFO] Registering route: GET /api/stats
[2025-08-02 03:12:16][INFO] Registering route: GET /api/stats/:type
[2025-08-02 03:12:16][INFO] Registering route: GET /api/settings/background
[2025-08-02 03:12:16][INFO] Registering route: POST /api/settings/background
[2025-08-02 03:12:16][INFO] Dispatching request: GET /api/settings/background
[2025-08-02 03:12:16][INFO] 数据库连接成功: 127.0.0.1:3306/plb_kj
[2025-08-02 03:12:16][INFO] Request: GET /api/auth/captcha
[2025-08-02 03:12:16][INFO] Registering route: GET /api/health
[2025-08-02 03:12:16][INFO] Registering route: POST /api/admin/login
[2025-08-02 03:12:16][INFO] Registering route: POST /api/users/login
[2025-08-02 03:12:16][INFO] Registering route: GET /api/auth/captcha
[2025-08-02 03:12:16][INFO] Registering route: GET /api/auth/validate
[2025-08-02 03:12:16][INFO] Registering route: GET /api/users
[2025-08-02 03:12:16][INFO] Registering route: POST /api/users
[2025-08-02 03:12:16][INFO] Registering route: GET /api/users/profile
[2025-08-02 03:12:16][INFO] Registering route: GET /api/users/:id
[2025-08-02 03:12:16][INFO] Registering route: PUT /api/users/:id
[2025-08-02 03:12:16][INFO] Registering route: DELETE /api/users/:id
[2025-08-02 03:12:16][INFO] Registering route: GET /api/stats
[2025-08-02 03:12:16][INFO] Registering route: GET /api/stats/:type
[2025-08-02 03:12:16][INFO] Registering route: GET /api/settings/background
[2025-08-02 03:12:16][INFO] Registering route: POST /api/settings/background
[2025-08-02 03:12:16][INFO] Dispatching request: GET /api/auth/captcha
[2025-08-02 03:12:16][INFO] 数据库连接成功: 127.0.0.1:3306/plb_kj
[2025-08-02 03:12:22][INFO] Request: GET /?ide_webview_request_time=1754075542107
[2025-08-02 03:12:22][INFO] Registering route: GET /api/health
[2025-08-02 03:12:22][INFO] Registering route: POST /api/admin/login
[2025-08-02 03:12:22][INFO] Registering route: POST /api/users/login
[2025-08-02 03:12:22][INFO] Registering route: GET /api/auth/captcha
[2025-08-02 03:12:22][INFO] Registering route: GET /api/auth/validate
[2025-08-02 03:12:22][INFO] Registering route: GET /api/users
[2025-08-02 03:12:22][INFO] Registering route: POST /api/users
[2025-08-02 03:12:22][INFO] Registering route: GET /api/users/profile
[2025-08-02 03:12:22][INFO] Registering route: GET /api/users/:id
[2025-08-02 03:12:22][INFO] Registering route: PUT /api/users/:id
[2025-08-02 03:12:22][INFO] Registering route: DELETE /api/users/:id
[2025-08-02 03:12:22][INFO] Registering route: GET /api/stats
[2025-08-02 03:12:22][INFO] Registering route: GET /api/stats/:type
[2025-08-02 03:12:22][INFO] Registering route: GET /api/settings/background
[2025-08-02 03:12:22][INFO] Registering route: POST /api/settings/background
[2025-08-02 03:12:22][INFO] Dispatching request: GET /
[2025-08-02 03:12:22][WARNING] Route not found: GET /
[2025-08-02 03:12:22][INFO] Request: GET /@vite/client
[2025-08-02 03:12:22][INFO] Registering route: GET /api/health
[2025-08-02 03:12:22][INFO] Registering route: POST /api/admin/login
[2025-08-02 03:12:22][INFO] Registering route: POST /api/users/login
[2025-08-02 03:12:22][INFO] Registering route: GET /api/auth/captcha
[2025-08-02 03:12:22][INFO] Registering route: GET /api/auth/validate
[2025-08-02 03:12:22][INFO] Registering route: GET /api/users
[2025-08-02 03:12:22][INFO] Registering route: POST /api/users
[2025-08-02 03:12:22][INFO] Registering route: GET /api/users/profile
[2025-08-02 03:12:22][INFO] Registering route: GET /api/users/:id
[2025-08-02 03:12:22][INFO] Registering route: PUT /api/users/:id
[2025-08-02 03:12:22][INFO] Registering route: DELETE /api/users/:id
[2025-08-02 03:12:22][INFO] Registering route: GET /api/stats
[2025-08-02 03:12:22][INFO] Registering route: GET /api/stats/:type
[2025-08-02 03:12:22][INFO] Registering route: GET /api/settings/background
[2025-08-02 03:12:22][INFO] Registering route: POST /api/settings/background
[2025-08-02 03:12:22][INFO] Dispatching request: GET /@vite/client
[2025-08-02 03:12:22][WARNING] Route not found: GET /@vite/client
[2025-08-02 03:14:19][INFO] Request: GET /api/settings/background
[2025-08-02 03:14:19][INFO] Registering route: GET /api/health
[2025-08-02 03:14:19][INFO] Registering route: POST /api/admin/login
[2025-08-02 03:14:19][INFO] Registering route: POST /api/users/login
[2025-08-02 03:14:19][INFO] Registering route: GET /api/auth/captcha
[2025-08-02 03:14:19][INFO] Registering route: GET /api/auth/validate
[2025-08-02 03:14:19][INFO] Registering route: GET /api/users
[2025-08-02 03:14:19][INFO] Registering route: POST /api/users
[2025-08-02 03:14:19][INFO] Registering route: GET /api/users/profile
[2025-08-02 03:14:19][INFO] Registering route: GET /api/users/:id
[2025-08-02 03:14:19][INFO] Registering route: PUT /api/users/:id
[2025-08-02 03:14:19][INFO] Registering route: DELETE /api/users/:id
[2025-08-02 03:14:19][INFO] Registering route: GET /api/stats
[2025-08-02 03:14:19][INFO] Registering route: GET /api/stats/:type
[2025-08-02 03:14:19][INFO] Registering route: GET /api/settings/background
[2025-08-02 03:14:19][INFO] Registering route: POST /api/settings/background
[2025-08-02 03:14:19][INFO] Dispatching request: GET /api/settings/background
[2025-08-02 03:14:19][INFO] 数据库连接成功: 127.0.0.1:3306/plb_kj
[2025-08-02 03:14:19][INFO] Request: GET /api/auth/captcha
[2025-08-02 03:14:19][INFO] Registering route: GET /api/health
[2025-08-02 03:14:19][INFO] Registering route: POST /api/admin/login
[2025-08-02 03:14:19][INFO] Registering route: POST /api/users/login
[2025-08-02 03:14:19][INFO] Registering route: GET /api/auth/captcha
[2025-08-02 03:14:19][INFO] Registering route: GET /api/auth/validate
[2025-08-02 03:14:19][INFO] Registering route: GET /api/users
[2025-08-02 03:14:19][INFO] Registering route: POST /api/users
[2025-08-02 03:14:19][INFO] Registering route: GET /api/users/profile
[2025-08-02 03:14:19][INFO] Registering route: GET /api/users/:id
[2025-08-02 03:14:19][INFO] Registering route: PUT /api/users/:id
[2025-08-02 03:14:19][INFO] Registering route: DELETE /api/users/:id
[2025-08-02 03:14:19][INFO] Registering route: GET /api/stats
[2025-08-02 03:14:19][INFO] Registering route: GET /api/stats/:type
[2025-08-02 03:14:19][INFO] Registering route: GET /api/settings/background
[2025-08-02 03:14:19][INFO] Registering route: POST /api/settings/background
[2025-08-02 03:14:19][INFO] Dispatching request: GET /api/auth/captcha
[2025-08-02 03:14:19][INFO] 数据库连接成功: 127.0.0.1:3306/plb_kj
[2025-08-02 03:14:20][INFO] Request: GET /api/auth/validate
[2025-08-02 03:14:20][INFO] Registering route: GET /api/health
[2025-08-02 03:14:20][INFO] Registering route: POST /api/admin/login
[2025-08-02 03:14:20][INFO] Registering route: POST /api/users/login
[2025-08-02 03:14:20][INFO] Registering route: GET /api/auth/captcha
[2025-08-02 03:14:20][INFO] Registering route: GET /api/auth/validate
[2025-08-02 03:14:20][INFO] Registering route: GET /api/users
[2025-08-02 03:14:20][INFO] Registering route: POST /api/users
[2025-08-02 03:14:20][INFO] Registering route: GET /api/users/profile
[2025-08-02 03:14:20][INFO] Registering route: GET /api/users/:id
[2025-08-02 03:14:20][INFO] Registering route: PUT /api/users/:id
[2025-08-02 03:14:20][INFO] Registering route: DELETE /api/users/:id
[2025-08-02 03:14:20][INFO] Registering route: GET /api/stats
[2025-08-02 03:14:20][INFO] Registering route: GET /api/stats/:type
[2025-08-02 03:14:20][INFO] Registering route: GET /api/settings/background
[2025-08-02 03:14:20][INFO] Registering route: POST /api/settings/background
[2025-08-02 03:14:20][INFO] Dispatching request: GET /api/auth/validate
[2025-08-02 03:14:20][INFO] 数据库连接成功: 127.0.0.1:3306/plb_kj
[2025-08-02 03:14:20][ERROR] Token validation error: 无效的认证令牌
[2025-08-02 03:14:21][INFO] Request: GET /api/settings/background
[2025-08-02 03:14:21][INFO] Registering route: GET /api/health
[2025-08-02 03:14:21][INFO] Registering route: POST /api/admin/login
[2025-08-02 03:14:21][INFO] Registering route: POST /api/users/login
[2025-08-02 03:14:21][INFO] Registering route: GET /api/auth/captcha
[2025-08-02 03:14:21][INFO] Registering route: GET /api/auth/validate
[2025-08-02 03:14:21][INFO] Registering route: GET /api/users
[2025-08-02 03:14:21][INFO] Registering route: POST /api/users
[2025-08-02 03:14:21][INFO] Registering route: GET /api/users/profile
[2025-08-02 03:14:21][INFO] Registering route: GET /api/users/:id
[2025-08-02 03:14:21][INFO] Registering route: PUT /api/users/:id
[2025-08-02 03:14:21][INFO] Registering route: DELETE /api/users/:id
[2025-08-02 03:14:21][INFO] Registering route: GET /api/stats
[2025-08-02 03:14:21][INFO] Registering route: GET /api/stats/:type
[2025-08-02 03:14:21][INFO] Registering route: GET /api/settings/background
[2025-08-02 03:14:21][INFO] Registering route: POST /api/settings/background
[2025-08-02 03:14:21][INFO] Dispatching request: GET /api/settings/background
[2025-08-02 03:14:21][INFO] 数据库连接成功: 127.0.0.1:3306/plb_kj
[2025-08-02 03:14:21][INFO] Request: GET /api/auth/captcha
[2025-08-02 03:14:21][INFO] Registering route: GET /api/health
[2025-08-02 03:14:21][INFO] Registering route: POST /api/admin/login
[2025-08-02 03:14:21][INFO] Registering route: POST /api/users/login
[2025-08-02 03:14:21][INFO] Registering route: GET /api/auth/captcha
[2025-08-02 03:14:21][INFO] Registering route: GET /api/auth/validate
[2025-08-02 03:14:21][INFO] Registering route: GET /api/users
[2025-08-02 03:14:21][INFO] Registering route: POST /api/users
[2025-08-02 03:14:21][INFO] Registering route: GET /api/users/profile
[2025-08-02 03:14:21][INFO] Registering route: GET /api/users/:id
[2025-08-02 03:14:21][INFO] Registering route: PUT /api/users/:id
[2025-08-02 03:14:21][INFO] Registering route: DELETE /api/users/:id
[2025-08-02 03:14:21][INFO] Registering route: GET /api/stats
[2025-08-02 03:14:21][INFO] Registering route: GET /api/stats/:type
[2025-08-02 03:14:21][INFO] Registering route: GET /api/settings/background
[2025-08-02 03:14:21][INFO] Registering route: POST /api/settings/background
[2025-08-02 03:14:21][INFO] Dispatching request: GET /api/auth/captcha
[2025-08-02 03:14:21][INFO] 数据库连接成功: 127.0.0.1:3306/plb_kj
[2025-08-02 03:14:25][INFO] Request: GET /?ide_webview_request_time=1754075665389
[2025-08-02 03:14:25][INFO] Registering route: GET /api/health
[2025-08-02 03:14:25][INFO] Registering route: POST /api/admin/login
[2025-08-02 03:14:25][INFO] Registering route: POST /api/users/login
[2025-08-02 03:14:25][INFO] Registering route: GET /api/auth/captcha
[2025-08-02 03:14:25][INFO] Registering route: GET /api/auth/validate
[2025-08-02 03:14:25][INFO] Registering route: GET /api/users
[2025-08-02 03:14:25][INFO] Registering route: POST /api/users
[2025-08-02 03:14:25][INFO] Registering route: GET /api/users/profile
[2025-08-02 03:14:25][INFO] Registering route: GET /api/users/:id
[2025-08-02 03:14:25][INFO] Registering route: PUT /api/users/:id
[2025-08-02 03:14:25][INFO] Registering route: DELETE /api/users/:id
[2025-08-02 03:14:25][INFO] Registering route: GET /api/stats
[2025-08-02 03:14:25][INFO] Registering route: GET /api/stats/:type
[2025-08-02 03:14:25][INFO] Registering route: GET /api/settings/background
[2025-08-02 03:14:25][INFO] Registering route: POST /api/settings/background
[2025-08-02 03:14:25][INFO] Dispatching request: GET /
[2025-08-02 03:14:25][WARNING] Route not found: GET /
[2025-08-02 03:14:25][INFO] Request: GET /@vite/client
[2025-08-02 03:14:25][INFO] Registering route: GET /api/health
[2025-08-02 03:14:25][INFO] Registering route: POST /api/admin/login
[2025-08-02 03:14:25][INFO] Registering route: POST /api/users/login
[2025-08-02 03:14:25][INFO] Registering route: GET /api/auth/captcha
[2025-08-02 03:14:25][INFO] Registering route: GET /api/auth/validate
[2025-08-02 03:14:25][INFO] Registering route: GET /api/users
[2025-08-02 03:14:25][INFO] Registering route: POST /api/users
[2025-08-02 03:14:25][INFO] Registering route: GET /api/users/profile
[2025-08-02 03:14:25][INFO] Registering route: GET /api/users/:id
[2025-08-02 03:14:25][INFO] Registering route: PUT /api/users/:id
[2025-08-02 03:14:25][INFO] Registering route: DELETE /api/users/:id
[2025-08-02 03:14:25][INFO] Registering route: GET /api/stats
[2025-08-02 03:14:25][INFO] Registering route: GET /api/stats/:type
[2025-08-02 03:14:25][INFO] Registering route: GET /api/settings/background
[2025-08-02 03:14:25][INFO] Registering route: POST /api/settings/background
[2025-08-02 03:14:25][INFO] Dispatching request: GET /@vite/client
[2025-08-02 03:14:25][WARNING] Route not found: GET /@vite/client
[2025-08-02 03:15:26][INFO] Request: POST /api/admin/login
[2025-08-02 03:15:26][INFO] Registering route: GET /api/health
[2025-08-02 03:15:26][INFO] Registering route: POST /api/admin/login
[2025-08-02 03:15:26][INFO] Registering route: POST /api/users/login
[2025-08-02 03:15:26][INFO] Registering route: GET /api/auth/captcha
[2025-08-02 03:15:26][INFO] Registering route: GET /api/auth/validate
[2025-08-02 03:15:26][INFO] Registering route: GET /api/users
[2025-08-02 03:15:26][INFO] Registering route: POST /api/users
[2025-08-02 03:15:26][INFO] Registering route: GET /api/users/profile
[2025-08-02 03:15:26][INFO] Registering route: GET /api/users/:id
[2025-08-02 03:15:26][INFO] Registering route: PUT /api/users/:id
[2025-08-02 03:15:26][INFO] Registering route: DELETE /api/users/:id
[2025-08-02 03:15:26][INFO] Registering route: GET /api/stats
[2025-08-02 03:15:26][INFO] Registering route: GET /api/stats/:type
[2025-08-02 03:15:26][INFO] Registering route: GET /api/settings/background
[2025-08-02 03:15:26][INFO] Registering route: POST /api/settings/background
[2025-08-02 03:15:26][INFO] Dispatching request: POST /api/admin/login
[2025-08-02 03:15:26][INFO] 数据库连接成功: 127.0.0.1:3306/plb_kj
[2025-08-02 03:15:26][INFO] Admin login attempt for username: admin
[2025-08-02 03:15:26][INFO] Admin user logged in successfully: admin
[2025-08-02 03:15:50][INFO] Request: GET /api/auth/captcha
[2025-08-02 03:15:50][INFO] Registering route: GET /api/health
[2025-08-02 03:15:50][INFO] Registering route: POST /api/admin/login
[2025-08-02 03:15:50][INFO] Registering route: POST /api/users/login
[2025-08-02 03:15:50][INFO] Registering route: GET /api/auth/captcha
[2025-08-02 03:15:50][INFO] Registering route: GET /api/auth/validate
[2025-08-02 03:15:50][INFO] Registering route: GET /api/users
[2025-08-02 03:15:50][INFO] Registering route: POST /api/users
[2025-08-02 03:15:50][INFO] Registering route: GET /api/users/profile
[2025-08-02 03:15:50][INFO] Registering route: GET /api/users/:id
[2025-08-02 03:15:50][INFO] Registering route: PUT /api/users/:id
[2025-08-02 03:15:50][INFO] Registering route: DELETE /api/users/:id
[2025-08-02 03:15:50][INFO] Registering route: GET /api/stats
[2025-08-02 03:15:50][INFO] Registering route: GET /api/stats/:type
[2025-08-02 03:15:50][INFO] Registering route: GET /api/settings/background
[2025-08-02 03:15:50][INFO] Registering route: POST /api/settings/background
[2025-08-02 03:15:50][INFO] Dispatching request: GET /api/auth/captcha
[2025-08-02 03:15:50][INFO] 数据库连接成功: 127.0.0.1:3306/plb_kj
[2025-08-02 03:15:52][INFO] Request: GET /api/auth/validate
[2025-08-02 03:15:52][INFO] Registering route: GET /api/health
[2025-08-02 03:15:52][INFO] Registering route: POST /api/admin/login
[2025-08-02 03:15:52][INFO] Registering route: POST /api/users/login
[2025-08-02 03:15:52][INFO] Registering route: GET /api/auth/captcha
[2025-08-02 03:15:52][INFO] Registering route: GET /api/auth/validate
[2025-08-02 03:15:52][INFO] Registering route: GET /api/users
[2025-08-02 03:15:52][INFO] Registering route: POST /api/users
[2025-08-02 03:15:52][INFO] Registering route: GET /api/users/profile
[2025-08-02 03:15:52][INFO] Registering route: GET /api/users/:id
[2025-08-02 03:15:52][INFO] Registering route: PUT /api/users/:id
[2025-08-02 03:15:52][INFO] Registering route: DELETE /api/users/:id
[2025-08-02 03:15:52][INFO] Registering route: GET /api/stats
[2025-08-02 03:15:52][INFO] Registering route: GET /api/stats/:type
[2025-08-02 03:15:52][INFO] Registering route: GET /api/settings/background
[2025-08-02 03:15:52][INFO] Registering route: POST /api/settings/background
[2025-08-02 03:15:52][INFO] Dispatching request: GET /api/auth/validate
[2025-08-02 03:15:52][INFO] 数据库连接成功: 127.0.0.1:3306/plb_kj
[2025-08-02 03:15:52][ERROR] Token validation error: 无效的认证令牌
[2025-08-02 03:15:52][INFO] Request: GET /api/auth/captcha
[2025-08-02 03:15:52][INFO] Registering route: GET /api/health
[2025-08-02 03:15:52][INFO] Registering route: POST /api/admin/login
[2025-08-02 03:15:52][INFO] Registering route: POST /api/users/login
[2025-08-02 03:15:52][INFO] Registering route: GET /api/auth/captcha
[2025-08-02 03:15:52][INFO] Registering route: GET /api/auth/validate
[2025-08-02 03:15:52][INFO] Registering route: GET /api/users
[2025-08-02 03:15:52][INFO] Registering route: POST /api/users
[2025-08-02 03:15:52][INFO] Registering route: GET /api/users/profile
[2025-08-02 03:15:52][INFO] Registering route: GET /api/users/:id
[2025-08-02 03:15:52][INFO] Registering route: PUT /api/users/:id
[2025-08-02 03:15:52][INFO] Registering route: DELETE /api/users/:id
[2025-08-02 03:15:52][INFO] Registering route: GET /api/stats
[2025-08-02 03:15:52][INFO] Registering route: GET /api/stats/:type
[2025-08-02 03:15:52][INFO] Registering route: GET /api/settings/background
[2025-08-02 03:15:52][INFO] Registering route: POST /api/settings/background
[2025-08-02 03:15:52][INFO] Dispatching request: GET /api/auth/captcha
[2025-08-02 03:15:52][INFO] 数据库连接成功: 127.0.0.1:3306/plb_kj
[2025-08-02 03:16:07][INFO] Request: POST /api/admin/login
[2025-08-02 03:16:07][INFO] Registering route: GET /api/health
[2025-08-02 03:16:07][INFO] Registering route: POST /api/admin/login
[2025-08-02 03:16:07][INFO] Registering route: POST /api/users/login
[2025-08-02 03:16:07][INFO] Registering route: GET /api/auth/captcha
[2025-08-02 03:16:07][INFO] Registering route: GET /api/auth/validate
[2025-08-02 03:16:07][INFO] Registering route: GET /api/users
[2025-08-02 03:16:07][INFO] Registering route: POST /api/users
[2025-08-02 03:16:07][INFO] Registering route: GET /api/users/profile
[2025-08-02 03:16:07][INFO] Registering route: GET /api/users/:id
[2025-08-02 03:16:07][INFO] Registering route: PUT /api/users/:id
[2025-08-02 03:16:07][INFO] Registering route: DELETE /api/users/:id
[2025-08-02 03:16:07][INFO] Registering route: GET /api/stats
[2025-08-02 03:16:07][INFO] Registering route: GET /api/stats/:type
[2025-08-02 03:16:07][INFO] Registering route: GET /api/settings/background
[2025-08-02 03:16:07][INFO] Registering route: POST /api/settings/background
[2025-08-02 03:16:07][INFO] Dispatching request: POST /api/admin/login
[2025-08-02 03:16:07][INFO] 数据库连接成功: 127.0.0.1:3306/plb_kj
[2025-08-02 03:16:07][INFO] Admin login attempt for username: test01
[2025-08-02 03:16:07][WARNING] Invalid captcha attempt for user: test01
[2025-08-02 03:16:07][ERROR] Admin login error: 验证码错误
[2025-08-02 03:16:07][INFO] Request: GET /api/auth/captcha
[2025-08-02 03:16:07][INFO] Registering route: GET /api/health
[2025-08-02 03:16:07][INFO] Registering route: POST /api/admin/login
[2025-08-02 03:16:07][INFO] Registering route: POST /api/users/login
[2025-08-02 03:16:07][INFO] Registering route: GET /api/auth/captcha
[2025-08-02 03:16:07][INFO] Registering route: GET /api/auth/validate
[2025-08-02 03:16:07][INFO] Registering route: GET /api/users
[2025-08-02 03:16:07][INFO] Registering route: POST /api/users
[2025-08-02 03:16:07][INFO] Registering route: GET /api/users/profile
[2025-08-02 03:16:07][INFO] Registering route: GET /api/users/:id
[2025-08-02 03:16:07][INFO] Registering route: PUT /api/users/:id
[2025-08-02 03:16:07][INFO] Registering route: DELETE /api/users/:id
[2025-08-02 03:16:07][INFO] Registering route: GET /api/stats
[2025-08-02 03:16:07][INFO] Registering route: GET /api/stats/:type
[2025-08-02 03:16:07][INFO] Registering route: GET /api/settings/background
[2025-08-02 03:16:07][INFO] Registering route: POST /api/settings/background
[2025-08-02 03:16:07][INFO] Dispatching request: GET /api/auth/captcha
[2025-08-02 03:16:07][INFO] 数据库连接成功: 127.0.0.1:3306/plb_kj
[2025-08-02 03:16:21][INFO] Request: POST /api/admin/login
[2025-08-02 03:16:21][INFO] Registering route: GET /api/health
[2025-08-02 03:16:21][INFO] Registering route: POST /api/admin/login
[2025-08-02 03:16:21][INFO] Registering route: POST /api/users/login
[2025-08-02 03:16:21][INFO] Registering route: GET /api/auth/captcha
[2025-08-02 03:16:21][INFO] Registering route: GET /api/auth/validate
[2025-08-02 03:16:21][INFO] Registering route: GET /api/users
[2025-08-02 03:16:21][INFO] Registering route: POST /api/users
[2025-08-02 03:16:21][INFO] Registering route: GET /api/users/profile
[2025-08-02 03:16:21][INFO] Registering route: GET /api/users/:id
[2025-08-02 03:16:21][INFO] Registering route: PUT /api/users/:id
[2025-08-02 03:16:21][INFO] Registering route: DELETE /api/users/:id
[2025-08-02 03:16:21][INFO] Registering route: GET /api/stats
[2025-08-02 03:16:21][INFO] Registering route: GET /api/stats/:type
[2025-08-02 03:16:21][INFO] Registering route: GET /api/settings/background
[2025-08-02 03:16:21][INFO] Registering route: POST /api/settings/background
[2025-08-02 03:16:21][INFO] Dispatching request: POST /api/admin/login
[2025-08-02 03:16:21][INFO] 数据库连接成功: 127.0.0.1:3306/plb_kj
[2025-08-02 03:16:21][INFO] Admin login attempt for username: test01
[2025-08-02 03:16:21][WARNING] Failed login attempt for admin user: test01
[2025-08-02 03:16:21][ERROR] Admin login error: 用户名或密码错误
[2025-08-02 03:16:21][INFO] Request: GET /api/auth/captcha
[2025-08-02 03:16:21][INFO] Registering route: GET /api/health
[2025-08-02 03:16:21][INFO] Registering route: POST /api/admin/login
[2025-08-02 03:16:21][INFO] Registering route: POST /api/users/login
[2025-08-02 03:16:21][INFO] Registering route: GET /api/auth/captcha
[2025-08-02 03:16:21][INFO] Registering route: GET /api/auth/validate
[2025-08-02 03:16:21][INFO] Registering route: GET /api/users
[2025-08-02 03:16:21][INFO] Registering route: POST /api/users
[2025-08-02 03:16:21][INFO] Registering route: GET /api/users/profile
[2025-08-02 03:16:21][INFO] Registering route: GET /api/users/:id
[2025-08-02 03:16:21][INFO] Registering route: PUT /api/users/:id
[2025-08-02 03:16:21][INFO] Registering route: DELETE /api/users/:id
[2025-08-02 03:16:21][INFO] Registering route: GET /api/stats
[2025-08-02 03:16:21][INFO] Registering route: GET /api/stats/:type
[2025-08-02 03:16:21][INFO] Registering route: GET /api/settings/background
[2025-08-02 03:16:21][INFO] Registering route: POST /api/settings/background
[2025-08-02 03:16:21][INFO] Dispatching request: GET /api/auth/captcha
[2025-08-02 03:16:21][INFO] 数据库连接成功: 127.0.0.1:3306/plb_kj
[2025-08-02 10:08:09][INFO] Request: GET /
[2025-08-02 10:08:09][INFO] Registering route: GET /api/health
[2025-08-02 10:08:09][INFO] Registering route: POST /api/admin/login
[2025-08-02 10:08:09][INFO] Registering route: POST /api/users/login
[2025-08-02 10:08:09][INFO] Registering route: GET /api/auth/captcha
[2025-08-02 10:08:09][INFO] Registering route: GET /api/auth/validate
[2025-08-02 10:08:09][INFO] Registering route: GET /api/users
[2025-08-02 10:08:09][INFO] Registering route: POST /api/users
[2025-08-02 10:08:09][INFO] Registering route: GET /api/users/profile
[2025-08-02 10:08:09][INFO] Registering route: GET /api/users/:id
[2025-08-02 10:08:09][INFO] Registering route: PUT /api/users/:id
[2025-08-02 10:08:09][INFO] Registering route: DELETE /api/users/:id
[2025-08-02 10:08:09][INFO] Registering route: GET /api/stats
[2025-08-02 10:08:09][INFO] Registering route: GET /api/stats/:type
[2025-08-02 10:08:09][INFO] Registering route: GET /api/settings/background
[2025-08-02 10:08:09][INFO] Registering route: POST /api/settings/background
[2025-08-02 10:08:09][INFO] Dispatching request: GET /
[2025-08-02 10:08:09][WARNING] Route not found: GET /
[2025-08-02 10:09:21][INFO] Request: GET /
[2025-08-02 10:09:21][INFO] Registering route: GET /api/health
[2025-08-02 10:09:21][INFO] Registering route: POST /api/admin/login
[2025-08-02 10:09:21][INFO] Registering route: POST /api/users/login
[2025-08-02 10:09:21][INFO] Registering route: GET /api/auth/captcha
[2025-08-02 10:09:21][INFO] Registering route: GET /api/auth/validate
[2025-08-02 10:09:21][INFO] Registering route: GET /api/users
[2025-08-02 10:09:21][INFO] Registering route: POST /api/users
[2025-08-02 10:09:21][INFO] Registering route: GET /api/users/profile
[2025-08-02 10:09:21][INFO] Registering route: GET /api/users/:id
[2025-08-02 10:09:21][INFO] Registering route: PUT /api/users/:id
[2025-08-02 10:09:21][INFO] Registering route: DELETE /api/users/:id
[2025-08-02 10:09:21][INFO] Registering route: GET /api/stats
[2025-08-02 10:09:21][INFO] Registering route: GET /api/stats/:type
[2025-08-02 10:09:21][INFO] Registering route: GET /api/settings/background
[2025-08-02 10:09:21][INFO] Registering route: POST /api/settings/background
[2025-08-02 10:09:21][INFO] Dispatching request: GET /
[2025-08-02 10:09:21][WARNING] Route not found: GET /
[2025-08-02 10:09:27][INFO] Request: GET /
[2025-08-02 10:09:27][INFO] Registering route: GET /api/health
[2025-08-02 10:09:27][INFO] Registering route: POST /api/admin/login
[2025-08-02 10:09:27][INFO] Registering route: POST /api/users/login
[2025-08-02 10:09:27][INFO] Registering route: GET /api/auth/captcha
[2025-08-02 10:09:27][INFO] Registering route: GET /api/auth/validate
[2025-08-02 10:09:27][INFO] Registering route: GET /api/users
[2025-08-02 10:09:27][INFO] Registering route: POST /api/users
[2025-08-02 10:09:27][INFO] Registering route: GET /api/users/profile
[2025-08-02 10:09:27][INFO] Registering route: GET /api/users/:id
[2025-08-02 10:09:27][INFO] Registering route: PUT /api/users/:id
[2025-08-02 10:09:27][INFO] Registering route: DELETE /api/users/:id
[2025-08-02 10:09:27][INFO] Registering route: GET /api/stats
[2025-08-02 10:09:27][INFO] Registering route: GET /api/stats/:type
[2025-08-02 10:09:27][INFO] Registering route: GET /api/settings/background
[2025-08-02 10:09:27][INFO] Registering route: POST /api/settings/background
[2025-08-02 10:09:27][INFO] Dispatching request: GET /
[2025-08-02 10:09:27][WARNING] Route not found: GET /
[2025-08-02 10:09:34][INFO] Request: GET /
[2025-08-02 10:09:34][INFO] Registering route: GET /api/health
[2025-08-02 10:09:34][INFO] Registering route: POST /api/admin/login
[2025-08-02 10:09:34][INFO] Registering route: POST /api/users/login
[2025-08-02 10:09:34][INFO] Registering route: GET /api/auth/captcha
[2025-08-02 10:09:34][INFO] Registering route: GET /api/auth/validate
[2025-08-02 10:09:34][INFO] Registering route: GET /api/users
[2025-08-02 10:09:34][INFO] Registering route: POST /api/users
[2025-08-02 10:09:34][INFO] Registering route: GET /api/users/profile
[2025-08-02 10:09:34][INFO] Registering route: GET /api/users/:id
[2025-08-02 10:09:34][INFO] Registering route: PUT /api/users/:id
[2025-08-02 10:09:34][INFO] Registering route: DELETE /api/users/:id
[2025-08-02 10:09:34][INFO] Registering route: GET /api/stats
[2025-08-02 10:09:34][INFO] Registering route: GET /api/stats/:type
[2025-08-02 10:09:34][INFO] Registering route: GET /api/settings/background
[2025-08-02 10:09:34][INFO] Registering route: POST /api/settings/background
[2025-08-02 10:09:34][INFO] Dispatching request: GET /
[2025-08-02 10:09:34][WARNING] Route not found: GET /
[2025-08-02 10:09:47][INFO] Request: GET /
[2025-08-02 10:09:47][INFO] Registering route: GET /api/health
[2025-08-02 10:09:47][INFO] Registering route: POST /api/admin/login
[2025-08-02 10:09:47][INFO] Registering route: POST /api/users/login
[2025-08-02 10:09:47][INFO] Registering route: GET /api/auth/captcha
[2025-08-02 10:09:47][INFO] Registering route: GET /api/auth/validate
[2025-08-02 10:09:47][INFO] Registering route: GET /api/users
[2025-08-02 10:09:47][INFO] Registering route: POST /api/users
[2025-08-02 10:09:47][INFO] Registering route: GET /api/users/profile
[2025-08-02 10:09:47][INFO] Registering route: GET /api/users/:id
[2025-08-02 10:09:47][INFO] Registering route: PUT /api/users/:id
[2025-08-02 10:09:47][INFO] Registering route: DELETE /api/users/:id
[2025-08-02 10:09:47][INFO] Registering route: GET /api/stats
[2025-08-02 10:09:47][INFO] Registering route: GET /api/stats/:type
[2025-08-02 10:09:47][INFO] Registering route: GET /api/settings/background
[2025-08-02 10:09:47][INFO] Registering route: POST /api/settings/background
[2025-08-02 10:09:47][INFO] Dispatching request: GET /
[2025-08-02 10:09:47][WARNING] Route not found: GET /
[2025-08-02 10:12:10][INFO] Request: GET /
[2025-08-02 10:12:10][INFO] Registering route: GET /api/health
[2025-08-02 10:12:10][INFO] Registering route: POST /api/admin/login
[2025-08-02 10:12:10][INFO] Registering route: POST /api/users/login
[2025-08-02 10:12:10][INFO] Registering route: GET /api/auth/captcha
[2025-08-02 10:12:10][INFO] Registering route: GET /api/auth/validate
[2025-08-02 10:12:10][INFO] Registering route: GET /api/users
[2025-08-02 10:12:10][INFO] Registering route: POST /api/users
[2025-08-02 10:12:10][INFO] Registering route: GET /api/users/profile
[2025-08-02 10:12:10][INFO] Registering route: GET /api/users/:id
[2025-08-02 10:12:10][INFO] Registering route: PUT /api/users/:id
[2025-08-02 10:12:10][INFO] Registering route: DELETE /api/users/:id
[2025-08-02 10:12:10][INFO] Registering route: GET /api/stats
[2025-08-02 10:12:10][INFO] Registering route: GET /api/stats/:type
[2025-08-02 10:12:10][INFO] Registering route: GET /api/settings/background
[2025-08-02 10:12:10][INFO] Registering route: POST /api/settings/background
[2025-08-02 10:12:10][INFO] Dispatching request: GET /
[2025-08-02 10:12:10][WARNING] Route not found: GET /
[2025-08-02 10:12:27][INFO] Request: GET /
[2025-08-02 10:12:27][INFO] Registering route: GET /api/health
[2025-08-02 10:12:27][INFO] Registering route: POST /api/admin/login
[2025-08-02 10:12:27][INFO] Registering route: POST /api/users/login
[2025-08-02 10:12:27][INFO] Registering route: GET /api/auth/captcha
[2025-08-02 10:12:27][INFO] Registering route: GET /api/auth/validate
[2025-08-02 10:12:27][INFO] Registering route: GET /api/users
[2025-08-02 10:12:27][INFO] Registering route: POST /api/users
[2025-08-02 10:12:27][INFO] Registering route: GET /api/users/profile
[2025-08-02 10:12:27][INFO] Registering route: GET /api/users/:id
[2025-08-02 10:12:27][INFO] Registering route: PUT /api/users/:id
[2025-08-02 10:12:27][INFO] Registering route: DELETE /api/users/:id
[2025-08-02 10:12:27][INFO] Registering route: GET /api/stats
[2025-08-02 10:12:27][INFO] Registering route: GET /api/stats/:type
[2025-08-02 10:12:27][INFO] Registering route: GET /api/settings/background
[2025-08-02 10:12:27][INFO] Registering route: POST /api/settings/background
[2025-08-02 10:12:27][INFO] Dispatching request: GET /
[2025-08-02 10:12:27][WARNING] Route not found: GET /
[2025-08-02 10:12:34][INFO] Request: GET /api/auth/captcha
[2025-08-02 10:12:34][INFO] Registering route: GET /api/health
[2025-08-02 10:12:34][INFO] Registering route: POST /api/admin/login
[2025-08-02 10:12:34][INFO] Registering route: POST /api/users/login
[2025-08-02 10:12:34][INFO] Registering route: GET /api/auth/captcha
[2025-08-02 10:12:34][INFO] Registering route: GET /api/auth/validate
[2025-08-02 10:12:34][INFO] Registering route: GET /api/users
[2025-08-02 10:12:34][INFO] Registering route: POST /api/users
[2025-08-02 10:12:34][INFO] Registering route: GET /api/users/profile
[2025-08-02 10:12:34][INFO] Registering route: GET /api/users/:id
[2025-08-02 10:12:34][INFO] Registering route: PUT /api/users/:id
[2025-08-02 10:12:34][INFO] Registering route: DELETE /api/users/:id
[2025-08-02 10:12:34][INFO] Registering route: GET /api/stats
[2025-08-02 10:12:34][INFO] Registering route: GET /api/stats/:type
[2025-08-02 10:12:34][INFO] Registering route: GET /api/settings/background
[2025-08-02 10:12:34][INFO] Registering route: POST /api/settings/background
[2025-08-02 10:12:34][INFO] Dispatching request: GET /api/auth/captcha
[2025-08-02 10:12:34][INFO] 数据库连接成功: 127.0.0.1:3306/plb_kj
[2025-08-02 10:13:30][INFO] Request: POST /api/admin/login
[2025-08-02 10:13:30][INFO] Registering route: GET /api/health
[2025-08-02 10:13:30][INFO] Registering route: POST /api/admin/login
[2025-08-02 10:13:30][INFO] Registering route: POST /api/users/login
[2025-08-02 10:13:30][INFO] Registering route: GET /api/auth/captcha
[2025-08-02 10:13:30][INFO] Registering route: GET /api/auth/validate
[2025-08-02 10:13:30][INFO] Registering route: GET /api/users
[2025-08-02 10:13:30][INFO] Registering route: POST /api/users
[2025-08-02 10:13:30][INFO] Registering route: GET /api/users/profile
[2025-08-02 10:13:30][INFO] Registering route: GET /api/users/:id
[2025-08-02 10:13:30][INFO] Registering route: PUT /api/users/:id
[2025-08-02 10:13:30][INFO] Registering route: DELETE /api/users/:id
[2025-08-02 10:13:30][INFO] Registering route: GET /api/stats
[2025-08-02 10:13:30][INFO] Registering route: GET /api/stats/:type
[2025-08-02 10:13:30][INFO] Registering route: GET /api/settings/background
[2025-08-02 10:13:30][INFO] Registering route: POST /api/settings/background
[2025-08-02 10:13:30][INFO] Dispatching request: POST /api/admin/login
[2025-08-02 10:13:30][INFO] 数据库连接成功: 127.0.0.1:3306/plb_kj
[2025-08-02 10:13:30][INFO] Admin login attempt for username: admin
[2025-08-02 10:13:30][INFO] Admin user logged in successfully: admin
[2025-08-02 11:29:02][INFO] Request: GET /api/auth/captcha
[2025-08-02 11:29:02][INFO] Registering route: GET /api/health
[2025-08-02 11:29:02][INFO] Registering route: POST /api/admin/login
[2025-08-02 11:29:02][INFO] Registering route: POST /api/users/login
[2025-08-02 11:29:02][INFO] Registering route: GET /api/auth/captcha
[2025-08-02 11:29:02][INFO] Registering route: GET /api/auth/validate
[2025-08-02 11:29:02][INFO] Registering route: GET /api/users
[2025-08-02 11:29:02][INFO] Registering route: POST /api/users
[2025-08-02 11:29:02][INFO] Registering route: GET /api/users/profile
[2025-08-02 11:29:02][INFO] Registering route: GET /api/users/:id
[2025-08-02 11:29:02][INFO] Registering route: PUT /api/users/:id
[2025-08-02 11:29:02][INFO] Registering route: DELETE /api/users/:id
[2025-08-02 11:29:02][INFO] Registering route: GET /api/stats
[2025-08-02 11:29:02][INFO] Registering route: GET /api/stats/:type
[2025-08-02 11:29:02][INFO] Registering route: GET /api/settings/background
[2025-08-02 11:29:02][INFO] Registering route: POST /api/settings/background
[2025-08-02 11:29:02][INFO] Registering route: GET /api/admin/settings/background
[2025-08-02 11:29:02][INFO] Dispatching request: GET /api/auth/captcha
[2025-08-02 11:29:02][INFO] 数据库连接成功: 127.0.0.1:3306/plb_kj
[2025-08-02 11:29:04][INFO] Request: GET /api/auth/captcha
[2025-08-02 11:29:04][INFO] Registering route: GET /api/health
[2025-08-02 11:29:04][INFO] Registering route: POST /api/admin/login
[2025-08-02 11:29:04][INFO] Registering route: POST /api/users/login
[2025-08-02 11:29:04][INFO] Registering route: GET /api/auth/captcha
[2025-08-02 11:29:04][INFO] Registering route: GET /api/auth/validate
[2025-08-02 11:29:04][INFO] Registering route: GET /api/users
[2025-08-02 11:29:04][INFO] Registering route: POST /api/users
[2025-08-02 11:29:04][INFO] Registering route: GET /api/users/profile
[2025-08-02 11:29:04][INFO] Registering route: GET /api/users/:id
[2025-08-02 11:29:04][INFO] Registering route: PUT /api/users/:id
[2025-08-02 11:29:04][INFO] Registering route: DELETE /api/users/:id
[2025-08-02 11:29:04][INFO] Registering route: GET /api/stats
[2025-08-02 11:29:04][INFO] Registering route: GET /api/stats/:type
[2025-08-02 11:29:04][INFO] Registering route: GET /api/settings/background
[2025-08-02 11:29:04][INFO] Registering route: POST /api/settings/background
[2025-08-02 11:29:04][INFO] Registering route: GET /api/admin/settings/background
[2025-08-02 11:29:04][INFO] Dispatching request: GET /api/auth/captcha
[2025-08-02 11:29:04][INFO] 数据库连接成功: 127.0.0.1:3306/plb_kj
[2025-08-02 11:29:15][INFO] Request: POST /api/admin/login
[2025-08-02 11:29:15][INFO] Registering route: GET /api/health
[2025-08-02 11:29:15][INFO] Registering route: POST /api/admin/login
[2025-08-02 11:29:15][INFO] Registering route: POST /api/users/login
[2025-08-02 11:29:15][INFO] Registering route: GET /api/auth/captcha
[2025-08-02 11:29:15][INFO] Registering route: GET /api/auth/validate
[2025-08-02 11:29:15][INFO] Registering route: GET /api/users
[2025-08-02 11:29:15][INFO] Registering route: POST /api/users
[2025-08-02 11:29:15][INFO] Registering route: GET /api/users/profile
[2025-08-02 11:29:15][INFO] Registering route: GET /api/users/:id
[2025-08-02 11:29:15][INFO] Registering route: PUT /api/users/:id
[2025-08-02 11:29:15][INFO] Registering route: DELETE /api/users/:id
[2025-08-02 11:29:15][INFO] Registering route: GET /api/stats
[2025-08-02 11:29:15][INFO] Registering route: GET /api/stats/:type
[2025-08-02 11:29:15][INFO] Registering route: GET /api/settings/background
[2025-08-02 11:29:15][INFO] Registering route: POST /api/settings/background
[2025-08-02 11:29:15][INFO] Registering route: GET /api/admin/settings/background
[2025-08-02 11:29:15][INFO] Dispatching request: POST /api/admin/login
[2025-08-02 11:29:15][INFO] 数据库连接成功: 127.0.0.1:3306/plb_kj
[2025-08-02 11:29:15][INFO] Admin login attempt for username: admin
[2025-08-02 11:29:15][INFO] Admin user logged in successfully: admin
[2025-08-02 12:19:58][INFO] Request: GET /admin
[2025-08-02 12:19:58][INFO] Registering route: GET /api/health
[2025-08-02 12:19:58][INFO] Registering route: POST /api/admin/login
[2025-08-02 12:19:58][INFO] Registering route: POST /api/users/login
[2025-08-02 12:19:58][INFO] Registering route: GET /api/auth/captcha
[2025-08-02 12:19:58][INFO] Registering route: GET /api/auth/validate
[2025-08-02 12:19:58][INFO] Registering route: GET /api/users
[2025-08-02 12:19:58][INFO] Registering route: POST /api/users
[2025-08-02 12:19:58][INFO] Registering route: GET /api/users/profile
[2025-08-02 12:19:58][INFO] Registering route: GET /api/users/:id
[2025-08-02 12:19:58][INFO] Registering route: PUT /api/users/:id
[2025-08-02 12:19:58][INFO] Registering route: DELETE /api/users/:id
[2025-08-02 12:19:58][INFO] Registering route: GET /api/stats
[2025-08-02 12:19:58][INFO] Registering route: GET /api/stats/:type
[2025-08-02 12:19:58][INFO] Registering route: GET /api/settings/background
[2025-08-02 12:19:58][INFO] Registering route: POST /api/settings/background
[2025-08-02 12:19:58][INFO] Registering route: GET /api/admin/settings/background
[2025-08-02 12:19:58][INFO] Dispatching request: GET /admin
[2025-08-02 12:19:58][WARNING] Route not found: GET /admin
[2025-08-02 18:24:09][INFO] Request: GET /api/settings/background
[2025-08-02 18:24:09][INFO] Registering route: GET /api/health
[2025-08-02 18:24:09][INFO] Registering route: POST /api/admin/login
[2025-08-02 18:24:09][INFO] Registering route: POST /api/users/login
[2025-08-02 18:24:09][INFO] Registering route: GET /api/auth/captcha
[2025-08-02 18:24:09][INFO] Registering route: GET /api/auth/validate
[2025-08-02 18:24:09][INFO] Registering route: GET /api/users
[2025-08-02 18:24:09][INFO] Registering route: POST /api/users
[2025-08-02 18:24:09][INFO] Registering route: GET /api/users/profile
[2025-08-02 18:24:09][INFO] Registering route: GET /api/users/:id
[2025-08-02 18:24:09][INFO] Registering route: PUT /api/users/:id
[2025-08-02 18:24:09][INFO] Registering route: DELETE /api/users/:id
[2025-08-02 18:24:09][INFO] Registering route: GET /api/stats
[2025-08-02 18:24:09][INFO] Registering route: GET /api/stats/:type
[2025-08-02 18:24:09][INFO] Registering route: GET /api/settings/background
[2025-08-02 18:24:09][INFO] Registering route: POST /api/settings/background
[2025-08-02 18:24:09][INFO] Registering route: GET /api/admin/settings/background
[2025-08-02 18:24:09][INFO] Dispatching request: GET /api/settings/background
[2025-08-02 18:24:09][INFO] 数据库连接成功: 127.0.0.1:3306/plb_kj
[2025-08-02 18:24:09][INFO] Request: GET /api/auth/captcha
[2025-08-02 18:24:09][INFO] Registering route: GET /api/health
[2025-08-02 18:24:09][INFO] Registering route: POST /api/admin/login
[2025-08-02 18:24:09][INFO] Registering route: POST /api/users/login
[2025-08-02 18:24:09][INFO] Registering route: GET /api/auth/captcha
[2025-08-02 18:24:09][INFO] Registering route: GET /api/auth/validate
[2025-08-02 18:24:09][INFO] Registering route: GET /api/users
[2025-08-02 18:24:09][INFO] Registering route: POST /api/users
[2025-08-02 18:24:09][INFO] Registering route: GET /api/users/profile
[2025-08-02 18:24:09][INFO] Registering route: GET /api/users/:id
[2025-08-02 18:24:09][INFO] Registering route: PUT /api/users/:id
[2025-08-02 18:24:09][INFO] Registering route: DELETE /api/users/:id
[2025-08-02 18:24:09][INFO] Registering route: GET /api/stats
[2025-08-02 18:24:09][INFO] Registering route: GET /api/stats/:type
[2025-08-02 18:24:09][INFO] Registering route: GET /api/settings/background
[2025-08-02 18:24:09][INFO] Registering route: POST /api/settings/background
[2025-08-02 18:24:09][INFO] Registering route: GET /api/admin/settings/background
[2025-08-02 18:24:09][INFO] Dispatching request: GET /api/auth/captcha
[2025-08-02 18:24:09][INFO] 数据库连接成功: 127.0.0.1:3306/plb_kj
[2025-08-02 18:24:22][INFO] Request: POST /api/admin/login
[2025-08-02 18:24:22][INFO] Registering route: GET /api/health
[2025-08-02 18:24:22][INFO] Registering route: POST /api/admin/login
[2025-08-02 18:24:22][INFO] Registering route: POST /api/users/login
[2025-08-02 18:24:22][INFO] Registering route: GET /api/auth/captcha
[2025-08-02 18:24:22][INFO] Registering route: GET /api/auth/validate
[2025-08-02 18:24:22][INFO] Registering route: GET /api/users
[2025-08-02 18:24:22][INFO] Registering route: POST /api/users
[2025-08-02 18:24:22][INFO] Registering route: GET /api/users/profile
[2025-08-02 18:24:22][INFO] Registering route: GET /api/users/:id
[2025-08-02 18:24:22][INFO] Registering route: PUT /api/users/:id
[2025-08-02 18:24:22][INFO] Registering route: DELETE /api/users/:id
[2025-08-02 18:24:22][INFO] Registering route: GET /api/stats
[2025-08-02 18:24:22][INFO] Registering route: GET /api/stats/:type
[2025-08-02 18:24:22][INFO] Registering route: GET /api/settings/background
[2025-08-02 18:24:22][INFO] Registering route: POST /api/settings/background
[2025-08-02 18:24:22][INFO] Registering route: GET /api/admin/settings/background
[2025-08-02 18:24:22][INFO] Dispatching request: POST /api/admin/login
[2025-08-02 18:24:22][INFO] 数据库连接成功: 127.0.0.1:3306/plb_kj
[2025-08-02 18:24:22][INFO] Admin login attempt for username: admin
[2025-08-02 18:24:22][INFO] Admin user logged in successfully: admin
[2025-08-02 18:24:32][INFO] Request: GET /api/settings/background
[2025-08-02 18:24:32][INFO] Registering route: GET /api/health
[2025-08-02 18:24:32][INFO] Registering route: POST /api/admin/login
[2025-08-02 18:24:32][INFO] Registering route: POST /api/users/login
[2025-08-02 18:24:32][INFO] Registering route: GET /api/auth/captcha
[2025-08-02 18:24:32][INFO] Registering route: GET /api/auth/validate
[2025-08-02 18:24:32][INFO] Registering route: GET /api/users
[2025-08-02 18:24:32][INFO] Registering route: POST /api/users
[2025-08-02 18:24:32][INFO] Registering route: GET /api/users/profile
[2025-08-02 18:24:32][INFO] Registering route: GET /api/users/:id
[2025-08-02 18:24:32][INFO] Registering route: PUT /api/users/:id
[2025-08-02 18:24:32][INFO] Registering route: DELETE /api/users/:id
[2025-08-02 18:24:32][INFO] Registering route: GET /api/stats
[2025-08-02 18:24:32][INFO] Registering route: GET /api/stats/:type
[2025-08-02 18:24:32][INFO] Registering route: GET /api/settings/background
[2025-08-02 18:24:32][INFO] Registering route: POST /api/settings/background
[2025-08-02 18:24:32][INFO] Registering route: GET /api/admin/settings/background
[2025-08-02 18:24:32][INFO] Dispatching request: GET /api/settings/background
[2025-08-02 18:24:32][INFO] 数据库连接成功: 127.0.0.1:3306/plb_kj
[2025-08-02 18:39:32][INFO] Request: GET /api/settings/background
[2025-08-02 18:39:32][INFO] Registering route: GET /api/health
[2025-08-02 18:39:32][INFO] Registering route: POST /api/admin/login
[2025-08-02 18:39:32][INFO] Registering route: POST /api/users/login
[2025-08-02 18:39:32][INFO] Registering route: GET /api/auth/captcha
[2025-08-02 18:39:32][INFO] Registering route: GET /api/auth/validate
[2025-08-02 18:39:32][INFO] Registering route: GET /api/users
[2025-08-02 18:39:32][INFO] Registering route: POST /api/users
[2025-08-02 18:39:32][INFO] Registering route: GET /api/users/profile
[2025-08-02 18:39:32][INFO] Registering route: GET /api/users/:id
[2025-08-02 18:39:32][INFO] Registering route: PUT /api/users/:id
[2025-08-02 18:39:32][INFO] Registering route: DELETE /api/users/:id
[2025-08-02 18:39:32][INFO] Registering route: GET /api/stats
[2025-08-02 18:39:32][INFO] Registering route: GET /api/stats/:type
[2025-08-02 18:39:32][INFO] Registering route: GET /api/settings/background
[2025-08-02 18:39:32][INFO] Registering route: POST /api/settings/background
[2025-08-02 18:39:32][INFO] Registering route: GET /api/admin/settings/background
[2025-08-02 18:39:32][INFO] Dispatching request: GET /api/settings/background
[2025-08-02 18:39:32][INFO] 数据库连接成功: 127.0.0.1:3306/plb_kj
[2025-08-02 18:39:32][INFO] Request: GET /api/auth/captcha
[2025-08-02 18:39:32][INFO] Registering route: GET /api/health
[2025-08-02 18:39:32][INFO] Registering route: POST /api/admin/login
[2025-08-02 18:39:32][INFO] Registering route: POST /api/users/login
[2025-08-02 18:39:32][INFO] Registering route: GET /api/auth/captcha
[2025-08-02 18:39:32][INFO] Registering route: GET /api/auth/validate
[2025-08-02 18:39:32][INFO] Registering route: GET /api/users
[2025-08-02 18:39:32][INFO] Registering route: POST /api/users
[2025-08-02 18:39:32][INFO] Registering route: GET /api/users/profile
[2025-08-02 18:39:32][INFO] Registering route: GET /api/users/:id
[2025-08-02 18:39:32][INFO] Registering route: PUT /api/users/:id
[2025-08-02 18:39:32][INFO] Registering route: DELETE /api/users/:id
[2025-08-02 18:39:32][INFO] Registering route: GET /api/stats
[2025-08-02 18:39:32][INFO] Registering route: GET /api/stats/:type
[2025-08-02 18:39:32][INFO] Registering route: GET /api/settings/background
[2025-08-02 18:39:32][INFO] Registering route: POST /api/settings/background
[2025-08-02 18:39:32][INFO] Registering route: GET /api/admin/settings/background
[2025-08-02 18:39:32][INFO] Dispatching request: GET /api/auth/captcha
[2025-08-02 18:39:32][INFO] 数据库连接成功: 127.0.0.1:3306/plb_kj
[2025-08-02 18:42:49][INFO] Request: POST /api/admin/login
[2025-08-02 18:42:49][INFO] Registering route: GET /api/health
[2025-08-02 18:42:49][INFO] Registering route: POST /api/admin/login
[2025-08-02 18:42:49][INFO] Registering route: POST /api/users/login
[2025-08-02 18:42:49][INFO] Registering route: GET /api/auth/captcha
[2025-08-02 18:42:49][INFO] Registering route: GET /api/auth/validate
[2025-08-02 18:42:49][INFO] Registering route: GET /api/users
[2025-08-02 18:42:49][INFO] Registering route: POST /api/users
[2025-08-02 18:42:49][INFO] Registering route: GET /api/users/profile
[2025-08-02 18:42:49][INFO] Registering route: GET /api/users/:id
[2025-08-02 18:42:49][INFO] Registering route: PUT /api/users/:id
[2025-08-02 18:42:49][INFO] Registering route: DELETE /api/users/:id
[2025-08-02 18:42:49][INFO] Registering route: GET /api/stats
[2025-08-02 18:42:49][INFO] Registering route: GET /api/stats/:type
[2025-08-02 18:42:49][INFO] Registering route: GET /api/settings/background
[2025-08-02 18:42:49][INFO] Registering route: POST /api/settings/background
[2025-08-02 18:42:49][INFO] Registering route: GET /api/admin/settings/background
[2025-08-02 18:42:49][INFO] Dispatching request: POST /api/admin/login
[2025-08-02 18:42:49][INFO] 数据库连接成功: 127.0.0.1:3306/plb_kj
[2025-08-02 18:42:49][INFO] Admin login attempt for username: admin
[2025-08-02 18:42:49][WARNING] Invalid captcha attempt for user: admin
[2025-08-02 18:42:49][ERROR] Admin login error: 验证码错误
[2025-08-02 18:42:49][INFO] Request: GET /api/auth/captcha
[2025-08-02 18:42:49][INFO] Registering route: GET /api/health
[2025-08-02 18:42:49][INFO] Registering route: POST /api/admin/login
[2025-08-02 18:42:49][INFO] Registering route: POST /api/users/login
[2025-08-02 18:42:49][INFO] Registering route: GET /api/auth/captcha
[2025-08-02 18:42:49][INFO] Registering route: GET /api/auth/validate
[2025-08-02 18:42:49][INFO] Registering route: GET /api/users
[2025-08-02 18:42:49][INFO] Registering route: POST /api/users
[2025-08-02 18:42:49][INFO] Registering route: GET /api/users/profile
[2025-08-02 18:42:49][INFO] Registering route: GET /api/users/:id
[2025-08-02 18:42:49][INFO] Registering route: PUT /api/users/:id
[2025-08-02 18:42:49][INFO] Registering route: DELETE /api/users/:id
[2025-08-02 18:42:49][INFO] Registering route: GET /api/stats
[2025-08-02 18:42:49][INFO] Registering route: GET /api/stats/:type
[2025-08-02 18:42:49][INFO] Registering route: GET /api/settings/background
[2025-08-02 18:42:49][INFO] Registering route: POST /api/settings/background
[2025-08-02 18:42:49][INFO] Registering route: GET /api/admin/settings/background
[2025-08-02 18:42:49][INFO] Dispatching request: GET /api/auth/captcha
[2025-08-02 18:42:49][INFO] 数据库连接成功: 127.0.0.1:3306/plb_kj
[2025-08-02 18:43:12][INFO] Request: POST /api/admin/login
[2025-08-02 18:43:12][INFO] Registering route: GET /api/health
[2025-08-02 18:43:12][INFO] Registering route: POST /api/admin/login
[2025-08-02 18:43:12][INFO] Registering route: POST /api/users/login
[2025-08-02 18:43:12][INFO] Registering route: GET /api/auth/captcha
[2025-08-02 18:43:12][INFO] Registering route: GET /api/auth/validate
[2025-08-02 18:43:12][INFO] Registering route: GET /api/users
[2025-08-02 18:43:12][INFO] Registering route: POST /api/users
[2025-08-02 18:43:12][INFO] Registering route: GET /api/users/profile
[2025-08-02 18:43:12][INFO] Registering route: GET /api/users/:id
[2025-08-02 18:43:12][INFO] Registering route: PUT /api/users/:id
[2025-08-02 18:43:12][INFO] Registering route: DELETE /api/users/:id
[2025-08-02 18:43:12][INFO] Registering route: GET /api/stats
[2025-08-02 18:43:12][INFO] Registering route: GET /api/stats/:type
[2025-08-02 18:43:12][INFO] Registering route: GET /api/settings/background
[2025-08-02 18:43:12][INFO] Registering route: POST /api/settings/background
[2025-08-02 18:43:12][INFO] Registering route: GET /api/admin/settings/background
[2025-08-02 18:43:12][INFO] Dispatching request: POST /api/admin/login
[2025-08-02 18:43:12][INFO] 数据库连接成功: 127.0.0.1:3306/plb_kj
[2025-08-02 18:43:12][INFO] Admin login attempt for username: admin
[2025-08-02 18:43:12][INFO] Admin user logged in successfully: admin
[2025-08-02 18:43:34][INFO] Request: GET /api/settings/background
[2025-08-02 18:43:34][INFO] Registering route: GET /api/health
[2025-08-02 18:43:34][INFO] Registering route: POST /api/admin/login
[2025-08-02 18:43:34][INFO] Registering route: POST /api/users/login
[2025-08-02 18:43:34][INFO] Registering route: GET /api/auth/captcha
[2025-08-02 18:43:34][INFO] Registering route: GET /api/auth/validate
[2025-08-02 18:43:34][INFO] Registering route: GET /api/users
[2025-08-02 18:43:34][INFO] Registering route: POST /api/users
[2025-08-02 18:43:34][INFO] Registering route: GET /api/users/profile
[2025-08-02 18:43:34][INFO] Registering route: GET /api/users/:id
[2025-08-02 18:43:34][INFO] Registering route: PUT /api/users/:id
[2025-08-02 18:43:34][INFO] Registering route: DELETE /api/users/:id
[2025-08-02 18:43:34][INFO] Registering route: GET /api/stats
[2025-08-02 18:43:34][INFO] Registering route: GET /api/stats/:type
[2025-08-02 18:43:34][INFO] Registering route: GET /api/settings/background
[2025-08-02 18:43:34][INFO] Registering route: POST /api/settings/background
[2025-08-02 18:43:34][INFO] Registering route: GET /api/admin/settings/background
[2025-08-02 18:43:34][INFO] Dispatching request: GET /api/settings/background
[2025-08-02 18:43:34][INFO] 数据库连接成功: 127.0.0.1:3306/plb_kj
[2025-08-02 18:43:47][INFO] Request: GET /api/settings/background
[2025-08-02 18:43:47][INFO] Registering route: GET /api/health
[2025-08-02 18:43:47][INFO] Registering route: POST /api/admin/login
[2025-08-02 18:43:47][INFO] Registering route: POST /api/users/login
[2025-08-02 18:43:47][INFO] Registering route: GET /api/auth/captcha
[2025-08-02 18:43:47][INFO] Registering route: GET /api/auth/validate
[2025-08-02 18:43:47][INFO] Registering route: GET /api/users
[2025-08-02 18:43:47][INFO] Registering route: POST /api/users
[2025-08-02 18:43:47][INFO] Registering route: GET /api/users/profile
[2025-08-02 18:43:47][INFO] Registering route: GET /api/users/:id
[2025-08-02 18:43:47][INFO] Registering route: PUT /api/users/:id
[2025-08-02 18:43:47][INFO] Registering route: DELETE /api/users/:id
[2025-08-02 18:43:47][INFO] Registering route: GET /api/stats
[2025-08-02 18:43:47][INFO] Registering route: GET /api/stats/:type
[2025-08-02 18:43:47][INFO] Registering route: GET /api/settings/background
[2025-08-02 18:43:47][INFO] Registering route: POST /api/settings/background
[2025-08-02 18:43:47][INFO] Registering route: GET /api/admin/settings/background
[2025-08-02 18:43:47][INFO] Dispatching request: GET /api/settings/background
[2025-08-02 18:43:47][INFO] 数据库连接成功: 127.0.0.1:3306/plb_kj
[2025-08-02 18:44:46][INFO] Request: POST /api/settings/background
[2025-08-02 18:44:46][INFO] Registering route: GET /api/health
[2025-08-02 18:44:46][INFO] Registering route: POST /api/admin/login
[2025-08-02 18:44:46][INFO] Registering route: POST /api/users/login
[2025-08-02 18:44:46][INFO] Registering route: GET /api/auth/captcha
[2025-08-02 18:44:46][INFO] Registering route: GET /api/auth/validate
[2025-08-02 18:44:46][INFO] Registering route: GET /api/users
[2025-08-02 18:44:46][INFO] Registering route: POST /api/users
[2025-08-02 18:44:46][INFO] Registering route: GET /api/users/profile
[2025-08-02 18:44:46][INFO] Registering route: GET /api/users/:id
[2025-08-02 18:44:46][INFO] Registering route: PUT /api/users/:id
[2025-08-02 18:44:46][INFO] Registering route: DELETE /api/users/:id
[2025-08-02 18:44:46][INFO] Registering route: GET /api/stats
[2025-08-02 18:44:46][INFO] Registering route: GET /api/stats/:type
[2025-08-02 18:44:46][INFO] Registering route: GET /api/settings/background
[2025-08-02 18:44:46][INFO] Registering route: POST /api/settings/background
[2025-08-02 18:44:46][INFO] Registering route: GET /api/admin/settings/background
[2025-08-02 18:44:46][INFO] Dispatching request: POST /api/settings/background
[2025-08-02 18:44:46][INFO] 数据库连接成功: 127.0.0.1:3306/plb_kj
[2025-08-02 18:44:47][INFO] Request: POST /api/settings/background
[2025-08-02 18:44:47][INFO] Registering route: GET /api/health
[2025-08-02 18:44:47][INFO] Registering route: POST /api/admin/login
[2025-08-02 18:44:47][INFO] Registering route: POST /api/users/login
[2025-08-02 18:44:47][INFO] Registering route: GET /api/auth/captcha
[2025-08-02 18:44:47][INFO] Registering route: GET /api/auth/validate
[2025-08-02 18:44:47][INFO] Registering route: GET /api/users
[2025-08-02 18:44:47][INFO] Registering route: POST /api/users
[2025-08-02 18:44:47][INFO] Registering route: GET /api/users/profile
[2025-08-02 18:44:47][INFO] Registering route: GET /api/users/:id
[2025-08-02 18:44:47][INFO] Registering route: PUT /api/users/:id
[2025-08-02 18:44:47][INFO] Registering route: DELETE /api/users/:id
[2025-08-02 18:44:47][INFO] Registering route: GET /api/stats
[2025-08-02 18:44:47][INFO] Registering route: GET /api/stats/:type
[2025-08-02 18:44:47][INFO] Registering route: GET /api/settings/background
[2025-08-02 18:44:47][INFO] Registering route: POST /api/settings/background
[2025-08-02 18:44:47][INFO] Registering route: GET /api/admin/settings/background
[2025-08-02 18:44:47][INFO] Dispatching request: POST /api/settings/background
[2025-08-02 18:44:47][INFO] 数据库连接成功: 127.0.0.1:3306/plb_kj
[2025-08-02 18:50:05][INFO] Request: GET /api/settings/background
[2025-08-02 18:50:05][INFO] Registering route: GET /api/health
[2025-08-02 18:50:05][INFO] Registering route: POST /api/admin/login
[2025-08-02 18:50:05][INFO] Registering route: POST /api/users/login
[2025-08-02 18:50:05][INFO] Registering route: GET /api/auth/captcha
[2025-08-02 18:50:05][INFO] Registering route: GET /api/auth/validate
[2025-08-02 18:50:05][INFO] Registering route: GET /api/users
[2025-08-02 18:50:05][INFO] Registering route: POST /api/users
[2025-08-02 18:50:05][INFO] Registering route: GET /api/users/profile
[2025-08-02 18:50:05][INFO] Registering route: GET /api/users/:id
[2025-08-02 18:50:05][INFO] Registering route: PUT /api/users/:id
[2025-08-02 18:50:05][INFO] Registering route: DELETE /api/users/:id
[2025-08-02 18:50:05][INFO] Registering route: GET /api/stats
[2025-08-02 18:50:05][INFO] Registering route: GET /api/stats/:type
[2025-08-02 18:50:05][INFO] Registering route: GET /api/settings/background
[2025-08-02 18:50:05][INFO] Registering route: POST /api/settings/background
[2025-08-02 18:50:05][INFO] Registering route: GET /api/admin/settings/background
[2025-08-02 18:50:05][INFO] Dispatching request: GET /api/settings/background
[2025-08-02 18:50:05][INFO] 数据库连接成功: 127.0.0.1:3306/plb_kj
[2025-08-02 18:50:05][INFO] Request: GET /api/auth/captcha
[2025-08-02 18:50:05][INFO] Registering route: GET /api/health
[2025-08-02 18:50:05][INFO] Registering route: POST /api/admin/login
[2025-08-02 18:50:05][INFO] Registering route: POST /api/users/login
[2025-08-02 18:50:05][INFO] Registering route: GET /api/auth/captcha
[2025-08-02 18:50:05][INFO] Registering route: GET /api/auth/validate
[2025-08-02 18:50:05][INFO] Registering route: GET /api/users
[2025-08-02 18:50:05][INFO] Registering route: POST /api/users
[2025-08-02 18:50:05][INFO] Registering route: GET /api/users/profile
[2025-08-02 18:50:05][INFO] Registering route: GET /api/users/:id
[2025-08-02 18:50:05][INFO] Registering route: PUT /api/users/:id
[2025-08-02 18:50:05][INFO] Registering route: DELETE /api/users/:id
[2025-08-02 18:50:05][INFO] Registering route: GET /api/stats
[2025-08-02 18:50:05][INFO] Registering route: GET /api/stats/:type
[2025-08-02 18:50:05][INFO] Registering route: GET /api/settings/background
[2025-08-02 18:50:05][INFO] Registering route: POST /api/settings/background
[2025-08-02 18:50:05][INFO] Registering route: GET /api/admin/settings/background
[2025-08-02 18:50:05][INFO] Dispatching request: GET /api/auth/captcha
[2025-08-02 18:50:05][INFO] 数据库连接成功: 127.0.0.1:3306/plb_kj
[2025-08-02 18:51:38][INFO] Request: POST /api/admin/login
[2025-08-02 18:51:38][INFO] Registering route: GET /api/health
[2025-08-02 18:51:38][INFO] Registering route: POST /api/admin/login
[2025-08-02 18:51:38][INFO] Registering route: POST /api/users/login
[2025-08-02 18:51:38][INFO] Registering route: GET /api/auth/captcha
[2025-08-02 18:51:38][INFO] Registering route: GET /api/auth/validate
[2025-08-02 18:51:38][INFO] Registering route: GET /api/users
[2025-08-02 18:51:38][INFO] Registering route: POST /api/users
[2025-08-02 18:51:38][INFO] Registering route: GET /api/users/profile
[2025-08-02 18:51:38][INFO] Registering route: GET /api/users/:id
[2025-08-02 18:51:38][INFO] Registering route: PUT /api/users/:id
[2025-08-02 18:51:38][INFO] Registering route: DELETE /api/users/:id
[2025-08-02 18:51:38][INFO] Registering route: GET /api/stats
[2025-08-02 18:51:38][INFO] Registering route: GET /api/stats/:type
[2025-08-02 18:51:38][INFO] Registering route: GET /api/settings/background
[2025-08-02 18:51:38][INFO] Registering route: POST /api/settings/background
[2025-08-02 18:51:38][INFO] Registering route: GET /api/admin/settings/background
[2025-08-02 18:51:38][INFO] Dispatching request: POST /api/admin/login
[2025-08-02 18:51:38][INFO] 数据库连接成功: 127.0.0.1:3306/plb_kj
[2025-08-02 18:51:38][INFO] Admin login attempt for username: test01
[2025-08-02 18:51:38][WARNING] Failed login attempt for admin user: test01
[2025-08-02 18:51:38][ERROR] Admin login error: 用户名或密码错误
[2025-08-02 18:51:38][INFO] Request: GET /api/auth/captcha
[2025-08-02 18:51:38][INFO] Registering route: GET /api/health
[2025-08-02 18:51:38][INFO] Registering route: POST /api/admin/login
[2025-08-02 18:51:38][INFO] Registering route: POST /api/users/login
[2025-08-02 18:51:38][INFO] Registering route: GET /api/auth/captcha
[2025-08-02 18:51:38][INFO] Registering route: GET /api/auth/validate
[2025-08-02 18:51:38][INFO] Registering route: GET /api/users
[2025-08-02 18:51:38][INFO] Registering route: POST /api/users
[2025-08-02 18:51:38][INFO] Registering route: GET /api/users/profile
[2025-08-02 18:51:38][INFO] Registering route: GET /api/users/:id
[2025-08-02 18:51:38][INFO] Registering route: PUT /api/users/:id
[2025-08-02 18:51:38][INFO] Registering route: DELETE /api/users/:id
[2025-08-02 18:51:38][INFO] Registering route: GET /api/stats
[2025-08-02 18:51:38][INFO] Registering route: GET /api/stats/:type
[2025-08-02 18:51:38][INFO] Registering route: GET /api/settings/background
[2025-08-02 18:51:38][INFO] Registering route: POST /api/settings/background
[2025-08-02 18:51:38][INFO] Registering route: GET /api/admin/settings/background
[2025-08-02 18:51:38][INFO] Dispatching request: GET /api/auth/captcha
[2025-08-02 18:51:38][INFO] 数据库连接成功: 127.0.0.1:3306/plb_kj
[2025-08-02 18:52:47][INFO] Request: POST /api/users
[2025-08-02 18:52:47][INFO] Registering route: GET /api/health
[2025-08-02 18:52:47][INFO] Registering route: POST /api/admin/login
[2025-08-02 18:52:47][INFO] Registering route: POST /api/users/login
[2025-08-02 18:52:47][INFO] Registering route: GET /api/auth/captcha
[2025-08-02 18:52:47][INFO] Registering route: GET /api/auth/validate
[2025-08-02 18:52:47][INFO] Registering route: GET /api/users
[2025-08-02 18:52:47][INFO] Registering route: POST /api/users
[2025-08-02 18:52:47][INFO] Registering route: GET /api/users/profile
[2025-08-02 18:52:47][INFO] Registering route: GET /api/users/:id
[2025-08-02 18:52:47][INFO] Registering route: PUT /api/users/:id
[2025-08-02 18:52:47][INFO] Registering route: DELETE /api/users/:id
[2025-08-02 18:52:47][INFO] Registering route: GET /api/stats
[2025-08-02 18:52:47][INFO] Registering route: GET /api/stats/:type
[2025-08-02 18:52:47][INFO] Registering route: GET /api/settings/background
[2025-08-02 18:52:47][INFO] Registering route: POST /api/settings/background
[2025-08-02 18:52:47][INFO] Registering route: GET /api/admin/settings/background
[2025-08-02 18:52:47][INFO] Dispatching request: POST /api/users
[2025-08-02 18:52:47][INFO] 数据库连接成功: 127.0.0.1:3306/plb_kj
[2025-08-02 19:33:10][INFO] Request: GET /api/settings/background
[2025-08-02 19:33:10][INFO] Registering route: GET /api/health
[2025-08-02 19:33:10][INFO] Registering route: POST /api/admin/login
[2025-08-02 19:33:10][INFO] Registering route: POST /api/users/login
[2025-08-02 19:33:10][INFO] Registering route: GET /api/auth/captcha
[2025-08-02 19:33:10][INFO] Registering route: GET /api/auth/validate
[2025-08-02 19:33:10][INFO] Registering route: GET /api/users
[2025-08-02 19:33:10][INFO] Registering route: POST /api/users
[2025-08-02 19:33:10][INFO] Registering route: GET /api/users/profile
[2025-08-02 19:33:10][INFO] Registering route: GET /api/users/:id
[2025-08-02 19:33:10][INFO] Registering route: PUT /api/users/:id
[2025-08-02 19:33:10][INFO] Registering route: DELETE /api/users/:id
[2025-08-02 19:33:10][INFO] Registering route: GET /api/stats
[2025-08-02 19:33:10][INFO] Registering route: GET /api/stats/:type
[2025-08-02 19:33:10][INFO] Registering route: GET /api/settings/background
[2025-08-02 19:33:10][INFO] Registering route: POST /api/settings/background
[2025-08-02 19:33:10][INFO] Registering route: GET /api/admin/settings/background
[2025-08-02 19:33:10][INFO] Dispatching request: GET /api/settings/background
[2025-08-02 19:33:10][INFO] 数据库连接成功: 127.0.0.1:3306/plb_kj
[2025-08-02 19:33:10][INFO] Request: GET /api/auth/captcha
[2025-08-02 19:33:10][INFO] Registering route: GET /api/health
[2025-08-02 19:33:10][INFO] Registering route: POST /api/admin/login
[2025-08-02 19:33:10][INFO] Registering route: POST /api/users/login
[2025-08-02 19:33:10][INFO] Registering route: GET /api/auth/captcha
[2025-08-02 19:33:10][INFO] Registering route: GET /api/auth/validate
[2025-08-02 19:33:10][INFO] Registering route: GET /api/users
[2025-08-02 19:33:10][INFO] Registering route: POST /api/users
[2025-08-02 19:33:10][INFO] Registering route: GET /api/users/profile
[2025-08-02 19:33:10][INFO] Registering route: GET /api/users/:id
[2025-08-02 19:33:10][INFO] Registering route: PUT /api/users/:id
[2025-08-02 19:33:10][INFO] Registering route: DELETE /api/users/:id
[2025-08-02 19:33:10][INFO] Registering route: GET /api/stats
[2025-08-02 19:33:10][INFO] Registering route: GET /api/stats/:type
[2025-08-02 19:33:10][INFO] Registering route: GET /api/settings/background
[2025-08-02 19:33:10][INFO] Registering route: POST /api/settings/background
[2025-08-02 19:33:10][INFO] Registering route: GET /api/admin/settings/background
[2025-08-02 19:33:10][INFO] Dispatching request: GET /api/auth/captcha
[2025-08-02 19:33:10][INFO] 数据库连接成功: 127.0.0.1:3306/plb_kj
[2025-08-02 19:33:22][INFO] Request: POST /api/admin/login
[2025-08-02 19:33:22][INFO] Registering route: GET /api/health
[2025-08-02 19:33:22][INFO] Registering route: POST /api/admin/login
[2025-08-02 19:33:22][INFO] Registering route: POST /api/users/login
[2025-08-02 19:33:22][INFO] Registering route: GET /api/auth/captcha
[2025-08-02 19:33:22][INFO] Registering route: GET /api/auth/validate
[2025-08-02 19:33:22][INFO] Registering route: GET /api/users
[2025-08-02 19:33:22][INFO] Registering route: POST /api/users
[2025-08-02 19:33:22][INFO] Registering route: GET /api/users/profile
[2025-08-02 19:33:22][INFO] Registering route: GET /api/users/:id
[2025-08-02 19:33:22][INFO] Registering route: PUT /api/users/:id
[2025-08-02 19:33:22][INFO] Registering route: DELETE /api/users/:id
[2025-08-02 19:33:22][INFO] Registering route: GET /api/stats
[2025-08-02 19:33:22][INFO] Registering route: GET /api/stats/:type
[2025-08-02 19:33:22][INFO] Registering route: GET /api/settings/background
[2025-08-02 19:33:22][INFO] Registering route: POST /api/settings/background
[2025-08-02 19:33:22][INFO] Registering route: GET /api/admin/settings/background
[2025-08-02 19:33:22][INFO] Dispatching request: POST /api/admin/login
[2025-08-02 19:33:22][INFO] 数据库连接成功: 127.0.0.1:3306/plb_kj
[2025-08-02 19:33:22][INFO] Admin login attempt for username: admin
[2025-08-02 19:33:22][INFO] Admin user logged in successfully: admin
