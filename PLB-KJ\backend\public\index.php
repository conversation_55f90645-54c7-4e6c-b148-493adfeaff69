<?php

// 跨境电商管理系统后端入口文件

// 允许跨域请求
header('Access-Control-Allow-Origin: *');
header('Content-Type: application/json; charset=UTF-8');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization, X-Requested-With');

// 如果是预检请求，直接返回200
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

// 设置错误处理
error_reporting(E_ALL);
ini_set('display_errors', 1);

// 定义项目根目录
define('ROOT_PATH', dirname(__DIR__));

// 自动加载
require __DIR__ . '/../vendor/autoload.php';

// 初始化路由
use App\Routing\Router;
use App\Helpers\Logger;

try {
    // 记录请求信息
    Logger::info(sprintf(
        "Request: %s %s",
        $_SERVER['REQUEST_METHOD'],
        $_SERVER['REQUEST_URI']
    ));

    // 加载路由配置
    require __DIR__ . '/../routes/api.php';
    
    // 分发请求
    Router::dispatch();
    
} catch (\Exception $e) {
    Logger::error("Application error: " . $e->getMessage());
    
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'error' => '服务器内部错误',
        'message' => $e->getMessage()
    ]);
}