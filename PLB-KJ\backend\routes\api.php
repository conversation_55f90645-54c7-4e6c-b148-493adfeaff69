<?php

// API路由文件

use App\Controllers\UserController;
use App\Controllers\AdminController;
use App\Controllers\StatsController;
use App\Controllers\SystemSettingsController;
use App\Routing\Router;
use App\Middleware\AuthMiddleware;
use App\Middleware\CorsMiddleware;
use App\Helpers\Database;

// 应用全局中间件
Router::use([CorsMiddleware::class, 'handle']);

// 公开路由（无需认证）
Router::get('/health', function() {
    try {
        // 检查数据库连接
        $db = Database::getInstance();
        $stmt = $db->query("SELECT 1");
        $result = $stmt->fetch();
        
        if ($result) {
            http_response_code(200);
            header('Content-Type: application/json');
            echo json_encode([
                'success' => true,
                'message' => '服务健康',
                'data' => [
                    'status' => 'healthy',
                    'database' => 'connected',
                    'timestamp' => time(),
                    'datetime' => date('Y-m-d H:i:s')
                ]
            ]);
        } else {
            http_response_code(503);
            header('Content-Type: application/json');
            echo json_encode([
                'success' => false,
                'message' => '数据库连接失败',
                'data' => [
                    'status' => 'unhealthy',
                    'database' => 'disconnected',
                    'timestamp' => time(),
                    'datetime' => date('Y-m-d H:i:s')
                ]
            ]);
        }
    } catch (Exception $e) {
        http_response_code(503);
        header('Content-Type: application/json');
        echo json_encode([
            'success' => false,
            'message' => '服务不健康: ' . $e->getMessage(),
            'data' => [
                'status' => 'unhealthy',
                'database' => 'error',
                'timestamp' => time(),
                'datetime' => date('Y-m-d H:i:s')
            ]
        ]);
    }
    exit;
});
Router::post('/admin/login', [AdminController::class, 'login']);
Router::post('/users/login', [UserController::class, 'login']);
Router::get('/auth/captcha', [AdminController::class, 'getCaptcha']);
Router::get('/auth/validate', [AdminController::class, 'validateToken']);

// 受保护路由组（需要认证）
Router::group(['middleware' => [AuthMiddleware::class, 'handle']], function() {
    // 用户管理路由
    Router::get('/users', [UserController::class, 'getAll']);
    Router::post('/users', [UserController::class, 'create']);
    Router::get('/users/profile', [UserController::class, 'getProfile']);
    Router::get('/users/:id', [UserController::class, 'getById']);
    Router::put('/users/:id', [UserController::class, 'update']);
    Router::delete('/users/:id', [UserController::class, 'delete']);
    
    // 统计数据路由
Router::get('/stats', [StatsController::class, 'getStats']);
Router::get('/stats/:type', [StatsController::class, 'getStatsByType']);

// 系统设置路由
Router::group(['middleware' => [AuthMiddleware::class, 'handle']], function() {
    Router::get('/settings/background', [SystemSettingsController::class, 'getBackgroundSetting']);
    Router::post('/settings/background', [SystemSettingsController::class, 'updateBackgroundSetting']);
        
    // 新增的后台管理页面路由
    Router::get('/admin/settings/background', [SystemSettingsController::class, 'showBackgroundSettingsPage']);
});
});