<?php

try {
    // 创建初始数据库连接
    $pdo = new PDO(
        "mysql:host=localhost",
        "root",
        "",
        [PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION]
    );

    // 创建数据库
    $pdo->exec("CREATE DATABASE IF NOT EXISTS plb_kj CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");
    echo "数据库创建成功\n";

    // 切换到新创建的数据库
    $pdo->exec("USE plb_kj");

    // 读取SQL文件
    $sql = file_get_contents(__DIR__ . '/../跨境电商管理系统数据库设计.sql');

    // 执行SQL语句
    $pdo->exec($sql);
    echo "数据库表创建成功\n";

    echo "数据库初始化完成\n";
} catch (PDOException $e) {
    die("数据库初始化失败: " . $e->getMessage() . "\n");
}