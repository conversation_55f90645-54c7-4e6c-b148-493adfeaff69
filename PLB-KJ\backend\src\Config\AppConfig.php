<?php

namespace App\Config;

/**
 * 应用配置类
 */
class AppConfig
{
    /**
     * 获取配置值
     *
     * @param string $key 配置键
     * @param mixed $default 默认值
     * @return mixed
     */
    public static function get($key, $default = null)
    {
        // 从环境变量获取配置
        $value = $_ENV[$key] ?? $_SERVER[$key] ?? getenv($key);
        
        if ($value !== false && $value !== null) {
            return $value;
        }
        
        // 从配置文件获取配置
        $config = self::loadConfigFile();
        
        return self::getValueFromConfig($config, $key, $default);
    }
    
    /**
     * 加载配置文件
     *
     * @return array
     */
    private static function loadConfigFile()
    {
        $configFile = ROOT_PATH . '/config/app.php';
        
        if (file_exists($configFile)) {
            return require $configFile;
        }
        
        return [];
    }
    
    /**
     * 从配置数组中获取值
     *
     * @param array $config 配置数组
     * @param string $key 配置键
     * @param mixed $default 默认值
     * @return mixed
     */
    private static function getValueFromConfig($config, $key, $default)
    {
        $keys = explode('.', $key);
        $value = $config;
        
        foreach ($keys as $k) {
            if (!is_array($value) || !array_key_exists($k, $value)) {
                return $default;
            }
            
            $value = $value[$k];
        }
        
        return $value;
    }
    
    /**
     * 设置配置值
     *
     * @param string $key 配置键
     * @param mixed $value 配置值
     * @return void
     */
    public static function set($key, $value)
    {
        $_ENV[$key] = $value;
        $_SERVER[$key] = $value;
        putenv("{$key}={$value}");
    }
}