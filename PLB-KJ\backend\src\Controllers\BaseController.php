<?php

namespace App\Controllers;

use App\Helpers\Logger;

/**
 * 基础控制器类
 */
class BaseController
{
    public function __construct()
    {
        // 设置默认响应头
        header('Access-Control-Allow-Origin: *');
        header('Content-Type: application/json; charset=UTF-8');
        header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
        header('Access-Control-Allow-Headers: Content-Type, Authorization, X-Requested-With');
    }

    /**
     * 获取请求数据
     *
     * @return array
     */
    protected function getRequestData()
    {
        try {
            $rawData = file_get_contents('php://input');
            Logger::info("Raw request data: " . $rawData);
            
            if (!empty($rawData)) {
                $data = json_decode($rawData, true);
                if (json_last_error() === JSON_ERROR_NONE) {
                    return $data;
                }
                Logger::warning("Invalid JSON data received: " . json_last_error_msg());
            }
            
            // 如果是表单数据
            if (!empty($_POST)) {
                return $_POST;
            }
            
            return [];
        } catch (\Exception $e) {
            Logger::error("Error getting request data: " . $e->getMessage());
            return [];
        }
    }

    /**
     * 格式化 JSON 响应
     *
     * @param mixed $data 响应数据
     * @param int $statusCode HTTP状态码
     * @param string|null $message 响应消息
     * @return array
     */
    protected function jsonResponse($data = null, $statusCode = 200, $message = null)
    {
        http_response_code($statusCode);
        
        $response = [
            'success' => $statusCode >= 200 && $statusCode < 300,
            'code' => $statusCode
        ];

        if ($data !== null) {
            $response['data'] = $data;
        }

        if ($message !== null) {
            $response['message'] = $message;
        }

        if ($statusCode >= 400) {
            $response['error'] = $message ?? '请求处理失败';
        }

        Logger::info("Sending response: " . json_encode($response));
        return $response;
    }

    /**
     * 发送错误响应
     *
     * @param string $message 错误消息
     * @param int $statusCode HTTP状态码
     * @return array
     */
    protected function errorResponse($message, $statusCode = 400)
    {
        Logger::error("Error response: $message (Status: $statusCode)");
        return $this->jsonResponse(null, $statusCode, $message);
    }
}