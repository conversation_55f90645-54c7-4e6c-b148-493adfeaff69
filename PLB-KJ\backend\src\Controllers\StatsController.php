<?php

namespace App\Controllers;

use App\Models\User;

class StatsController extends BaseController
{
    private $userModel;
    
    public function __construct()
    {
        $this->userModel = new User();
    }
    
    /**
     * 获取系统统计数据
     *
     * @return array
     */
    public function getStats()
    {
        try {
            // 获取用户数
            $users = $this->userModel->findAll();
            $userCount = count($users);

            // 模拟订单数和商品数
            $orderCount = rand(100, 1000);
            $productCount = rand(50, 500);

            return [
                'userCount' => $userCount,
                'orderCount' => $orderCount,
                'productCount' => $productCount
            ];
        } catch (Exception $e) {
            http_response_code(500);
            return ['error' => '服务器内部错误'];
        }
    }

    /**
     * 获取用户统计数据
     *
     * @return array
     */
    public function getUserStats()
    {
        try {
            // 获取所有用户
            $allUsers = $this->userModel->findAll();
            $totalUsers = count($allUsers);

            // 统计各种类型的用户
            $activeUsers = 0;
            $adminUsers = 0;
            $regularUsers = 0;
            $customerUsers = 0;
            $inactiveUsers = 0;

            foreach ($allUsers as $user) {
                // 统计状态
                if ($user['status'] == 1) {
                    $activeUsers++;
                } else {
                    $inactiveUsers++;
                }

                // 统计角色
                switch ($user['role']) {
                    case 'admin':
                        $adminUsers++;
                        break;
                    case 'user':
                        $regularUsers++;
                        break;
                    case 'customer':
                        $customerUsers++;
                        break;
                }
            }

            return [
                'success' => true,
                'total_users' => $totalUsers,
                'active_users' => $activeUsers,
                'inactive_users' => $inactiveUsers,
                'admin_users' => $adminUsers,
                'regular_users' => $regularUsers,
                'customer_users' => $customerUsers
            ];
        } catch (Exception $e) {
            http_response_code(500);
            return ['success' => false, 'error' => '服务器内部错误'];
        }
    }

    /**
     * 根据类型获取统计数据
     *
     * @return array
     */
    public function getStatsByType()
    {
        try {
            // 从URL路径获取类型
            $pathInfo = $_SERVER['PATH_INFO'] ?? '';
            $pathParts = explode('/', trim($pathInfo, '/'));
            $type = end($pathParts);

            switch ($type) {
                case 'users':
                    return $this->getUserStats();
                default:
                    return $this->getStats();
            }
        } catch (Exception $e) {
            http_response_code(500);
            return ['success' => false, 'error' => '服务器内部错误'];
        }
    }
}