<?php

namespace App\Controllers;

use App\Models\User;

class StatsController extends BaseController
{
    private $userModel;
    
    public function __construct()
    {
        $this->userModel = new User();
    }
    
    /**
     * 获取系统统计数据
     *
     * @return array
     */
    public function getStats()
    {
        try {
            // 获取用户数
            $users = $this->userModel->findAll();
            $userCount = count($users);
            
            // 模拟订单数和商品数
            $orderCount = rand(100, 1000);
            $productCount = rand(50, 500);
            
            return [
                'userCount' => $userCount,
                'orderCount' => $orderCount,
                'productCount' => $productCount
            ];
        } catch (Exception $e) {
            http_response_code(500);
            return ['error' => '服务器内部错误'];
        }
    }
}