<?php

namespace App\Controllers;

use App\Helpers\Database;

/**
 * 系统设置控制器
 */
class SystemSettingsController extends BaseController
{
    private $db;
    
    public function __construct()
    {
        parent::__construct();
        $this->db = Database::getInstance();
    }
    
    /**
     * 获取系统设置
     *
     * @param string $key 设置键
     * @return array
     */
    public function getSetting($key)
    {
        try {
            $stmt = $this->db->prepare("SELECT setting_value FROM plb_kj_system_settings WHERE setting_key = ?");
            $stmt->execute([$key]);
            $result = $stmt->fetch();
            
            if ($result) {
                return [
                    'success' => true,
                    'data' => $result['setting_value']
                ];
            } else {
                return [
                    'success' => false,
                    'message' => '设置项不存在'
                ];
            }
        } catch (\Exception $e) {
            return [
                'success' => false,
                'message' => '获取设置失败: ' . $e->getMessage()
            ];
        }
    }
    
    /**
     * 更新系统设置
     *
     * @param string $key 设置键
     * @param string $value 设置值
     * @return array
     */
    public function updateSetting($key, $value)
    {
        try {
            $stmt = $this->db->prepare("INSERT INTO plb_kj_system_settings (setting_key, setting_value, description) VALUES (?, ?, ?) ON DUPLICATE KEY UPDATE setting_value = VALUES(setting_value), updated_at = CURRENT_TIMESTAMP");
            $description = '';
            switch($key) {
                case 'background_image':
                    $description = '登录背景图片';
                    break;
                default:
                    $description = '系统设置';
            }
            $result = $stmt->execute([$key, $value, $description]);
            
            if ($result) {
                return [
                    'success' => true,
                    'message' => '设置更新成功'
                ];
            } else {
                return [
                    'success' => false,
                    'message' => '设置更新失败'
                ];
            }
        } catch (\Exception $e) {
            return [
                'success' => false,
                'message' => '更新设置失败: ' . $e->getMessage()
            ];
        }
    }
    
    /**
     * 获取自定义背景图片设置
     *
     * @return array
     */
    public function getBackgroundImage()
    {
        return $this->getSetting('background_image');
    }
    
    /**
     * 设置自定义背景图片
     *
     * @param string $imageUrl 图片URL
     * @return array
     */
    public function setBackgroundImage($imageUrl)
    {
        return $this->updateSetting('background_image', $imageUrl);
    }
    
    // 获取背景设置
    public function getBackgroundSetting($request, $response) {
        $backgroundImage = $this->getSetting('background_image');
        return $response->withJson([
            'background_image' => $backgroundImage
        ]);
    }

    // 更新背景设置
    public function updateBackgroundSetting($request, $response) {
        $data = $request->getParsedBody();
        $backgroundImage = $data['background_image'] ?? '';
        
        $result = $this->updateSetting('background_image', $backgroundImage);
        if ($result['success']) {
            return $response->withJson([
                'status' => 'success',
                'message' => '背景设置已更新'
            ]);
        } else {
            return $response->withJson([
                'status' => 'error',
                'message' => '背景设置更新失败: ' . $result['message']
            ], 500);
        }
    }
    
    // 显示背景设置页面
    public function showBackgroundSettingsPage($request, $response) {
        // 渲染背景设置页面
        return $this->render('admin/background_settings.html');
    }
}