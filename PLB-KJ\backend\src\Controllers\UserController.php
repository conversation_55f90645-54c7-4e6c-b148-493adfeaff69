<?php

namespace App\Controllers;

use App\Models\User;
use App\Exceptions\AuthException;
use App\Helpers\Logger;
use App\Services\CaptchaService;

class UserController extends BaseController
{
    private $userModel;
    private $captchaService;
    
    public function __construct()
    {
        parent::__construct();
        $this->userModel = new User();
        $this->captchaService = CaptchaService::getInstance();
    }
    
    /**
     * 管理员获取所有用户（带分页和筛选）
     *
     * @return array
     */
    public function getAllForAdmin()
    {
        try {
            // 获取查询参数
            $page = (int)($_GET['page'] ?? 1);
            $pageSize = (int)($_GET['page_size'] ?? 20);
            $search = $_GET['search'] ?? '';
            $role = $_GET['role'] ?? '';
            $status = isset($_GET['status']) ? (int)$_GET['status'] : null;

            // 构建查询条件
            $conditions = [];
            $params = [];

            if (!empty($search)) {
                $conditions[] = "(username LIKE ? OR email LIKE ?)";
                $params[] = "%{$search}%";
                $params[] = "%{$search}%";
            }

            if (!empty($role)) {
                $conditions[] = "role = ?";
                $params[] = $role;
            }

            if ($status !== null) {
                $conditions[] = "status = ?";
                $params[] = $status;
            }

            // 获取用户列表
            $users = $this->userModel->findAllWithPagination($page, $pageSize, $conditions, $params);
            $total = $this->userModel->countWithConditions($conditions, $params);

            // 移除敏感信息
            foreach ($users as &$user) {
                unset($user['password_hash']);
            }

            return [
                'success' => true,
                'data' => $users,
                'total' => $total,
                'page' => $page,
                'page_size' => $pageSize,
                'total_pages' => ceil($total / $pageSize)
            ];
        } catch (Exception $e) {
            http_response_code(500);
            return ['success' => false, 'error' => '服务器内部错误'];
        }
    }
    
    /**
     * 根据ID获取用户
     *
     * @return array
     */
    public function getById()
    {
        try {
            // 从查询参数获取ID
            $id = $_GET['id'] ?? null;
            
            if (!$id) {
                http_response_code(400);
                return ['error' => '缺少用户ID'];
            }
            
            $user = $this->userModel->findById($id);
            if ($user) {
                return $user;
            } else {
                http_response_code(404);
                return ['error' => '用户不存在'];
            }
        } catch (Exception $e) {
            http_response_code(500);
            return ['error' => '服务器内部错误'];
        }
    }
    
    /**
     * 创建用户
     *
     * @return array
     */
    public function create()
    {
        try {
            // 获取POST数据
            $data = json_decode(file_get_contents('php://input'), true);
            
            // 定义验证规则
            $rules = [
                'username' => [
                    'required' => true,
                    'min_length' => 3,
                    'max_length' => 50,
                    'message' => '用户名必须为3-50个字符'
                ],
                'email' => [
                    'required' => true,
                    'email' => true,
                    'max_length' => 100,
                    'message' => '请输入有效的邮箱地址'
                ],
                'password_hash' => [
                    'required' => true,
                    'min_length' => 6,
                    'message' => '密码至少需要6个字符'
                ]
            ];
            
            // 验证数据
            $validation = \App\Validation\Validator::validate($data, $rules);
            
            if ($validation !== true) {
                http_response_code(400);
                return ['error' => implode(', ', $validation)];
            }
            
            // 检查用户名和邮箱是否已存在
            if ($this->userModel->findByUsername($data['username'])) {
                http_response_code(400);
                return ['error' => '用户名已存在'];
            }
            
            if ($this->userModel->findByEmail($data['email'])) {
                http_response_code(400);
                return ['error' => '邮箱已存在'];
            }
            
            // 创建用户
            $userId = $this->userModel->create($data);
            
            if ($userId) {
                $user = $this->userModel->findById($userId);
                http_response_code(201);
                return $user;
            } else {
                http_response_code(500);
                return ['error' => '创建用户失败'];
            }
        } catch (Exception $e) {
            http_response_code(500);
            return ['error' => '服务器内部错误'];
        }
    }
    
    /**
     * 更新用户
     *
     * @return array
     */
    public function update()
    {
        try {
            // 从查询参数获取ID
            $id = $_GET['id'] ?? null;
            
            if (!$id) {
                http_response_code(400);
                return ['error' => '缺少用户ID'];
            }
            
            // 获取PUT数据
            $data = json_decode(file_get_contents('php://input'), true);
            
            // 检查用户是否存在
            $existingUser = $this->userModel->findById($id);
            if (!$existingUser) {
                http_response_code(404);
                return ['error' => '用户不存在'];
            }
            
            // 更新用户
            $result = $this->userModel->update($id, $data);
            
            if ($result) {
                $user = $this->userModel->findById($id);
                return $user;
            } else {
                http_response_code(500);
                return ['error' => '更新用户失败'];
            }
        } catch (Exception $e) {
            http_response_code(500);
            return ['error' => '服务器内部错误'];
        }
    }
    
    /**
     * 用户登录
     *
     * @return array
     */
    public function login()
    {
        try {
            // 获取POST数据
            $rawData = file_get_contents('php://input');
            $data = json_decode($rawData, true);
            
            // 验证必需字段
            if (!isset($data['username']) || !isset($data['password'])) {
                http_response_code(400);
                return [
                    'success' => false,
                    'error' => '用户名和密码是必需的'
                ];
            }
            
            // 验证验证码（如果提供了验证码）
            if (isset($data['captcha'])) {
                session_start();
                
                // 检查验证码是否存在
                if (!isset($_SESSION['captcha_hash']) || !isset($_SESSION['captcha_time'])) {
                    Logger::warning("No captcha found in session for user: {$data['username']}");
                    throw new AuthException('验证码已过期，请刷新页面重试', 400);
                }
                
                // 检查验证码是否过期（5分钟有效期）
                if (time() - $_SESSION['captcha_time'] > 300) {
                    unset($_SESSION['captcha_hash']);
                    unset($_SESSION['captcha_time']);
                    Logger::warning("Captcha expired for user: {$data['username']}");
                    throw new AuthException('验证码已过期，请刷新获取新的验证码', 400);
                }
                
                // 验证验证码
                $expectedHash = $_SESSION['captcha_hash'];
                $inputHash = $this->captchaService->hashCaptcha($data['captcha']);
                
                if ($inputHash !== $expectedHash) {
                    // 清除已使用的验证码
                    unset($_SESSION['captcha_hash']);
                    unset($_SESSION['captcha_time']);
                    Logger::warning("Invalid captcha attempt for user: {$data['username']}");
                    throw new AuthException('验证码错误', 400);
                }
                
                // 清除已使用的验证码
                unset($_SESSION['captcha_hash']);
                unset($_SESSION['captcha_time']);
            }
            
            // 查找用户
            $user = $this->userModel->findByUsername($data['username']);
            
            if (!$user) {
                http_response_code(401);
                return [
                    'success' => false,
                    'error' => '用户名或密码错误'
                ];
            }
            
            // 验证密码
            if (!password_verify($data['password'], $user['password_hash'])) {
                http_response_code(401);
                return [
                    'success' => false,
                    'error' => '用户名或密码错误'
                ];
            }
            
            // 生成token（包含用户ID、时间戳和随机字符串）
            $token = base64_encode($user['id'] . ':' . time() . ':' . bin2hex(random_bytes(16)));
            
            // 设置响应头
            header('Content-Type: application/json; charset=UTF-8');
            http_response_code(200);
            
            return [
                'success' => true,
                'message' => '登录成功',
                'token' => $token,
                'user' => [
                    'id' => $user['id'],
                    'username' => $user['username'],
                    'email' => $user['email'],
                    'role' => $user['role']
                ]
            ];
        } catch (AuthException $e) {
            Logger::error("User login error: {$e->getMessage()}");
            http_response_code($e->getCode());
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        } catch (\Exception $e) {
            Logger::error("User login error: {$e->getMessage()}");
            http_response_code(500);
            return [
                'success' => false,
                'error' => '服务器内部错误'
            ];
        }
    }
    
    /**
     * 删除用户
     *
     * @return array
     */
    public function delete()
    {
        try {
            // 从查询参数获取ID
            $id = $_GET['id'] ?? null;
            
            if (!$id) {
                http_response_code(400);
                return ['error' => '缺少用户ID'];
            }
            
            // 检查用户是否存在
            $existingUser = $this->userModel->findById($id);
            if (!$existingUser) {
                http_response_code(404);
                return ['error' => '用户不存在'];
            }
            
            // 删除用户
            $result = $this->userModel->delete($id);
            
            if ($result) {
                http_response_code(204); // No Content
                return [];
            } else {
                http_response_code(500);
                return ['error' => '删除用户失败'];
            }
        } catch (Exception $e) {
            http_response_code(500);
            return ['error' => '服务器内部错误'];
        }
    }
    
    /**
     * 获取当前用户的个人资料
     *
     * @return array
     */
    public function getProfile()
    {
        try {
            // 从请求头获取token
            $headers = getallheaders();
            $authHeader = $headers['Authorization'] ?? '';
            
            if (empty($authHeader) || !str_starts_with($authHeader, 'Bearer ')) {
                throw new AuthException('未提供有效的认证token', 401);
            }
            
            // 提取token
            $token = substr($authHeader, 7);
            
            // 解析token
            $tokenParts = explode(':', base64_decode($token));
            
            if (count($tokenParts) !== 3) {
                throw new AuthException('token格式无效', 401);
            }
            
            list($userId, $timestamp, $random) = $tokenParts;
            
            // 获取用户资料
            $user = $this->userModel->findById($userId);
            if (!$user) {
                throw new AuthException('用户不存在', 404);
            }
            
            // 只返回安全的用户信息
            return [
                'success' => true,
                'user' => [
                    'id' => $user['id'],
                    'username' => $user['username'],
                    'email' => $user['email'],
                    'role' => $user['role'],
                    'first_name' => $user['first_name'],
                    'last_name' => $user['last_name'],
                    'created_at' => $user['created_at']
                ]
            ];
        } catch (AuthException $e) {
            Logger::warning("获取用户资料失败: {$e->getMessage()}");
            http_response_code($e->getCode());
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        } catch (\Exception $e) {
            Logger::error("获取用户资料时发生错误: {$e->getMessage()}");
            http_response_code(500);
            return [
                'success' => false,
                'error' => '服务器内部错误'
            ];
        }
    }

    /**
     * 管理员创建用户
     *
     * @return array
     */
    public function createForAdmin()
    {
        try {
            // 获取POST数据
            $data = json_decode(file_get_contents('php://input'), true);

            // 定义验证规则
            $rules = [
                'username' => [
                    'required' => true,
                    'min_length' => 3,
                    'max_length' => 50,
                    'message' => '用户名必须为3-50个字符'
                ],
                'email' => [
                    'required' => true,
                    'email' => true,
                    'max_length' => 100,
                    'message' => '请输入有效的邮箱地址'
                ],
                'password' => [
                    'required' => true,
                    'min_length' => 6,
                    'message' => '密码至少需要6个字符'
                ]
            ];

            // 验证数据
            $validation = \App\Validation\Validator::validate($data, $rules);

            if ($validation !== true) {
                http_response_code(400);
                return ['success' => false, 'error' => implode(', ', $validation)];
            }

            // 检查用户名和邮箱是否已存在
            if ($this->userModel->findByUsername($data['username'])) {
                http_response_code(400);
                return ['success' => false, 'error' => '用户名已存在'];
            }

            if ($this->userModel->findByEmail($data['email'])) {
                http_response_code(400);
                return ['success' => false, 'error' => '邮箱已存在'];
            }

            // 设置默认值
            $data['role'] = $data['role'] ?? 'user';
            $data['status'] = $data['status'] ?? 1;
            $data['password_hash'] = password_hash($data['password'], PASSWORD_DEFAULT);
            unset($data['password']);

            // 创建用户
            $userId = $this->userModel->create($data);

            if ($userId) {
                $user = $this->userModel->findById($userId);
                unset($user['password_hash']);
                http_response_code(201);
                return ['success' => true, 'data' => $user];
            } else {
                http_response_code(500);
                return ['success' => false, 'error' => '创建用户失败'];
            }
        } catch (Exception $e) {
            http_response_code(500);
            return ['success' => false, 'error' => '服务器内部错误'];
        }
    }

    /**
     * 管理员获取用户详情
     *
     * @return array
     */
    public function getByIdForAdmin()
    {
        try {
            // 从URL路径获取ID
            $pathInfo = $_SERVER['PATH_INFO'] ?? '';
            $pathParts = explode('/', trim($pathInfo, '/'));
            $id = end($pathParts);

            if (!$id) {
                http_response_code(400);
                return ['success' => false, 'error' => '缺少用户ID'];
            }

            $user = $this->userModel->findById($id);
            if ($user) {
                unset($user['password_hash']);
                return ['success' => true, 'data' => $user];
            } else {
                http_response_code(404);
                return ['success' => false, 'error' => '用户不存在'];
            }
        } catch (Exception $e) {
            http_response_code(500);
            return ['success' => false, 'error' => '服务器内部错误'];
        }
    }

    /**
     * 管理员更新用户
     *
     * @return array
     */
    public function updateForAdmin()
    {
        try {
            // 从URL路径获取ID
            $pathInfo = $_SERVER['PATH_INFO'] ?? '';
            $pathParts = explode('/', trim($pathInfo, '/'));
            $id = end($pathParts);

            if (!$id) {
                http_response_code(400);
                return ['success' => false, 'error' => '缺少用户ID'];
            }

            // 获取PUT数据
            $data = json_decode(file_get_contents('php://input'), true);

            // 检查用户是否存在
            $existingUser = $this->userModel->findById($id);
            if (!$existingUser) {
                http_response_code(404);
                return ['success' => false, 'error' => '用户不存在'];
            }

            // 检查用户名和邮箱是否被其他用户使用
            if (isset($data['username'])) {
                $userWithSameUsername = $this->userModel->findByUsername($data['username']);
                if ($userWithSameUsername && $userWithSameUsername['id'] != $id) {
                    http_response_code(400);
                    return ['success' => false, 'error' => '用户名已被其他用户使用'];
                }
            }

            if (isset($data['email'])) {
                $userWithSameEmail = $this->userModel->findByEmail($data['email']);
                if ($userWithSameEmail && $userWithSameEmail['id'] != $id) {
                    http_response_code(400);
                    return ['success' => false, 'error' => '邮箱已被其他用户使用'];
                }
            }

            // 更新用户
            $result = $this->userModel->update($id, $data);

            if ($result) {
                $user = $this->userModel->findById($id);
                unset($user['password_hash']);
                return ['success' => true, 'data' => $user];
            } else {
                http_response_code(500);
                return ['success' => false, 'error' => '更新用户失败'];
            }
        } catch (Exception $e) {
            http_response_code(500);
            return ['success' => false, 'error' => '服务器内部错误'];
        }
    }

    /**
     * 管理员删除用户
     *
     * @return array
     */
    public function deleteForAdmin()
    {
        try {
            // 从URL路径获取ID
            $pathInfo = $_SERVER['PATH_INFO'] ?? '';
            $pathParts = explode('/', trim($pathInfo, '/'));
            $id = end($pathParts);

            if (!$id) {
                http_response_code(400);
                return ['success' => false, 'error' => '缺少用户ID'];
            }

            // 检查用户是否存在
            $existingUser = $this->userModel->findById($id);
            if (!$existingUser) {
                http_response_code(404);
                return ['success' => false, 'error' => '用户不存在'];
            }

            // 删除用户
            $result = $this->userModel->delete($id);

            if ($result) {
                http_response_code(200);
                return ['success' => true, 'message' => '用户删除成功'];
            } else {
                http_response_code(500);
                return ['success' => false, 'error' => '删除用户失败'];
            }
        } catch (Exception $e) {
            http_response_code(500);
            return ['success' => false, 'error' => '服务器内部错误'];
        }
    }

    /**
     * 管理员批量删除用户
     *
     * @return array
     */
    public function batchDeleteForAdmin()
    {
        try {
            $data = json_decode(file_get_contents('php://input'), true);
            $userIds = $data['user_ids'] ?? [];

            if (empty($userIds) || !is_array($userIds)) {
                http_response_code(400);
                return ['success' => false, 'error' => '请提供要删除的用户ID列表'];
            }

            $deletedCount = 0;
            foreach ($userIds as $userId) {
                if ($this->userModel->delete($userId)) {
                    $deletedCount++;
                }
            }

            return [
                'success' => true,
                'message' => "成功删除 {$deletedCount} 个用户",
                'deleted_count' => $deletedCount
            ];
        } catch (Exception $e) {
            http_response_code(500);
            return ['success' => false, 'error' => '服务器内部错误'];
        }
    }

    /**
     * 管理员更新用户状态
     *
     * @return array
     */
    public function updateStatusForAdmin()
    {
        try {
            // 从URL路径获取ID
            $pathInfo = $_SERVER['PATH_INFO'] ?? '';
            $pathParts = explode('/', trim($pathInfo, '/'));
            $id = $pathParts[count($pathParts) - 2]; // status前面的ID

            if (!$id) {
                http_response_code(400);
                return ['success' => false, 'error' => '缺少用户ID'];
            }

            $data = json_decode(file_get_contents('php://input'), true);
            $status = $data['status'] ?? null;

            if ($status === null || !in_array($status, [0, 1])) {
                http_response_code(400);
                return ['success' => false, 'error' => '状态值无效'];
            }

            // 检查用户是否存在
            $existingUser = $this->userModel->findById($id);
            if (!$existingUser) {
                http_response_code(404);
                return ['success' => false, 'error' => '用户不存在'];
            }

            // 更新状态
            $result = $this->userModel->update($id, ['status' => $status]);

            if ($result) {
                $user = $this->userModel->findById($id);
                unset($user['password_hash']);
                return ['success' => true, 'data' => $user];
            } else {
                http_response_code(500);
                return ['success' => false, 'error' => '更新用户状态失败'];
            }
        } catch (Exception $e) {
            http_response_code(500);
            return ['success' => false, 'error' => '服务器内部错误'];
        }
    }

    /**
     * 管理员重置用户密码
     *
     * @return array
     */
    public function resetPasswordForAdmin()
    {
        try {
            // 从URL路径获取ID
            $pathInfo = $_SERVER['PATH_INFO'] ?? '';
            $pathParts = explode('/', trim($pathInfo, '/'));
            $id = $pathParts[count($pathParts) - 2]; // password前面的ID

            if (!$id) {
                http_response_code(400);
                return ['success' => false, 'error' => '缺少用户ID'];
            }

            $data = json_decode(file_get_contents('php://input'), true);
            $password = $data['password'] ?? '';

            if (strlen($password) < 6) {
                http_response_code(400);
                return ['success' => false, 'error' => '密码至少需要6个字符'];
            }

            // 检查用户是否存在
            $existingUser = $this->userModel->findById($id);
            if (!$existingUser) {
                http_response_code(404);
                return ['success' => false, 'error' => '用户不存在'];
            }

            // 更新密码
            $passwordHash = password_hash($password, PASSWORD_DEFAULT);
            $result = $this->userModel->update($id, ['password_hash' => $passwordHash]);

            if ($result) {
                return ['success' => true, 'message' => '密码重置成功'];
            } else {
                http_response_code(500);
                return ['success' => false, 'error' => '重置密码失败'];
            }
        } catch (Exception $e) {
            http_response_code(500);
            return ['success' => false, 'error' => '服务器内部错误'];
        }
    }
}