<?php

namespace App\Controllers;

use App\Models\User;
use App\Exceptions\AuthException;
use App\Helpers\Logger;
use App\Services\CaptchaService;

class UserController extends BaseController
{
    private $userModel;
    private $captchaService;
    
    public function __construct()
    {
        parent::__construct();
        $this->userModel = new User();
        $this->captchaService = CaptchaService::getInstance();
    }
    
    /**
     * 获取所有用户
     *
     * @return array
     */
    public function getAll()
    {
        try {
            $users = $this->userModel->findAll();
            return $users;
        } catch (Exception $e) {
            http_response_code(500);
            return ['error' => '服务器内部错误'];
        }
    }
    
    /**
     * 根据ID获取用户
     *
     * @return array
     */
    public function getById()
    {
        try {
            // 从查询参数获取ID
            $id = $_GET['id'] ?? null;
            
            if (!$id) {
                http_response_code(400);
                return ['error' => '缺少用户ID'];
            }
            
            $user = $this->userModel->findById($id);
            if ($user) {
                return $user;
            } else {
                http_response_code(404);
                return ['error' => '用户不存在'];
            }
        } catch (Exception $e) {
            http_response_code(500);
            return ['error' => '服务器内部错误'];
        }
    }
    
    /**
     * 创建用户
     *
     * @return array
     */
    public function create()
    {
        try {
            // 获取POST数据
            $data = json_decode(file_get_contents('php://input'), true);
            
            // 定义验证规则
            $rules = [
                'username' => [
                    'required' => true,
                    'min_length' => 3,
                    'max_length' => 50,
                    'message' => '用户名必须为3-50个字符'
                ],
                'email' => [
                    'required' => true,
                    'email' => true,
                    'max_length' => 100,
                    'message' => '请输入有效的邮箱地址'
                ],
                'password_hash' => [
                    'required' => true,
                    'min_length' => 6,
                    'message' => '密码至少需要6个字符'
                ]
            ];
            
            // 验证数据
            $validation = \App\Validation\Validator::validate($data, $rules);
            
            if ($validation !== true) {
                http_response_code(400);
                return ['error' => implode(', ', $validation)];
            }
            
            // 检查用户名和邮箱是否已存在
            if ($this->userModel->findByUsername($data['username'])) {
                http_response_code(400);
                return ['error' => '用户名已存在'];
            }
            
            if ($this->userModel->findByEmail($data['email'])) {
                http_response_code(400);
                return ['error' => '邮箱已存在'];
            }
            
            // 创建用户
            $userId = $this->userModel->create($data);
            
            if ($userId) {
                $user = $this->userModel->findById($userId);
                http_response_code(201);
                return $user;
            } else {
                http_response_code(500);
                return ['error' => '创建用户失败'];
            }
        } catch (Exception $e) {
            http_response_code(500);
            return ['error' => '服务器内部错误'];
        }
    }
    
    /**
     * 更新用户
     *
     * @return array
     */
    public function update()
    {
        try {
            // 从查询参数获取ID
            $id = $_GET['id'] ?? null;
            
            if (!$id) {
                http_response_code(400);
                return ['error' => '缺少用户ID'];
            }
            
            // 获取PUT数据
            $data = json_decode(file_get_contents('php://input'), true);
            
            // 检查用户是否存在
            $existingUser = $this->userModel->findById($id);
            if (!$existingUser) {
                http_response_code(404);
                return ['error' => '用户不存在'];
            }
            
            // 更新用户
            $result = $this->userModel->update($id, $data);
            
            if ($result) {
                $user = $this->userModel->findById($id);
                return $user;
            } else {
                http_response_code(500);
                return ['error' => '更新用户失败'];
            }
        } catch (Exception $e) {
            http_response_code(500);
            return ['error' => '服务器内部错误'];
        }
    }
    
    /**
     * 用户登录
     *
     * @return array
     */
    public function login()
    {
        try {
            // 获取POST数据
            $rawData = file_get_contents('php://input');
            $data = json_decode($rawData, true);
            
            // 验证必需字段
            if (!isset($data['username']) || !isset($data['password'])) {
                http_response_code(400);
                return [
                    'success' => false,
                    'error' => '用户名和密码是必需的'
                ];
            }
            
            // 验证验证码（如果提供了验证码）
            if (isset($data['captcha'])) {
                session_start();
                
                // 检查验证码是否存在
                if (!isset($_SESSION['captcha_hash']) || !isset($_SESSION['captcha_time'])) {
                    Logger::warning("No captcha found in session for user: {$data['username']}");
                    throw new AuthException('验证码已过期，请刷新页面重试', 400);
                }
                
                // 检查验证码是否过期（5分钟有效期）
                if (time() - $_SESSION['captcha_time'] > 300) {
                    unset($_SESSION['captcha_hash']);
                    unset($_SESSION['captcha_time']);
                    Logger::warning("Captcha expired for user: {$data['username']}");
                    throw new AuthException('验证码已过期，请刷新获取新的验证码', 400);
                }
                
                // 验证验证码
                $expectedHash = $_SESSION['captcha_hash'];
                $inputHash = $this->captchaService->hashCaptcha($data['captcha']);
                
                if ($inputHash !== $expectedHash) {
                    // 清除已使用的验证码
                    unset($_SESSION['captcha_hash']);
                    unset($_SESSION['captcha_time']);
                    Logger::warning("Invalid captcha attempt for user: {$data['username']}");
                    throw new AuthException('验证码错误', 400);
                }
                
                // 清除已使用的验证码
                unset($_SESSION['captcha_hash']);
                unset($_SESSION['captcha_time']);
            }
            
            // 查找用户
            $user = $this->userModel->findByUsername($data['username']);
            
            if (!$user) {
                http_response_code(401);
                return [
                    'success' => false,
                    'error' => '用户名或密码错误'
                ];
            }
            
            // 验证密码
            if (!password_verify($data['password'], $user['password_hash'])) {
                http_response_code(401);
                return [
                    'success' => false,
                    'error' => '用户名或密码错误'
                ];
            }
            
            // 生成token（包含用户ID、时间戳和随机字符串）
            $token = base64_encode($user['id'] . ':' . time() . ':' . bin2hex(random_bytes(16)));
            
            // 设置响应头
            header('Content-Type: application/json; charset=UTF-8');
            http_response_code(200);
            
            return [
                'success' => true,
                'message' => '登录成功',
                'token' => $token,
                'user' => [
                    'id' => $user['id'],
                    'username' => $user['username'],
                    'email' => $user['email'],
                    'role' => $user['role']
                ]
            ];
        } catch (AuthException $e) {
            Logger::error("User login error: {$e->getMessage()}");
            http_response_code($e->getCode());
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        } catch (\Exception $e) {
            Logger::error("User login error: {$e->getMessage()}");
            http_response_code(500);
            return [
                'success' => false,
                'error' => '服务器内部错误'
            ];
        }
    }
    
    /**
     * 删除用户
     *
     * @return array
     */
    public function delete()
    {
        try {
            // 从查询参数获取ID
            $id = $_GET['id'] ?? null;
            
            if (!$id) {
                http_response_code(400);
                return ['error' => '缺少用户ID'];
            }
            
            // 检查用户是否存在
            $existingUser = $this->userModel->findById($id);
            if (!$existingUser) {
                http_response_code(404);
                return ['error' => '用户不存在'];
            }
            
            // 删除用户
            $result = $this->userModel->delete($id);
            
            if ($result) {
                http_response_code(204); // No Content
                return [];
            } else {
                http_response_code(500);
                return ['error' => '删除用户失败'];
            }
        } catch (Exception $e) {
            http_response_code(500);
            return ['error' => '服务器内部错误'];
        }
    }
    
    /**
     * 获取当前用户的个人资料
     *
     * @return array
     */
    public function getProfile()
    {
        try {
            // 从请求头获取token
            $headers = getallheaders();
            $authHeader = $headers['Authorization'] ?? '';
            
            if (empty($authHeader) || !str_starts_with($authHeader, 'Bearer ')) {
                throw new AuthException('未提供有效的认证token', 401);
            }
            
            // 提取token
            $token = substr($authHeader, 7);
            
            // 解析token
            $tokenParts = explode(':', base64_decode($token));
            
            if (count($tokenParts) !== 3) {
                throw new AuthException('token格式无效', 401);
            }
            
            list($userId, $timestamp, $random) = $tokenParts;
            
            // 获取用户资料
            $user = $this->userModel->findById($userId);
            if (!$user) {
                throw new AuthException('用户不存在', 404);
            }
            
            // 只返回安全的用户信息
            return [
                'success' => true,
                'user' => [
                    'id' => $user['id'],
                    'username' => $user['username'],
                    'email' => $user['email'],
                    'role' => $user['role'],
                    'first_name' => $user['first_name'],
                    'last_name' => $user['last_name'],
                    'created_at' => $user['created_at']
                ]
            ];
        } catch (AuthException $e) {
            Logger::warning("获取用户资料失败: {$e->getMessage()}");
            http_response_code($e->getCode());
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        } catch (\Exception $e) {
            Logger::error("获取用户资料时发生错误: {$e->getMessage()}");
            http_response_code(500);
            return [
                'success' => false,
                'error' => '服务器内部错误'
            ];
        }
    }
}