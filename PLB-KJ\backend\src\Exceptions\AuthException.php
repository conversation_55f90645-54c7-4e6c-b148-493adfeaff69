<?php

namespace App\Exceptions;

class AuthException extends \Exception
{
    protected $errorCode;

    public function __construct($message = "", $code = 0, $errorCode = "AUTH_ERROR")
    {
        parent::__construct($message, $code);
        $this->errorCode = $errorCode;
    }

    public function getErrorCode()
    {
        return $this->errorCode;
    }

    public static function unauthorized($message = "未授权访问")
    {
        return new self($message, 401, "UNAUTHORIZED");
    }

    public static function forbidden($message = "权限不足")
    {
        return new self($message, 403, "FORBIDDEN");
    }

    public static function tokenExpired($message = "认证已过期")
    {
        return new self($message, 419, "TOKEN_EXPIRED");
    }

    public static function invalidToken($message = "无效的认证令牌")
    {
        return new self($message, 401, "INVALID_TOKEN");
    }
}