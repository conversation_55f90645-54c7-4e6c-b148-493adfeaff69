<?php

namespace App\Helpers;

use PDO;
use PDOException;

class Database
{
    private static $instance = null;
    private $connection = null;
    private $config;
    
    private function __construct()
    {
        $this->loadConfig();
        $this->connect();
    }
    
    private function loadConfig()
    {
        $configFile = __DIR__ . '/../../config/database.php';
        if (!file_exists($configFile)) {
            throw new \Exception('数据库配置文件不存在');
        }
        $this->config = require $configFile;
    }
    
    private function connect()
    {
        try {
            $dsn = "mysql:host={$this->config['host']};port={$this->config['port']};dbname={$this->config['database']};charset={$this->config['charset']}";
            $this->connection = new PDO(
                $dsn,
                $this->config['username'],
                $this->config['password'],
                [
                    PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                    PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
                    PDO::ATTR_EMULATE_PREPARES => false,
                ]
            );
            Logger::info("数据库连接成功: {$this->config['host']}:{$this->config['port']}/{$this->config['database']}");
        } catch (PDOException $e) {
            Logger::error("数据库连接失败: " . $e->getMessage());
            throw new \Exception('数据库连接失败: ' . $e->getMessage());
        }
    }
    
    public static function getInstance()
    {
        if (self::$instance === null) {
            self::$instance = new static();
        }
        return self::$instance;
    }
    
    // PDO 方法代理
    public function prepare($statement, $options = [])
    {
        return $this->connection->prepare($statement, $options);
    }

    public function query($statement)
    {
        return $this->connection->query($statement);
    }

    public function lastInsertId($name = null)
    {
        return $this->connection->lastInsertId($name);
    }

    public function beginTransaction()
    {
        return $this->connection->beginTransaction();
    }

    public function commit()
    {
        return $this->connection->commit();
    }

    public function rollBack()
    {
        return $this->connection->rollBack();
    }

    public function exec($statement)
    {
        return $this->connection->exec($statement);
    }

    public function quote($string, $parameter_type = PDO::PARAM_STR)
    {
        return $this->connection->quote($string, $parameter_type);
    }
    
    private function __clone() {}
    
    public function __wakeup()
    {
        throw new \Exception("不能反序列化单例");
    }
}