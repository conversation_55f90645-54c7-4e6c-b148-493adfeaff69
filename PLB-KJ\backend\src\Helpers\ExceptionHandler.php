<?php

namespace App\Helpers;

/**
 * 异常处理助手类
 */
class ExceptionHandler
{
    /**
     * 注册异常处理函数
     *
     * @return void
     */
    public static function register()
    {
        set_exception_handler([self::class, 'handleException']);
        set_error_handler([self::class, 'handleError']);
    }
    
    /**
     * 处理未捕获的异常
     *
     * @param \Throwable $exception
     * @return void
     */
    public static function handleException($exception)
    {
        // 记录异常日志
        Logger::error("未捕获的异常: " . $exception->getMessage() . " in " . $exception->getFile() . " on line " . $exception->getLine());
        
        // 如果是API请求，返回JSON错误响应
        if (self::isApiRequest()) {
            http_response_code(500);
            header('Content-Type: application/json');
            echo json_encode([
                'error' => '服务器内部错误',
                'message' => $exception->getMessage() // 在生产环境中应该移除这行
            ]);
        } else {
            // 对于非API请求，显示错误页面
            http_response_code(500);
            echo "<h1>服务器内部错误</h1>";
            echo "<p>" . htmlspecialchars($exception->getMessage()) . "</p>";
            // 在生产环境中应该移除下面这行
            echo "<p>在 " . $exception->getFile() . " 第 " . $exception->getLine() . " 行</p>";
        }
        
        exit;
    }
    
    /**
     * 处理PHP错误
     *
     * @param int $severity
     * @param string $message
     * @param string $file
     * @param int $line
     * @return void
     */
    public static function handleError($severity, $message, $file, $line)
    {
        // 记录错误日志
        Logger::error("PHP错误: {$message} in {$file} on line {$line}");
        
        // 如果是致命错误，转换为异常
        if (!(error_reporting() & $severity)) {
            return;
        }
        
        throw new \ErrorException($message, 0, $severity, $file, $line);
    }
    
    /**
     * 判断是否为API请求
     *
     * @return bool
     */
    private static function isApiRequest()
    {
        $path = $_SERVER['REQUEST_URI'] ?? '';
        return strpos($path, '/api/') === 0;
    }
}