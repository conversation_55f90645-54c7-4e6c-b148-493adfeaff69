<?php

namespace App\Helpers;

/**
 * 简单的日志记录工具
 */
class Logger
{
    private static $logFile;
    
    /**
     * 初始化日志文件路径
     *
     * @return void
     */
    public static function init()
    {
        $logDir = __DIR__ . '/../../logs';
        
        // 确保日志目录存在并可写
        if (!file_exists($logDir)) {
            if (!mkdir($logDir, 0777, true)) {
                error_log("无法创建日志目录: $logDir");
                return;
            }
        }
        
        // 设置目录权限
        chmod($logDir, 0777);
        
        self::$logFile = $logDir . '/app.log';
        
        // 如果日志文件不存在，创建它
        if (!file_exists(self::$logFile)) {
            if (!touch(self::$logFile)) {
                error_log("无法创建日志文件: " . self::$logFile);
                return;
            }
            chmod(self::$logFile, 0666);
        }
    }
    
    private static function ensureInitialized()
    {
        if (self::$logFile === null) {
            self::init();
        }
    }
    
    private static function write($level, $message)
    {
        self::ensureInitialized();
        
        if (self::$logFile === null) {
            error_log("Logger未正确初始化");
            return;
        }
        
        $timestamp = date('Y-m-d H:i:s');
        $formattedMessage = "[$timestamp][$level] $message" . PHP_EOL;
        
        try {
            if (file_put_contents(self::$logFile, $formattedMessage, FILE_APPEND | LOCK_EX) === false) {
                error_log("无法写入日志: " . self::$logFile);
            }
        } catch (\Exception $e) {
            error_log("写入日志时发生错误: " . $e->getMessage());
        }
    }
    
    /**
     * 记录一般信息
     *
     * @param string $message
     * @return void
     */
    public static function info($message)
    {
        self::write('INFO', $message);
    }
    
    /**
     * 记录错误信息
     *
     * @param string $message
     * @return void
     */
    public static function error($message)
    {
        self::write('ERROR', $message);
    }
    
    /**
     * 记录警告信息
     *
     * @param string $message
     * @return void
     */
    public static function warning($message)
    {
        self::write('WARNING', $message);
    }
    
    public static function debug($message)
    {
        if (defined('APP_DEBUG') && APP_DEBUG) {
            self::write('DEBUG', $message);
        }
    }
}