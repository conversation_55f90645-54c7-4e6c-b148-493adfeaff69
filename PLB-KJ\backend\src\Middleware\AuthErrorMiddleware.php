<?php

namespace App\Middleware;

use App\Helpers\Logger;

class AuthErrorMiddleware
{
    /**
     * 处理认证相关错误
     *
     * @param \Exception $e
     * @return array
     */
    public static function handle($e)
    {
        Logger::error("认证错误: " . $e->getMessage());
        
        $errorCode = $e->getCode();
        switch ($errorCode) {
            case 401:
                return [
                    'error' => '未授权访问',
                    'code' => 'UNAUTHORIZED',
                    'message' => $e->getMessage()
                ];
            case 403:
                return [
                    'error' => '权限不足',
                    'code' => 'FORBIDDEN',
                    'message' => $e->getMessage()
                ];
            case 419:
                return [
                    'error' => '认证已过期',
                    'code' => 'TOKEN_EXPIRED',
                    'message' => $e->getMessage()
                ];
            default:
                return [
                    'error' => '认证过程发生错误',
                    'code' => 'AUTH_ERROR',
                    'message' => $e->getMessage()
                ];
        }
    }
}