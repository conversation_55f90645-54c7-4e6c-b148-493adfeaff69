<?php

namespace App\Middleware;

use App\Exceptions\AuthException;
use App\Controllers\AdminController;
use App\Helpers\Logger;

class AuthMiddleware
{
    /**
     * 处理认证中间件
     *
     * @param callable $next 下一个处理器
     * @return callable
     */
    public static function handle($next)
    {
        return function () use ($next) {
            try {
                // 排除不需要认证的路由
                $excludedRoutes = [
                    '/api/admin/login',
                    '/api/users/login',
                    '/api/auth/validate'
                ];

                if (in_array($_SERVER['REQUEST_URI'], $excludedRoutes)) {
                    return $next();
                }

                // 验证token
                $adminController = new AdminController();
                $result = $adminController->validateToken();

                if (isset($result['error'])) {
                    throw new AuthException($result['error'], $result['code'] ?? 401);
                }

                // 认证通过，继续处理请求
                return $next();

            } catch (AuthException $e) {
                Logger::warning("Authentication failed: " . $e->getMessage());
                http_response_code($e->getCode());
                return [
                    'error' => $e->getMessage(),
                    'code' => $e->getErrorCode()
                ];
            }
        };
    }
}