<?php

namespace App\Middleware;

/**
 * CORS中间件
 */
class CorsMiddleware
{
    /**
     * 处理CORS请求
     *
     * @return void
     */
    public static function handle()
    {
        // 允许所有来源访问
        header('Access-Control-Allow-Origin: *');
        
        // 允许的请求方法
        header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
        
        // 允许的请求头
        header('Access-Control-Allow-Headers: Content-Type, Authorization, X-Requested-With');
        
        // 预检请求缓存时间
        header('Access-Control-Max-Age: 86400');
        
        // 允许发送认证信息
        header('Access-Control-Allow-Credentials: true');
        
        // 处理预检请求
        if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
            http_response_code(200);
            exit();
        }
    }
}