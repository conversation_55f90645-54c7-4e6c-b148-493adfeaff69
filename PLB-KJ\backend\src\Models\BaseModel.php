<?php

namespace App\Models;

use App\Helpers\Database;
use PDO;

class BaseModel
{
    protected $db;
    protected $table;
    protected $primaryKey = 'id';
    protected $prefix;
    
    public function __construct()
    {
        $this->db = Database::getInstance();
        $config = require __DIR__ . '/../../config/database.php';
        $this->prefix = $config['prefix'];
        
        // 如果子类没有设置表名，从类名推断
        if (empty($this->table)) {
            $className = (new \ReflectionClass($this))->getShortName();
            $this->table = $this->prefix . strtolower($className) . 's';
        } else {
            $this->table = $this->prefix . $this->table;
        }
    }
    
    /**
     * 根据ID查找记录
     *
     * @param int $id
     * @return array|null
     */
    public function findById($id)
    {
        $stmt = $this->db->prepare("SELECT * FROM {$this->table} WHERE {$this->primaryKey} = ?");
        $stmt->execute([$id]);
        return $stmt->fetch(PDO::FETCH_ASSOC);
    }
    
    /**
     * 获取所有记录
     *
     * @return array
     */
    public function findAll()
    {
        $stmt = $this->db->query("SELECT * FROM {$this->table}");
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }
    
    /**
     * 创建新记录
     *
     * @param array $data
     * @return int|false
     */
    public function create($data)
    {
        $fields = array_keys($data);
        $placeholders = str_repeat('?,', count($fields) - 1) . '?';
        $columns = implode(',', $fields);
        
        $stmt = $this->db->prepare("INSERT INTO {$this->table} ($columns) VALUES ($placeholders)");
        $stmt->execute(array_values($data));
        
        return $this->db->lastInsertId();
    }
    
    /**
     * 更新记录
     *
     * @param int $id
     * @param array $data
     * @return bool
     */
    public function update($id, $data)
    {
        $fields = array_keys($data);
        $set = implode('=?,', $fields) . '=?';
        
        $stmt = $this->db->prepare("UPDATE {$this->table} SET $set WHERE {$this->primaryKey} = ?");
        $values = array_values($data);
        $values[] = $id;
        
        return $stmt->execute($values);
    }
    
    /**
     * 删除记录
     *
     * @param int $id
     * @return bool
     */
    public function delete($id)
    {
        $stmt = $this->db->prepare("DELETE FROM {$this->table} WHERE {$this->primaryKey} = ?");
        return $stmt->execute([$id]);
    }
    
    /**
     * 根据条件查找单个记录
     *
     * @param string $field
     * @param mixed $value
     * @return array|null
     */
    public function findBy($field, $value)
    {
        $stmt = $this->db->prepare("SELECT * FROM {$this->table} WHERE $field = ?");
        $stmt->execute([$value]);
        return $stmt->fetch(PDO::FETCH_ASSOC);
    }
}