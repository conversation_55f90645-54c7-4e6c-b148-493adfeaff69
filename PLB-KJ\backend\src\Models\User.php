<?php

namespace App\Models;

use App\Helpers\Logger;

class User extends BaseModel
{
    protected $table = 'users';
    
    /**
     * 根据用户名查找用户
     *
     * @param string $username
     * @return array|null
     */
    public function findByUsername($username)
    {
        return $this->findBy('username', $username);
    }
    
    /**
     * 根据邮箱查找用户
     *
     * @param string $email
     * @return array|null
     */
    public function findByEmail($email)
    {
        return $this->findBy('email', $email);
    }
    
    /**
     * 验证用户密码
     *
     * @param string $password
     * @param string $hash
     * @return bool
     */
    public function verifyPassword($password, $hash)
    {
        return password_verify($password, $hash);
    }
    
    /**
     * 创建新用户
     *
     * @param array $data
     * @return int|false
     */
    public function createUser($data)
    {
        if (isset($data['password'])) {
            $data['password_hash'] = password_hash($data['password'], PASSWORD_DEFAULT);
            unset($data['password']);
        }
        
        return $this->create($data);
    }
    
    /**
     * 更新用户信息
     *
     * @param int $id
     * @param array $data
     * @return bool
     */
    public function updateUser($id, $data)
    {
        if (isset($data['password'])) {
            $data['password_hash'] = password_hash($data['password'], PASSWORD_DEFAULT);
            unset($data['password']);
        }
        
        return $this->update($id, $data);
    }
}