<?php

namespace App\Routing;

use App\Helpers\Logger;

/**
 * 简单路由类
 */
class Router
{
    /**
     * 路由表
     *
     * @var array
     */
    private static $routes = [];
    
    private static $prefix = '/api';
    
    private static $middlewares = [];

    private static $currentGroupMiddlewares = [];

    /**
     * 添加GET路由
     *
     * @param string $path
     * @param callable $callback
     * @return void
     */
    public static function get($path, $callback)
    {
        self::addRoute('GET', $path, $callback);
    }
    
    /**
     * 添加POST路由
     *
     * @param string $path
     * @param callable $callback
     * @return void
     */
    public static function post($path, $callback)
    {
        self::addRoute('POST', $path, $callback);
    }
    
    /**
     * 添加PUT路由
     *
     * @param string $path
     * @param callable $callback
     * @return void
     */
    public static function put($path, $callback)
    {
        self::addRoute('PUT', $path, $callback);
    }
    
    /**
     * 添加DELETE路由
     *
     * @param string $path
     * @param callable $callback
     * @return void
     */
    public static function delete($path, $callback)
    {
        self::addRoute('DELETE', $path, $callback);
    }
    
    /**
     * 添加路由
     *
     * @param string $method
     * @param string $path
     * @param callable $callback
     * @return void
     */
    private static function addRoute($method, $path, $handler)
    {
        // 确保路径以/开头
        $path = '/' . ltrim($path, '/');
        
        // 添加API前缀
        $path = self::$prefix . $path;
        
        Logger::info("Registering route: $method $path");
        
        // 如果在组内，添加当前组的中间件
        $middlewares = self::$currentGroupMiddlewares;
        
        self::$routes[] = [
            'method' => $method,
            'path' => $path,
            'handler' => $handler,
            'middlewares' => $middlewares
        ];
    }
    
    /**
     * 添加全局中间件
     *
     * @param array|callable $middleware
     * @return void
     */
    public static function use($middleware) 
    {
        if (is_array($middleware)) {
            array_push(self::$middlewares, ...$middleware);
        } else {
            self::$middlewares[] = $middleware;
        }
    }

    /**
     * 定义路由组
     *
     * @param array $options
     * @param callable $callback
     * @return void
     */
    public static function group(array $options, callable $callback)
    {
        // 保存当前的中间件状态
        $previousMiddlewares = self::$currentGroupMiddlewares;
        
        // 如果有新的中间件，添加到当前组
        if (isset($options['middleware'])) {
            if (is_array($options['middleware'])) {
                array_push(self::$currentGroupMiddlewares, ...$options['middleware']);
            } else {
                self::$currentGroupMiddlewares[] = $options['middleware'];
            }
        }
        
        // 执行路由组回调
        $callback();
        
        // 恢复之前的中间件状态
        self::$currentGroupMiddlewares = $previousMiddlewares;
    }

    /**
     * 分发请求
     *
     * @param string $method
     * @param string $path
     * @return void
     */
    public static function dispatch() 
    {
        $method = $_SERVER['REQUEST_METHOD'];
        $uri = parse_url($_SERVER['REQUEST_URI'], PHP_URL_PATH);
        
        Logger::info("Dispatching request: $method $uri");

        // 运行全局中间件
        foreach (self::$middlewares as $middleware) {
            if (is_array($middleware)) {
                $class = $middleware[0];
                $method = $middleware[1];
                if (method_exists($class, $method)) {
                    $instance = new $class();
                    $instance->$method();
                }
            }
        }

        // 查找匹配的路由
        foreach (self::$routes as $route) {
            $pattern = $route['path'];
            // 转换URL参数为正则表达式
            $pattern = preg_replace('/:[^\/]+/', '([^/]+)', $pattern);
            $pattern = '#^' . $pattern . '$#';

            if ($method === $route['method'] && preg_match($pattern, $uri, $matches)) {
                array_shift($matches); // 移除完整匹配部分

                try {
                    // 运行路由特定的中间件
                    if (isset($route['middlewares'])) {
                        foreach ($route['middlewares'] as $middleware) {
                            if (is_array($middleware)) {
                                $class = $middleware[0];
                                $method = $middleware[1];
                                if (method_exists($class, $method)) {
                                    $instance = new $class();
                                    $instance->$method();
                                }
                            }
                        }
                    }

                    // 处理回调
                    $handler = $route['handler'];
                    if (is_array($handler)) {
                        $controller = new $handler[0]();
                        $action = $handler[1];
                        $result = $controller->$action(...$matches);
                    } else {
                        $result = $handler(...$matches);
                    }

                    // 如果返回的是数组，转换为JSON
                    if (is_array($result)) {
                        echo json_encode($result);
                    } else {
                        echo $result;
                    }
                    return;
                } catch (\Exception $e) {
                    Logger::error("Route handler error: " . $e->getMessage());
                    http_response_code(500);
                    echo json_encode([
                        'success' => false,
                        'error' => '服务器内部错误',
                        'message' => $e->getMessage()
                    ]);
                    return;
                }
            }
        }

        // 没有找到匹配的路由
        Logger::warning("Route not found: $method $uri");
        http_response_code(404);
        echo json_encode([
            'success' => false,
            'error' => '请求的接口不存在'
        ]);
    }
}