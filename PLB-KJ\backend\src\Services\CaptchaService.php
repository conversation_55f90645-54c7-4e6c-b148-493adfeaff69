<?php

namespace App\Services;

class CaptchaService
{
    private static $instance = null;
    
    private function __construct() {}
    
    public static function getInstance()
    {
        if (self::$instance === null) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    /**
     * 生成验证码
     *
     * @param int $length 验证码长度
     * @return array 包含验证码文本和图片的数组
     */
    public function generateCaptcha($length = 4)
    {
        // 生成随机验证码
        $characters = '23456789ABCDEFGHJKLMNPQRSTUVWXYZ';
        $captchaText = '';
        for ($i = 0; $i < $length; $i++) {
            $captchaText .= $characters[rand(0, strlen($characters) - 1)];
        }
        
        // 创建验证码图片
        $width = 150;  // 增加宽度以提供更多空间
        $height = 50;  // 增加高度以提供更多空间
        $image = imagecreate($width, $height);
        
        // 设置颜色
        $backgroundColor = imagecolorallocate($image, 255, 255, 255);
        $textColor = imagecolorallocate($image, rand(0, 100), rand(0, 100), rand(0, 100));
        $lineColor = imagecolorallocate($image, rand(150, 200), rand(150, 200), rand(150, 200));
        
        // 填充背景色
        imagefill($image, 0, 0, $backgroundColor);
        
        // 添加干扰线
        for ($i = 0; $i < 5; $i++) {
            imageline(
                $image,
                rand(0, $width),
                rand(0, $height),
                rand(0, $width),
                rand(0, $height),
                $lineColor
            );
        }
        
        // 添加验证码文本，逐个字符绘制以更好地控制位置
        $charCount = strlen($captchaText);
        $fontSize = 5;
        $totalCharWidth = $charCount * 15; // 每个字符大约15像素宽
        $x = ($width - $totalCharWidth) / 2; // 居中对齐
        $y = ($height - 15) / 2 + 8; // 垂直居中
        
        // 为每个字符设置不同的位置和角度以增加安全性
        for ($i = 0; $i < $charCount; $i++) {
            $charX = $x + $i * 20 + rand(-2, 2); // 每个字符水平位置
            $charY = $y + rand(-5, 5); // 每个字符垂直位置有轻微变化
            
            // 为每个字符设置不同的颜色
            $charColor = imagecolorallocate($image, rand(0, 100), rand(0, 100), rand(0, 100));
            
            imagestring($image, $fontSize, $charX, $charY, $captchaText[$i], $charColor);
        }
        
        // 添加干扰点
        for ($i = 0; $i < 100; $i++) {
            imagesetpixel(
                $image,
                rand(0, $width),
                rand(0, $height),
                imagecolorallocate($image, rand(150, 255), rand(150, 255), rand(150, 255))
            );
        }
        
        // 将图片转换为base64编码
        ob_start();
        imagepng($image);
        $imageData = ob_get_contents();
        ob_end_clean();
        
        imagedestroy($image);
        
        $base64Image = 'data:image/png;base64,' . base64_encode($imageData);
        
        return [
            'text' => $captchaText,
            'image' => $base64Image
        ];
    }
    
    /**
     * 验证验证码
     *
     * @param string $input 用户输入的验证码
     * @param string $expected 正确的验证码
     * @return bool 验证结果
     */
    public function validateCaptcha($input, $expected)
    {
        return strtoupper($input) === strtoupper($expected);
    }
    
    /**
     * 生成验证码哈希值用于安全存储
     *
     * @param string $captcha 验证码文本
     * @return string 哈希值
     */
    public function hashCaptcha($captcha)
    {
        return hash('sha256', $captcha . getenv('APP_KEY', true) ?: 'default_key');
    }
}