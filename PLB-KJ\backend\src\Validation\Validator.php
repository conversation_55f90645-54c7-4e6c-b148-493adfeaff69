<?php

namespace App\Validation;

/**
 * 验证器类
 */
class Validator
{
    /**
     * 验证数据
     *
     * @param array $data 待验证的数据
     * @param array $rules 验证规则
     * @return array|bool 验证错误信息或true表示验证通过
     */
    public static function validate($data, $rules)
    {
        $errors = [];
        
        foreach ($rules as $field => $rule) {
            // 检查必填字段
            if (isset($rule['required']) && $rule['required'] && empty($data[$field])) {
                $errors[] = $rule['message'] ?? "{$field} 不能为空";
                continue;
            }
            
            // 如果字段为空且非必填，跳过其他验证
            if (!isset($data[$field]) || $data[$field] === '') {
                continue;
            }
            
            // 验证最小长度
            if (isset($rule['min_length']) && strlen($data[$field]) < $rule['min_length']) {
                $errors[] = $rule['message'] ?? "{$field} 长度不能少于 {$rule['min_length']} 个字符";
                continue;
            }
            
            // 验证最大长度
            if (isset($rule['max_length']) && strlen($data[$field]) > $rule['max_length']) {
                $errors[] = $rule['message'] ?? "{$field} 长度不能超过 {$rule['max_length']} 个字符";
                continue;
            }
            
            // 验证邮箱格式
            if (isset($rule['email']) && $rule['email']) {
                if (!filter_var($data[$field], FILTER_VALIDATE_EMAIL)) {
                    $errors[] = $rule['message'] ?? "{$field} 不是有效的邮箱地址";
                    continue;
                }
            }
            
            // 验证数值范围
            if (isset($rule['min']) && is_numeric($data[$field]) && $data[$field] < $rule['min']) {
                $errors[] = $rule['message'] ?? "{$field} 不能小于 {$rule['min']}";
                continue;
            }
            
            if (isset($rule['max']) && is_numeric($data[$field]) && $data[$field] > $rule['max']) {
                $errors[] = $rule['message'] ?? "{$field} 不能大于 {$rule['max']}";
                continue;
            }
        }
        
        return empty($errors) ? true : $errors;
    }
}