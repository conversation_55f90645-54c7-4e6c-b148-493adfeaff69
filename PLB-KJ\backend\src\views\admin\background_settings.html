<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>背景设置 - 管理员面板</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 0;
            background-color: #f5f5f5;
        }
        .header {
            background-color: #007cba;
            color: white;
            padding: 15px 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-wrap: wrap;
        }
        .header h1 {
            margin: 0;
            font-size: 1.5em;
            flex-grow: 1;
        }
        .nav-links {
            margin: 10px 0;
        }
        .nav-links a {
            color: white;
            text-decoration: none;
            margin-right: 15px;
        }
        .nav-links a:hover {
            text-decoration: underline;
        }
        .container {
            max-width: 800px;
            margin: 20px auto;
            padding: 0 20px;
        }
        .card {
            background-color: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            padding: 30px;
            margin-bottom: 20px;
        }
        .form-group {
            margin-bottom: 20px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #333;
        }
        input[type="text"] {
            width: 100%;
            padding: 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
            font-size: 16px;
        }
        .preview-container {
            margin: 20px 0;
            text-align: center;
        }
        .preview-image {
            max-width: 100%;
            max-height: 300px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        .buttons {
            display: flex;
            gap: 10px;
            margin-top: 20px;
        }
        button {
            padding: 12px 24px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
            flex: 1;
        }
        .save-btn {
            background-color: #007cba;
            color: white;
        }
        .save-btn:hover {
            background-color: #005a87;
        }
        .reset-btn {
            background-color: #dc3545;
            color: white;
        }
        .reset-btn:hover {
            background-color: #c82333;
        }
        .message {
            padding: 12px;
            border-radius: 4px;
            margin-bottom: 20px;
            text-align: center;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>背景设置</h1>
        <div class="nav-links">
            <a href="/admin/panel">返回面板</a>
        </div>
    </div>
    
    <div class="container">
        <div class="card">
            <h2>管理后台登录界面壁纸</h2>
            <p>设置管理后台登录界面的背景壁纸</p>
            
            <div id="message"></div>
            
            <form id="backgroundForm">
                <div class="form-group">
                    <label for="backgroundImage">背景图片URL:</label>
                    <input type="text" id="backgroundImage" name="background_image" placeholder="请输入背景图片的完整URL地址">
                </div>
                
                <div class="preview-container">
                    <h3>当前背景预览:</h3>
                    <div id="preview">
                        <!-- 预览图片将通过JavaScript动态加载 -->
                    </div>
                </div>
                
                <div class="buttons">
                    <button type="submit" class="save-btn">保存设置</button>
                    <button type="button" class="reset-btn" id="resetBtn">重置默认</button>
                </div>
            </form>
        </div>
        
        <div class="card">
            <h3>使用说明</h3>
            <ul>
                <li>输入有效的图片URL地址来设置自定义背景</li>
                <li>推荐使用高清图片以获得最佳显示效果</li>
                <li>点击"重置默认"可恢复系统默认背景</li>
                <li>背景图片将在下次登录时生效</li>
            </ul>
        </div>
    </div>

    <script>
        const API_BASE = '/api';
        const form = document.getElementById('backgroundForm');
        const resetBtn = document.getElementById('resetBtn');
        const previewContainer = document.getElementById('preview');
        const messageContainer = document.getElementById('message');
        
        // 显示消息
        function showMessage(message, type) {
            messageContainer.innerHTML = `<div class="message ${type}">${message}</div>`;
            setTimeout(() => {
                messageContainer.innerHTML = '';
            }, 5000);
        }
        
        // 更新预览
        function updatePreview(imageUrl) {
            previewContainer.innerHTML = '';
            if (imageUrl) {
                const img = document.createElement('img');
                img.src = imageUrl;
                img.className = 'preview-image';
                img.alt = '背景预览';
                img.onerror = function() {
                    previewContainer.innerHTML = '<p>图片加载失败</p>';
                };
                previewContainer.appendChild(img);
            } else {
                previewContainer.innerHTML = '<p>暂无自定义背景<br>系统将使用默认背景</p>';
            }
        }
        
        // 加载当前背景设置
        async function loadBackgroundSetting() {
            try {
                const response = await fetch(`${API_BASE}/settings/background`);
                const data = await response.json();
                
                if (data.background_image && data.background_image.success) {
                    document.getElementById('backgroundImage').value = data.background_image.data || '';
                    updatePreview(data.background_image.data || '');
                } else {
                    updatePreview('');
                }
            } catch (error) {
                console.error('加载背景设置失败:', error);
                showMessage('加载背景设置失败', 'error');
            }
        }
        
        // 保存背景设置
        async function saveBackgroundSetting(imageUrl) {
            try {
                const response = await fetch(`${API_BASE}/settings/background`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        background_image: imageUrl
                    })
                });
                
                const data = await response.json();
                
                if (data.status === 'success') {
                    showMessage('背景设置已保存', 'success');
                    updatePreview(imageUrl);
                } else {
                    showMessage('保存失败: ' + (data.message || '未知错误'), 'error');
                }
            } catch (error) {
                console.error('保存背景设置失败:', error);
                showMessage('保存背景设置失败', 'error');
            }
        }
        
        // 表单提交事件
        form.addEventListener('submit', async (e) => {
            e.preventDefault();
            const imageUrl = document.getElementById('backgroundImage').value;
            await saveBackgroundSetting(imageUrl);
        });
        
        // 重置按钮事件
        resetBtn.addEventListener('click', async () => {
            document.getElementById('backgroundImage').value = '';
            await saveBackgroundSetting('');
        });
        
        // 页面加载时获取当前设置
        document.addEventListener('DOMContentLoaded', loadBackgroundSetting);
    </script>
</body>
</html>