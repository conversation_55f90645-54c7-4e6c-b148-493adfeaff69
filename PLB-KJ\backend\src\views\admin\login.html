<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>管理员登录</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
        }
        .login-container {
            background-color: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            width: 100%;
            max-width: 400px;
        }
        h1 {
            text-align: center;
            color: #333;
            margin-bottom: 30px;
        }
        .form-group {
            margin-bottom: 20px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            color: #555;
        }
        input {
            width: 100%;
            padding: 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
            font-size: 16px;
        }
        button {
            width: 100%;
            background-color: #007cba;
            color: white;
            padding: 12px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
        }
        button:hover {
            background-color: #005a87;
        }
        .error {
            color: red;
            margin: 10px 0;
            text-align: center;
        }
        .success {
            color: green;
            margin: 10px 0;
            text-align: center;
        }
        
        /* 移动端优化 */
        @media (max-width: 480px) {
            body {
                padding: 10px;
            }
            .login-container {
                padding: 20px;
            }
        }
    </style>
</head>
<body>
    <div class="login-container">
        <h1>管理员登录</h1>
        <div id="message"></div>
        <form id="loginForm">
            <div class="form-group">
                <label for="username">用户名:</label>
                <input type="text" id="username" name="username" required>
            </div>
            
            <div class="form-group">
                <label for="password">密码:</label>
                <input type="password" id="password" name="password" required>
            </div>
            
            <button type="submit">登录</button>
        </form>
    </div>

    <script>
        const API_BASE = '/api';
        
        // 显示消息
        function showMessage(message, type) {
            const messageEl = document.getElementById('message');
            messageEl.textContent = message;
            messageEl.className = type;
            
            // 5秒后清除消息
            setTimeout(() => {
                messageEl.textContent = '';
                messageEl.className = '';
            }, 5000);
        }
        
        // 处理表单提交
        document.getElementById('loginForm').addEventListener('submit', async (e) => {
            e.preventDefault();
            
            const formData = new FormData(e.target);
            const loginData = Object.fromEntries(formData.entries());
            
            try {
                const response = await fetch(`${API_BASE}/admin/login`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(loginData)
                });
                
                console.log('Response Status:', response.status);
                console.log('Response Headers:', response.headers);
                
                // 检查响应内容类型
                const contentType = response.headers.get('content-type');
                console.log('Content-Type:', contentType);
                
                if (response.ok) {
                    // 根据内容类型处理响应
                    let result;
                    if (contentType && contentType.includes('application/json')) {
                        result = await response.json();
                    } else {
                        const text = await response.text();
                        console.log('Non-JSON response:', text);
                        throw new Error('服务器返回了非JSON响应: ' + text);
                    }
                    
                    showMessage('登录成功', 'success');
                    // 保存token到localStorage
                    localStorage.setItem('admin_token', result.token);
                    // 2秒后重定向到管理员面板
                    setTimeout(() => {
                        window.location.href = 'panel.html';
                    }, 2000);
                } else {
                    let result;
                    if (contentType && contentType.includes('application/json')) {
                        result = await response.json();
                    } else {
                        const text = await response.text();
                        console.log('Non-JSON response:', text);
                        throw new Error('服务器返回了非JSON响应: ' + text);
                    }
                    showMessage('登录失败: ' + result.error, 'error');
                }
            } catch (error) {
                showMessage('登录失败: ' + error.message, 'error');
            }
        });
    </script>
</body>
</html>