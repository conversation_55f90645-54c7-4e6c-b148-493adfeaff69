<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>管理员面板</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 0;
            background-color: #f5f5f5;
        }
        .header {
            background-color: #007cba;
            color: white;
            padding: 15px 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-wrap: wrap;
        }
        .header h1 {
            margin: 0;
            font-size: 1.5em;
            flex-grow: 1;
        }
        .logout-btn {
            background-color: #dc3545;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            white-space: nowrap;
        }
        .logout-btn:hover {
            background-color: #c82333;
        }
        .container {
            max-width: 1200px;
            margin: 20px auto;
            padding: 0 20px;
        }
        .dashboard {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        .card {
            background-color: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            padding: 20px;
        }
        .card h2 {
            margin-top: 0;
            color: #333;
        }
        .stats {
            display: flex;
            justify-content: space-between;
            flex-wrap: wrap;
        }
        .stat {
            text-align: center;
            flex: 1;
            min-width: 100px;
            margin: 10px;
        }
        .stat-value {
            font-size: 2em;
            font-weight: bold;
            color: #007cba;
        }
        .stat-label {
            color: #666;
        }
        .section {
            background-color: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            padding: 20px;
            margin-bottom: 20px;
            overflow-x: auto;
        }
        .section h2 {
            margin-top: 0;
            color: #333;
            border-bottom: 2px solid #007cba;
            padding-bottom: 10px;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            min-width: 600px;
        }
        th, td {
            border: 1px solid #ddd;
            padding: 12px;
            text-align: left;
        }
        th {
            background-color: #f2f2f2;
        }
        .action-btn {
            background-color: #007cba;
            color: white;
            border: none;
            padding: 6px 12px;
            border-radius: 4px;
            cursor: pointer;
        }
        .action-btn:hover {
            background-color: #005a87;
        }
        .action-btn.delete {
            background-color: #dc3545;
        }
        .action-btn.delete:hover {
            background-color: #c82333;
        }
        .settings-section {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }
        .setting-card {
            background-color: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            padding: 20px;
            text-align: center;
        }
        .setting-card h3 {
            margin-top: 0;
            color: #333;
        }
        .setting-card .icon {
            font-size: 48px;
            color: #007cba;
            margin-bottom: 15px;
        }
        .setting-card .btn {
            background-color: #007cba;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            text-decoration: none;
            display: inline-block;
            margin-top: 10px;
        }
        .setting-card .btn:hover {
            background-color: #005a87;
        }
        .message {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        /* 移动端优化 */
        @media (max-width: 768px) {
            .header {
                flex-direction: column;
                align-items: flex-start;
            }
            .header h1 {
                margin-bottom: 10px;
            }
            .container {
                padding: 0 10px;
            }
            .dashboard {
                grid-template-columns: 1fr;
            }
            .stat {
                min-width: auto;
                margin: 5px;
            }
            .stat-value {
                font-size: 1.5em;
            }
            th, td {
                padding: 8px;
                font-size: 14px;
            }
        }
        
        @media (max-width: 480px) {
            .header {
                padding: 10px;
            }
            .logout-btn {
                padding: 6px 12px;
                font-size: 14px;
            }
            .card, .section {
                padding: 15px;
            }
            .action-btn {
                padding: 4px 8px;
                font-size: 12px;
                margin-right: 2px;
            }
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>跨境电商管理系统 - 管理员面板</h1>
        <div>
            <span id="currentUser">加载中...</span>
            <button class="logout-btn" id="logoutBtn">退出登录</button>
        </div>
    </div>
    
    <div class="container">
        <div id="message"></div>
        
        <div class="dashboard">
            <div class="card">
                <h2>系统概览</h2>
                <div class="stats">
                    <div class="stat">
                        <div class="stat-value" id="userCount">0</div>
                        <div class="stat-label">用户数</div>
                    </div>
                    <div class="stat">
                        <div class="stat-value" id="productCount">0</div>
                        <div class="stat-label">商品数</div>
                    </div>
                    <div class="stat">
                        <div class="stat-value" id="orderCount">0</div>
                        <div class="stat-label">订单数</div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="section">
            <h2>系统管理</h2>
            <div class="settings-section">
                <div class="setting-card">
                    <div class="icon">👥</div>
                    <h3>用户管理</h3>
                    <p>管理系统用户和管理员账户</p>
                    <a href="/admin/users" class="btn">进入管理</a>
                </div>
                
                <div class="setting-card">
                    <div class="icon">🖼️</div>
                    <h3>界面设置</h3>
                    <p>自定义管理后台界面</p>
                    <a href="/admin/settings/background" class="btn">背景设置</a>
                </div>
                
                <div class="setting-card">
                    <div class="icon">📊</div>
                    <h3>数据统计</h3>
                    <p>查看系统运行统计数据</p>
                    <a href="/admin/stats" class="btn">查看统计</a>
                </div>
            </div>
        </div>
        
        <div class="section">
            <h2>最近活动</h2>
            <table>
                <thead>
                    <tr>
                        <th>时间</th>
                        <th>操作</th>
                        <th>用户</th>
                        <th>详情</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>2023-05-15 14:30</td>
                        <td>登录</td>
                        <td>admin</td>
                        <td>成功登录管理后台</td>
                    </tr>
                    <tr>
                        <td>2023-05-15 10:15</td>
                        <td>创建用户</td>
                        <td>admin</td>
                        <td>创建新用户 "testuser"</td>
                    </tr>
                    <tr>
                        <td>2023-05-14 16:45</td>
                        <td>修改设置</td>
                        <td>admin</td>
                        <td>更新系统配置</td>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>

    <script>
        const API_BASE = '/api';
        
        // 页面加载时检查是否已登录
        window.addEventListener('DOMContentLoaded', function() {
            const token = localStorage.getItem('admin_token');
            if (!token) {
                // 未登录，重定向到登录页面
                window.location.href = 'login.html';
            } else {
                // 解析token获取用户信息（简化实现）
                try {
                    const [userId, timestamp] = atob(token).split(':');
                    // 在实际应用中，应该通过API获取用户详细信息
                    // 这里我们简化处理，只显示用户ID
                    document.getElementById('currentUser').textContent = `管理员 (ID: ${userId})`;
                } catch (e) {
                    // token解析失败，清除并重定向
                    localStorage.removeItem('admin_token');
                    window.location.href = 'login.html';
                }
            }
        });
        
        // 显示消息
        function showMessage(message, type) {
            const messageEl = document.getElementById('message');
            messageEl.textContent = message;
            messageEl.className = 'message ' + type;
            
            // 5秒后清除消息
            setTimeout(() => {
                messageEl.textContent = '';
                messageEl.className = '';
            }, 5000);
        }
        
        // 加载统计数据
        async function loadStats() {
            try {
                const response = await fetch(`${API_BASE}/stats`);
                const stats = await response.json();
                
                // 更新统计数字
                document.getElementById('userCount').textContent = stats.userCount;
                document.getElementById('orderCount').textContent = stats.orderCount;
                document.getElementById('productCount').textContent = stats.productCount;
            } catch (error) {
                showMessage('加载统计数据失败: ' + error.message, 'error');
            }
        }
        
        // 加载用户列表
        async function loadUsers() {
            try {
                const response = await fetch(`${API_BASE}/users`);
                const users = await response.json();
                
                const tbody = document.querySelector('#usersTable tbody');
                tbody.innerHTML = '';
                
                users.forEach(user => {
                    const row = document.createElement('tr');
                    row.innerHTML = `
                        <td>${user.id}</td>
                        <td>${user.username}</td>
                        <td>${user.email}</td>
                        <td>${user.first_name} ${user.last_name}</td>
                        <td>${user.role}</td>
                        <td>${user.status == 1 ? '激活' : '禁用'}</td>
                        <td>${user.created_at}</td>
                        <td>
                            <button class="action-btn" onclick="editUser(${user.id})">编辑</button>
                            <button class="action-btn delete-btn" onclick="deleteUser(${user.id})">删除</button>
                        </td>
                    `;
                    tbody.appendChild(row);
                });
            } catch (error) {
                showMessage('加载用户列表失败: ' + error.message, 'error');
            }
        }
        
        // 编辑用户（占位函数）
        function editUser(id) {
            showMessage(`编辑用户 ${id} 功能尚未实现`, 'error');
        }
        
        // 删除用户
        async function deleteUser(id) {
            if (!confirm('确定要删除这个用户吗？')) {
                return;
            }
            
            try {
                const response = await fetch(`${API_BASE}/users/${id}`, {
                    method: 'DELETE'
                });
                
                if (response.ok) {
                    showMessage('用户删除成功', 'success');
                    loadUsers(); // 重新加载用户列表
                } else {
                    const result = await response.json();
                    showMessage('删除用户失败: ' + result.error, 'error');
                }
            } catch (error) {
                showMessage('删除用户失败: ' + error.message, 'error');
            }
        }
        
        // 退出登录
        document.getElementById('logoutBtn').addEventListener('click', () => {
            // 清除token（如果存储在localStorage中）
            // localStorage.removeItem('admin_token');
            // 重定向到登录页面
            window.location.href = 'login.html';
        });
        
        // 页面加载时自动加载统计数据和用户列表
        loadStats();
        loadUsers();
    </script>
</body>
</html>