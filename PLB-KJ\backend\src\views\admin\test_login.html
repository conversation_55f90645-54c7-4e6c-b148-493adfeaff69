<!DOCTYPE html>
<html>
<head>
    <title>测试登录</title>
</head>
<body>
    <h1>测试管理员登录</h1>
    <form id="loginForm">
        <div>
            <label for="username">用户名:</label>
            <input type="text" id="username" name="username" value="admin" required>
        </div>
        <div>
            <label for="password">密码:</label>
            <input type="password" id="password" name="password" value="123456" required>
        </div>
        <button type="submit">登录</button>
    </form>
    <div id="result"></div>

    <script>
        document.getElementById('loginForm').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const formData = new FormData(this);
            const data = Object.fromEntries(formData);
            
            try {
                const response = await fetch('/api/admin/login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(data)
                });
                
                const result = await response.text();
                document.getElementById('result').innerHTML = `
                    <h2>响应信息</h2>
                    <p>状态码: ${response.status}</p>
                    <p>响应内容: ${result}</p>
                    <p>是否为JSON: ${isValidJSON(result)}</p>
                `;
            } catch (error) {
                document.getElementById('result').innerHTML = `<p>错误: ${error.message}</p>`;
            }
        });
        
        function isValidJSON(str) {
            try {
                JSON.parse(str);
                return true;
            } catch (e) {
                return false;
            }
        }
    </script>
</body>
</html>