<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>用户管理</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0 auto;
            padding: 20px;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
        }
        input, select {
            width: 100%;
            padding: 8px;
            box-sizing: border-box;
        }
        button {
            background-color: #007cba;
            color: white;
            padding: 10px 20px;
            border: none;
            cursor: pointer;
        }
        button:hover {
            background-color: #005a87;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
            overflow-x: auto;
            display: block;
        }
        th, td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
            white-space: nowrap;
        }
        th {
            background-color: #f2f2f2;
        }
        .error {
            color: red;
            margin: 10px 0;
        }
        .success {
            color: green;
            margin: 10px 0;
        }
        
        /* 移动端优化 */
        @media (max-width: 768px) {
            body {
                padding: 10px;
            }
            .container {
                max-width: 100%;
            }
            h1 {
                font-size: 1.5em;
            }
            h2 {
                font-size: 1.3em;
            }
            th, td {
                padding: 6px;
                font-size: 14px;
            }
        }
        
        @media (max-width: 480px) {
            button {
                padding: 8px 16px;
                font-size: 14px;
            }
            th, td {
                padding: 4px;
                font-size: 12px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>用户管理</h1>
        
        <div id="message"></div>
        
        <form id="userForm">
            <div class="form-group">
                <label for="username">用户名:</label>
                <input type="text" id="username" name="username" required>
            </div>
            
            <div class="form-group">
                <label for="email">邮箱:</label>
                <input type="email" id="email" name="email" required>
            </div>
            
            <div class="form-group">
                <label for="password">密码:</label>
                <input type="password" id="password" name="password" required>
            </div>
            
            <div class="form-group">
                <label for="firstName">名字:</label>
                <input type="text" id="firstName" name="first_name">
            </div>
            
            <div class="form-group">
                <label for="lastName">姓氏:</label>
                <input type="text" id="lastName" name="last_name">
            </div>
            
            <div class="form-group">
                <label for="phone">电话:</label>
                <input type="text" id="phone" name="phone">
            </div>
            
            <div class="form-group">
                <label for="role">角色:</label>
                <select id="role" name="role">
                    <option value="staff">员工</option>
                    <option value="manager">经理</option>
                    <option value="admin">管理员</option>
                </select>
            </div>
            
            <button type="submit">创建用户</button>
        </form>
        
        <h2>用户列表</h2>
        <button id="loadUsers">加载用户</button>
        <table id="usersTable">
            <thead>
                <tr>
                    <th>ID</th>
                    <th>用户名</th>
                    <th>邮箱</th>
                    <th>姓名</th>
                    <th>角色</th>
                    <th>状态</th>
                    <th>创建时间</th>
                    <th>操作</th>
                </tr>
            </thead>
            <tbody>
            </tbody>
        </table>
    </div>

    <script>
        const API_BASE = '/api';
        
        // 显示消息
        function showMessage(message, type) {
            const messageEl = document.getElementById('message');
            messageEl.textContent = message;
            messageEl.className = type;
            
            // 3秒后清除消息
            setTimeout(() => {
                messageEl.textContent = '';
                messageEl.className = '';
            }, 3000);
        }
        
        // 加载用户列表
        async function loadUsers() {
            try {
                const response = await fetch(`${API_BASE}/users`);
                const users = await response.json();
                
                const tbody = document.querySelector('#usersTable tbody');
                tbody.innerHTML = '';
                
                users.forEach(user => {
                    const row = document.createElement('tr');
                    row.innerHTML = `
                        <td>${user.id}</td>
                        <td>${user.username}</td>
                        <td>${user.email}</td>
                        <td>${user.first_name} ${user.last_name}</td>
                        <td>${user.role}</td>
                        <td>${user.status == 1 ? '激活' : '禁用'}</td>
                        <td>${user.created_at}</td>
                        <td>
                            <button onclick="deleteUser(${user.id})">删除</button>
                        </td>
                    `;
                    tbody.appendChild(row);
                });
            } catch (error) {
                showMessage('加载用户列表失败: ' + error.message, 'error');
            }
        }
        
        // 删除用户
        async function deleteUser(id) {
            if (!confirm('确定要删除这个用户吗？')) {
                return;
            }
            
            try {
                const response = await fetch(`${API_BASE}/users/${id}`, {
                    method: 'DELETE'
                });
                
                if (response.ok) {
                    showMessage('用户删除成功', 'success');
                    loadUsers(); // 重新加载用户列表
                } else {
                    const result = await response.json();
                    showMessage('删除用户失败: ' + result.error, 'error');
                }
            } catch (error) {
                showMessage('删除用户失败: ' + error.message, 'error');
            }
        }
        
        // 处理表单提交
        document.getElementById('userForm').addEventListener('submit', async (e) => {
            e.preventDefault();
            
            const formData = new FormData(e.target);
            const userData = Object.fromEntries(formData.entries());
            
            try {
                const response = await fetch(`${API_BASE}/users`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(userData)
                });
                
                if (response.ok) {
                    const user = await response.json();
                    showMessage('用户创建成功', 'success');
                    e.target.reset(); // 重置表单
                    loadUsers(); // 重新加载用户列表
                } else {
                    const result = await response.json();
                    showMessage('创建用户失败: ' + result.error, 'error');
                }
            } catch (error) {
                showMessage('创建用户失败: ' + error.message, 'error');
            }
        });
        
        // 绑定加载用户按钮
        document.getElementById('loadUsers').addEventListener('click', loadUsers);
        
        // 页面加载时自动加载用户列表
        loadUsers();
    </script>
</body>
</html>