<?php

// 测试管理员登录API

function testApi($url, $data = null, $method = 'GET') {
    $ch = curl_init();
    
    curl_setopt_array($ch, [
        CURLOPT_URL => $url,
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_HTTPHEADER => [
            'Content-Type: application/json'
        ]
    ]);
    
    if ($method === 'POST') {
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
    }
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);
    
    return [
        'code' => $httpCode,
        'body' => $response
    ];
}

// 首先创建一个管理员用户
// 注意：这需要确保API服务器正在运行
echo "创建管理员用户:\n";
$adminData = [
    'username' => 'admin',
    'email' => '<EMAIL>',
    'password_hash' => password_hash('admin123', PASSWORD_DEFAULT),
    'first_name' => 'Admin',
    'last_name' => 'User',
    'role' => 'admin'
];

$result = testApi('http://localhost:8000/api/users', $adminData, 'POST');
echo "HTTP状态码: " . $result['code'] . "\n";
echo "响应内容: " . $result['body'] . "\n";

// 测试管理员登录
echo "\n测试管理员登录:\n";
$loginData = [
    'username' => 'admin',
    'password' => 'admin123'
];

$result = testApi('http://localhost:8000/api/admin/login', $loginData, 'POST');

echo "HTTP状态码: " . $result['code'] . "\n";
echo "响应内容: " . $result['body'] . "\n";

// 测试使用错误的凭据登录
echo "\n测试使用错误凭据登录:\n";
$invalidLoginData = [
    'username' => 'admin',
    'password' => 'wrongpassword'
];

$result = testApi('http://localhost:8000/api/admin/login', $invalidLoginData, 'POST');

echo "HTTP状态码: " . $result['code'] . "\n";
echo "响应内容: " . $result['body'] . "\n";