<?php

// 简单的API测试脚本

// 测试基础路径
$baseUrl = 'http://localhost:8000';

// 测试获取所有用户
echo "测试获取所有用户:\n";
$ch = curl_init("{$baseUrl}/api/users");
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
$response = curl_exec($ch);
$httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
curl_close($ch);

echo "HTTP状态码: {$httpCode}\n";
echo "响应内容: {$response}\n\n";

// 测试创建用户
echo "测试创建用户:\n";
$userData = [
    'username' => 'testuser2',
    'email' => '<EMAIL>',
    'password_hash' => '123456', // 符合最少6个字符的要求
    'first_name' => 'Test',
    'last_name' => 'User',
    'role' => 'staff'
];

$ch = curl_init("{$baseUrl}/api/users");
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_POST, true);
curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($userData));
curl_setopt($ch, CURLOPT_HTTPHEADER, [
    'Content-Type: application/json'
]);

$response = curl_exec($ch);
$httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
curl_close($ch);

echo "HTTP状态码: {$httpCode}\n";
echo "响应内容: {$response}\n\n";