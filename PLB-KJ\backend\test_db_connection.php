<?php

require_once __DIR__ . '/vendor/autoload.php';

use App\Helpers\Logger;

try {
    // 加载数据库配置
    $dbConfig = require __DIR__ . '/src/Config/database.php';
    
    // 创建PDO连接
    $dsn = "mysql:host={$dbConfig['host']};port={$dbConfig['port']};dbname={$dbConfig['database']};charset={$dbConfig['charset']}";
    $pdo = new PDO($dsn, $dbConfig['username'], $dbConfig['password'], [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
        PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
    ]);
    
    echo "数据库连接成功!\n";
    echo "服务器信息: " . $pdo->getAttribute(PDO::ATTR_SERVER_VERSION) . "\n";
    
    // 测试查询
    $stmt = $pdo->query("SHOW TABLES");
    $tables = $stmt->fetchAll();
    
    echo "\n数据库表:\n";
    foreach ($tables as $table) {
        echo "- " . reset($table) . "\n";
    }
    
} catch (PDOException $e) {
    echo "数据库连接失败: " . $e->getMessage() . "\n";
    Logger::error("数据库连接失败: " . $e->getMessage());
}