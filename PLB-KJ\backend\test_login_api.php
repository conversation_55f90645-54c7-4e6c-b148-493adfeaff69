<?php

// 测试登录API的脚本

$curl = curl_init();

// 设置请求参数
curl_setopt_array($curl, [
    CURLOPT_URL => 'http://localhost:8000/api/admin/login',
    CURLOPT_RETURNTRANSFER => true,
    CURLOPT_ENCODING => '',
    CURLOPT_MAXREDIRS => 10,
    CURLOPT_TIMEOUT => 0,
    CURLOPT_FOLLOWLOCATION => true,
    CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
    CURLOPT_CUSTOMREQUEST => 'POST',
    CURLOPT_POSTFIELDS => '{"username": "admin", "password": "admin123"}',
    CURLOPT_HTTPHEADER => [
        'Content-Type: application/json'
    ],
]);

// 执行请求
$response = curl_exec($curl);

// 获取HTTP状态码
$httpCode = curl_getinfo($curl, CURLINFO_HTTP_CODE);

// 关闭curl
curl_close($curl);

// 输出结果
echo "HTTP Status Code: " . $httpCode . "\n";
echo "Response: " . $response . "\n";

// 尝试解析JSON响应
$json = json_decode($response, true);
if (json_last_error() === JSON_ERROR_NONE) {
    echo "Valid JSON response\n";
    print_r($json);
} else {
    echo "Invalid JSON response: " . json_last_error_msg() . "\n";
    echo "Response content: " . $response . "\n";
}