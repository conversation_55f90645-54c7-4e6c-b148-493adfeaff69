<?php

require_once 'vendor/autoload.php';

use App\Routing\Router;
use App\Controllers\UserController;

// Define a test route
Router::get('/api/users', [UserController::class, 'getAll']);

// Try to dispatch a request
try {
    echo "Testing route dispatching...\n";
    
    // Test with a defined route
    echo "Testing /api/users route...\n";
    Router::dispatch('GET', '/api/users');
    
} catch (Exception $e) {
    echo "Exception: " . $e->getMessage() . "\n";
    echo "Exception trace: " . $e->getTraceAsString() . "\n";
}