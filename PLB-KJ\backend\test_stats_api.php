<?php

// 测试统计数据API
function testStatsApi() {
    $url = 'http://localhost:8001/api/stats';
    
    // 初始化cURL
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    
    // 执行请求
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    
    // 关闭cURL
    curl_close($ch);
    
    // 输出结果
    echo "HTTP状态码: " . $httpCode . "\n";
    echo "响应内容: " . $response . "\n";
    
    // 解析JSON响应
    $data = json_decode($response, true);
    
    if ($httpCode == 200 && $data) {
        echo "统计数据获取成功:\n";
        echo "用户数: " . $data['userCount'] . "\n";
        echo "订单数: " . $data['orderCount'] . "\n";
        echo "商品数: " . $data['productCount'] . "\n";
    } else {
        echo "获取统计数据失败\n";
    }
}

testStatsApi();