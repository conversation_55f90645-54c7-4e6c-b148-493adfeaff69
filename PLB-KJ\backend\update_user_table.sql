-- 更新用户表结构以支持用户管理功能

-- 1. 添加last_login字段
ALTER TABLE plb_kj_users 
ADD COLUMN last_login TIMESTAMP NULL AFTER updated_at;

-- 2. 修改role枚举值，添加user和customer角色
ALTER TABLE plb_kj_users 
MODIFY COLUMN role ENUM('admin', 'manager', 'staff', 'user', 'customer') DEFAULT 'user';

-- 3. 添加索引以提高查询性能
CREATE INDEX idx_users_role ON plb_kj_users(role);
CREATE INDEX idx_users_status ON plb_kj_users(status);
CREATE INDEX idx_users_email ON plb_kj_users(email);
CREATE INDEX idx_users_username ON plb_kj_users(username);
CREATE INDEX idx_users_created_at ON plb_kj_users(created_at);
CREATE INDEX idx_users_last_login ON plb_kj_users(last_login);

-- 4. 插入一些测试用户数据（如果不存在）
INSERT IGNORE INTO plb_kj_users (username, email, password_hash, first_name, last_name, role, status, last_login) VALUES
('admin', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', '管理员', '用户', 'admin', 1, NOW()),
('zhangsan', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', '张', '三', 'user', 1, DATE_SUB(NOW(), INTERVAL 1 DAY)),
('lisi', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', '李', '四', 'user', 1, DATE_SUB(NOW(), INTERVAL 2 DAY)),
('wangwu', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', '王', '五', 'customer', 0, DATE_SUB(NOW(), INTERVAL 3 DAY)),
('zhaoliu', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', '赵', '六', 'user', 1, DATE_SUB(NOW(), INTERVAL 1 HOUR)),
('manager1', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', '经理', '一', 'manager', 1, DATE_SUB(NOW(), INTERVAL 5 HOUR)),
('customer1', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', '客户', '一', 'customer', 1, DATE_SUB(NOW(), INTERVAL 2 HOUR)),
('staff1', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', '员工', '一', 'staff', 1, DATE_SUB(NOW(), INTERVAL 3 HOUR));

-- 5. 更新现有用户的last_login字段（如果为NULL）
UPDATE plb_kj_users 
SET last_login = DATE_SUB(NOW(), INTERVAL FLOOR(RAND() * 30) DAY)
WHERE last_login IS NULL;

-- 6. 创建用户活动日志表（可选，用于记录用户操作）
CREATE TABLE IF NOT EXISTS plb_kj_user_activity_logs (
  id INT PRIMARY KEY AUTO_INCREMENT,
  user_id INT NOT NULL,
  action VARCHAR(100) NOT NULL,
  description TEXT,
  ip_address VARCHAR(45),
  user_agent TEXT,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (user_id) REFERENCES plb_kj_users(id) ON DELETE CASCADE,
  INDEX idx_user_activity_user_id (user_id),
  INDEX idx_user_activity_action (action),
  INDEX idx_user_activity_created_at (created_at)
);

-- 7. 创建用户会话表（可选，用于管理用户登录会话）
CREATE TABLE IF NOT EXISTS plb_kj_user_sessions (
  id INT PRIMARY KEY AUTO_INCREMENT,
  user_id INT NOT NULL,
  session_token VARCHAR(255) NOT NULL UNIQUE,
  ip_address VARCHAR(45),
  user_agent TEXT,
  expires_at TIMESTAMP NOT NULL,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  FOREIGN KEY (user_id) REFERENCES plb_kj_users(id) ON DELETE CASCADE,
  INDEX idx_user_sessions_user_id (user_id),
  INDEX idx_user_sessions_token (session_token),
  INDEX idx_user_sessions_expires_at (expires_at)
);
