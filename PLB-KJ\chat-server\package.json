{"name": "plb-kj-chat-server", "version": "1.0.0", "description": "跨境电商管理系统实时聊天服务器", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "test": "jest"}, "keywords": ["websocket", "chat", "socket.io", "real-time"], "author": "PLB-KJ Team", "license": "MIT", "dependencies": {"socket.io": "^4.7.2", "express": "^4.18.2", "cors": "^2.8.5", "mysql2": "^3.6.0", "redis": "^4.6.7", "jsonwebtoken": "^9.0.2", "bcryptjs": "^2.4.3", "multer": "^1.4.5-lts.1", "uuid": "^9.0.0", "moment": "^2.29.4", "dotenv": "^16.3.1"}, "devDependencies": {"nodemon": "^3.0.1", "jest": "^29.6.2"}, "engines": {"node": ">=16.0.0"}}