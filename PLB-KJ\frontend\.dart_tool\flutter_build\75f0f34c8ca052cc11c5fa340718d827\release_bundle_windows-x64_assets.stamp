{"inputs": ["D:\\flutter\\packages\\flutter_tools\\lib\\src\\build_system\\targets\\windows.dart", "D:\\PLB-Links\\PLB-KJ\\frontend\\pubspec.yaml", "D:\\flutter\\packages\\flutter_tools\\lib\\src\\build_system\\targets\\icon_tree_shaker.dart", "D:\\flutter\\bin\\cache\\engine.stamp", "D:\\flutter\\bin\\cache\\engine.stamp", "D:\\PLB-Links\\PLB-KJ\\frontend\\pubspec.yaml", "D:\\PLB-Links\\PLB-KJ\\frontend\\assets\\images\\.gitkeep", "D:\\PLB-Links\\PLB-KJ\\frontend\\assets\\icons\\.gitkeep", "D:\\PLB-Links\\PLB-KJ\\frontend\\assets\\fonts\\Roboto-Regular.ttf", "D:\\PLB-Links\\PLB-KJ\\frontend\\assets\\fonts\\Roboto-Bold.ttf", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\window_manager-0.3.9\\images\\ic_chrome_close.png", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\window_manager-0.3.9\\images\\ic_chrome_maximize.png", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\window_manager-0.3.9\\images\\ic_chrome_minimize.png", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\window_manager-0.3.9\\images\\ic_chrome_unmaximize.png", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\cupertino_icons-1.0.8\\assets\\CupertinoIcons.ttf", "D:\\flutter\\bin\\cache\\artifacts\\material_fonts\\MaterialIcons-Regular.otf", "D:\\flutter\\packages\\flutter\\lib\\src\\material\\shaders\\ink_sparkle.frag", "D:\\PLB-Links\\PLB-KJ\\frontend\\.dart_tool\\flutter_build\\75f0f34c8ca052cc11c5fa340718d827\\native_assets.json", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\args-2.7.0\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\async-2.13.0\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\boolean_selector-2.1.2\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\characters-1.4.0\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\clock-1.1.2\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\collection-1.19.1\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\cookie_jar-4.0.8\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\cupertino_icons-1.0.8\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\dio-5.8.0+1\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\dio_cookie_manager-3.2.0\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\dio_web_adapter-2.1.1\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\fake_async-1.3.3\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\ffi-2.1.4\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\file-7.0.1\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\flutter_lints-2.0.3\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\flutter_svg-2.2.0\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\http-1.4.0\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\http_parser-4.1.2\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\intl-0.18.1\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\leak_tracker-11.0.1\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\leak_tracker_flutter_testing-3.0.10\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\leak_tracker_testing-3.0.2\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\lints-2.1.1\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\matcher-0.12.17\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\material_color_utilities-0.11.1\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\meta-1.16.0\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\nested-1.0.0\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\path-1.9.1\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\path_parsing-1.1.0\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\path_provider_linux-2.2.1\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\path_provider_platform_interface-2.1.2\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\path_provider_windows-2.3.0\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\petitparser-6.1.0\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\platform-3.1.6\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\plugin_platform_interface-2.1.8\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\provider-6.1.5\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\screen_retriever-0.1.9\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\shared_preferences-2.5.3\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\shared_preferences_android-2.4.10\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\shared_preferences_foundation-2.5.4\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\shared_preferences_linux-2.4.1\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\shared_preferences_platform_interface-2.4.1\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\shared_preferences_web-2.4.3\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\shared_preferences_windows-2.4.1\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\source_span-1.10.1\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\stack_trace-1.12.1\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\stream_channel-2.1.4\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\string_scanner-1.4.1\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\term_glyph-1.2.2\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\test_api-0.7.6\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\typed_data-1.4.0\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\universal_io-2.2.2\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\vector_graphics-1.1.19\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\vector_graphics_codec-1.1.13\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\vector_graphics_compiler-1.1.17\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\vector_math-2.2.0\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\vm_service-15.0.2\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\web-1.1.1\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\window_manager-0.3.9\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\xdg_directories-1.1.0\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\xml-6.5.0\\LICENSE", "D:\\flutter\\bin\\cache\\pkg\\sky_engine\\LICENSE", "D:\\flutter\\packages\\flutter\\LICENSE", "D:\\PLB-Links\\PLB-KJ\\frontend\\DOES_NOT_EXIST_RERUN_FOR_WILDCARD520238179"], "outputs": ["D:\\PLB-Links\\PLB-KJ\\frontend\\build\\flutter_assets\\assets\\images\\.gitkeep", "D:\\PLB-Links\\PLB-KJ\\frontend\\build\\flutter_assets\\assets\\icons\\.gitkeep", "D:\\PLB-Links\\PLB-KJ\\frontend\\build\\flutter_assets\\assets\\fonts\\Roboto-Regular.ttf", "D:\\PLB-Links\\PLB-KJ\\frontend\\build\\flutter_assets\\assets\\fonts\\Roboto-Bold.ttf", "D:\\PLB-Links\\PLB-KJ\\frontend\\build\\flutter_assets\\packages\\window_manager\\images\\ic_chrome_close.png", "D:\\PLB-Links\\PLB-KJ\\frontend\\build\\flutter_assets\\packages\\window_manager\\images\\ic_chrome_maximize.png", "D:\\PLB-Links\\PLB-KJ\\frontend\\build\\flutter_assets\\packages\\window_manager\\images\\ic_chrome_minimize.png", "D:\\PLB-Links\\PLB-KJ\\frontend\\build\\flutter_assets\\packages\\window_manager\\images\\ic_chrome_unmaximize.png", "D:\\PLB-Links\\PLB-KJ\\frontend\\build\\flutter_assets\\packages\\cupertino_icons\\assets\\CupertinoIcons.ttf", "D:\\PLB-Links\\PLB-KJ\\frontend\\build\\flutter_assets\\fonts\\MaterialIcons-Regular.otf", "D:\\PLB-Links\\PLB-KJ\\frontend\\build\\flutter_assets\\shaders\\ink_sparkle.frag", "D:\\PLB-Links\\PLB-KJ\\frontend\\build\\flutter_assets\\AssetManifest.json", "D:\\PLB-Links\\PLB-KJ\\frontend\\build\\flutter_assets\\AssetManifest.bin", "D:\\PLB-Links\\PLB-KJ\\frontend\\build\\flutter_assets\\FontManifest.json", "D:\\PLB-Links\\PLB-KJ\\frontend\\build\\flutter_assets\\NOTICES.Z", "D:\\PLB-Links\\PLB-KJ\\frontend\\build\\flutter_assets\\NativeAssetsManifest.json"]}