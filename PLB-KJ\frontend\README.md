# PLB-KJ Flutter Frontend

跨境电商管理系统的Flutter前端应用。

## 目录结构

- `lib/` - 主要的Flutter应用代码
  - `core/` - 核心功能模块
  - `features/` - 应用功能模块
  - `shared/` - 共享组件和资源
  - `main.dart` - 应用入口点

## 功能模块

1. 用户认证
2. 仪表盘
3. 用户管理
4. 媒体管理

## 开发环境

- Flutter 3.x
- Dart 2.19+

## 运行项目

```bash
flutter pub get
flutter run
```

## 构建桌面客户端

### Windows

```bash
flutter build windows
```

构建后的可执行文件位于: `build\windows\x64\runner\Release\plb_kj_admin.exe`

你也可以使用提供的构建脚本:

```bash
build_desktop.bat
```

### Linux

```bash
flutter build linux
```

### macOS

```bash
flutter build macos
```

对于Linux和macOS平台，你也可以使用提供的构建脚本:

```bash
./build_desktop.sh
```