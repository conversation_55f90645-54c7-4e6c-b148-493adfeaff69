#!/bin/bash

# 构建Linux桌面客户端
echo "Building Linux desktop client..."
flutter build linux
if [ $? -ne 0 ]; then
    echo "Error building Linux desktop client"
    exit 1
fi

echo ""
echo "Linux desktop client built successfully!"
echo "Executable location: build/linux/x64/release/bundle/plb_kj_admin"

echo ""
echo "Building macOS desktop client..."
flutter build macos
if [ $? -ne 0 ]; then
    echo "Error building macOS desktop client"
    exit 1
fi

echo ""
echo "macOS desktop client built successfully!"
echo "App location: build/macos/Build/Products/Release/plb_kj_admin.app"

echo ""
echo "All desktop clients built successfully!"
echo "Press any key to continue..."
read -n 1 -s