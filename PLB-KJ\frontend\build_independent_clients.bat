@echo off
echo ========================================
echo 构建独立客户端应用
echo ========================================

echo.
echo 1. 构建管理端客户端...
cd plb_kj_admin
flutter build windows --release
if %ERRORLEVEL% EQU 0 (
    echo ✅ 管理端构建成功: plb_kj_admin.exe
    copy "build\windows\x64\runner\Release\plb_kj_admin.exe" "..\plb_kj_admin_manager.exe"
) else (
    echo ❌ 管理端构建失败
)

echo.
echo 2. 构建用户端客户端...
cd ..\plb_kj_user

REM 创建简单的main.dart用于测试
echo import 'package:flutter/material.dart'; > lib\main.dart
echo. >> lib\main.dart
echo void main() { >> lib\main.dart
echo   runApp(MaterialApp( >> lib\main.dart
echo     home: Scaffold( >> lib\main.dart
echo       appBar: AppBar(title: Text('用户端')), >> lib\main.dart
echo       body: Center(child: Text('用户端应用')), >> lib\main.dart
echo     ), >> lib\main.dart
echo   )); >> lib\main.dart
echo } >> lib\main.dart

flutter build windows --release
if %ERRORLEVEL% EQU 0 (
    echo ✅ 用户端构建成功: plb_kj_user.exe
    copy "build\windows\x64\runner\Release\plb_kj_user.exe" "..\plb_kj_user_center.exe"
) else (
    echo ❌ 用户端构建失败
)

cd ..
echo.
echo ========================================
echo 构建完成！
echo ========================================
echo 管理端应用: plb_kj_admin_manager.exe
echo 用户端应用: plb_kj_user_center.exe
echo ========================================
pause
