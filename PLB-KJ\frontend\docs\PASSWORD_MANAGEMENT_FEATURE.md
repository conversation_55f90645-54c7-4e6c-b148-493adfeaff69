# 用户密码管理功能实现

## 🎯 功能概述

为用户管理系统添加了完整的密码管理功能，包括编辑用户时的密码修改选项和独立的密码重置功能。

## ✨ 新增功能

### 1. 编辑用户时修改密码
- **可选密码修改**: 在编辑用户对话框中添加"修改密码"复选框
- **条件显示**: 只有勾选"修改密码"时才显示密码输入框
- **密码验证**: 包含长度验证（最少6位）和必填验证
- **分离更新**: 用户信息和密码分别更新，提高安全性

### 2. 独立密码重置功能
- **专用对话框**: 独立的密码重置对话框
- **密码确认**: 要求输入两次密码并验证一致性
- **密码可见性**: 支持显示/隐藏密码
- **安全提示**: 显示重置密码的影响提示

### 3. 用户界面增强
- **新增操作按钮**: 在用户列表中添加"重置密码"按钮
- **图标设计**: 使用lock_reset图标，橙色主题
- **操作反馈**: 成功/失败的友好提示信息

## 🏗️ 技术实现

### 1. EditUserDialog增强

#### 新增状态变量
```dart
class _EditUserDialogState extends State<EditUserDialog> {
  late final TextEditingController _passwordController;
  bool _changePassword = false;  // 控制是否修改密码
  // ... 其他变量
}
```

#### 密码修改选项UI
```dart
// 修改密码复选框
Row(
  children: [
    Checkbox(
      value: _changePassword,
      onChanged: (value) {
        setState(() {
          _changePassword = value ?? false;
          if (!_changePassword) {
            _passwordController.clear();
          }
        });
      },
    ),
    const Text('修改密码'),
  ],
),

// 条件显示的密码输入框
if (_changePassword) ...[
  const SizedBox(height: 16),
  TextFormField(
    controller: _passwordController,
    decoration: const InputDecoration(
      labelText: '新密码',
      border: OutlineInputBorder(),
      helperText: '留空则不修改密码',
    ),
    obscureText: true,
    validator: (value) {
      if (_changePassword && (value == null || value.isEmpty)) {
        return '请输入新密码';
      }
      if (_changePassword && value != null && value.length < 6) {
        return '密码长度至少6位';
      }
      return null;
    },
  ),
],
```

#### 分离更新逻辑
```dart
Future<void> _updateUser() async {
  // 更新用户基本信息
  final request = UpdateUserRequest(
    username: _usernameController.text,
    email: _emailController.text,
    role: _selectedRole,
    status: _selectedStatus,
  );
  await userService.updateUser(widget.user.id, request);

  // 如果选择了修改密码，则单独更新密码
  if (_changePassword && _passwordController.text.isNotEmpty) {
    await userService.resetUserPassword(widget.user.id, _passwordController.text);
  }
}
```

### 2. ResetPasswordDialog组件

#### 完整的密码重置对话框
```dart
class ResetPasswordDialog extends StatefulWidget {
  final User user;
  final VoidCallback onPasswordReset;
  // ...
}

class _ResetPasswordDialogState extends State<ResetPasswordDialog> {
  final _formKey = GlobalKey<FormState>();
  final _passwordController = TextEditingController();
  final _confirmPasswordController = TextEditingController();
  bool _isLoading = false;
  bool _showPassword = false;  // 控制密码可见性
}
```

#### 密码确认验证
```dart
TextFormField(
  controller: _confirmPasswordController,
  decoration: InputDecoration(
    labelText: '确认密码',
    border: const OutlineInputBorder(),
    suffixIcon: IconButton(
      icon: Icon(_showPassword ? Icons.visibility : Icons.visibility_off),
      onPressed: () {
        setState(() {
          _showPassword = !_showPassword;
        });
      },
    ),
  ),
  obscureText: !_showPassword,
  validator: (value) {
    if (value == null || value.isEmpty) {
      return '请确认密码';
    }
    if (value != _passwordController.text) {
      return '两次输入的密码不一致';
    }
    return null;
  },
),
```

#### 安全提示UI
```dart
Container(
  padding: const EdgeInsets.all(12),
  decoration: BoxDecoration(
    color: Colors.orange.withValues(alpha: 0.1),
    borderRadius: BorderRadius.circular(8),
    border: Border.all(color: Colors.orange.withValues(alpha: 0.3)),
  ),
  child: Row(
    children: [
      Icon(Icons.warning_amber, color: Colors.orange[700], size: 20),
      const SizedBox(width: 8),
      Expanded(
        child: Text(
          '重置密码后，用户需要使用新密码重新登录',
          style: TextStyle(color: Colors.orange[700], fontSize: 12),
        ),
      ),
    ],
  ),
),
```

### 3. 用户列表操作按钮

#### 新增重置密码按钮
```dart
// 操作按钮组
Row(
  mainAxisSize: MainAxisSize.min,
  children: [
    IconButton(
      onPressed: () => _showEditUserDialog(user),
      icon: const Icon(Icons.edit, size: 18),
      tooltip: '编辑',
    ),
    IconButton(
      onPressed: () => _showResetPasswordDialog(user),
      icon: const Icon(Icons.lock_reset, size: 18, color: Colors.orange),
      tooltip: '重置密码',
    ),
    IconButton(
      onPressed: () => _showDeleteUserDialog(user),
      icon: const Icon(Icons.delete, size: 18, color: Colors.red),
      tooltip: '删除',
    ),
  ],
),
```

## 🔐 安全特性

### 1. 密码验证
- **最小长度**: 密码至少6位字符
- **确认验证**: 两次输入密码必须一致
- **非空验证**: 选择修改密码时必须输入新密码

### 2. 分离更新
- **独立API调用**: 用户信息和密码分别更新
- **事务安全**: 即使密码更新失败，用户信息也已成功更新
- **错误隔离**: 不同操作的错误信息分别处理

### 3. 用户体验
- **可选修改**: 编辑用户时密码修改是可选的
- **密码可见性**: 支持显示/隐藏密码输入
- **操作确认**: 重置密码前显示影响提示

## 📱 用户界面

### 1. 编辑用户对话框
```
┌─────────────────────────────────┐
│ 编辑用户                        │
├─────────────────────────────────┤
│ 用户名: [张三            ]      │
│ 邮箱:   [zhangsan@...    ]      │
│ 角色:   [管理员 ▼        ]      │
│ 状态:   [启用 ▼          ]      │
│                                 │
│ ☑ 修改密码                      │
│ 新密码: [••••••••        ]      │
│                                 │
├─────────────────────────────────┤
│              [取消] [保存]      │
└─────────────────────────────────┘
```

### 2. 重置密码对话框
```
┌─────────────────────────────────┐
│ 重置密码 - 张三                 │
├─────────────────────────────────┤
│ 为用户 "张三" 设置新密码        │
│                                 │
│ 新密码:   [••••••••      ] 👁   │
│ 确认密码: [••••••••      ] 👁   │
│                                 │
│ ⚠ 重置密码后，用户需要使用新    │
│   密码重新登录                  │
├─────────────────────────────────┤
│              [取消] [重置密码]  │
└─────────────────────────────────┘
```

### 3. 用户列表操作
```
┌─────────────────────────────────────────────────────────┐
│ 用户名    邮箱         角色    状态    操作             │
├─────────────────────────────────────────────────────────┤
│ 张三     zhangsan@...  管理员  启用   ✏️ 🔓 🗑️        │
│ 李四     lisi@...      用户    启用   ✏️ 🔓 🗑️        │
└─────────────────────────────────────────────────────────┘
```

## 🧪 功能测试

### 1. 编辑用户密码修改
- ✅ **默认状态**: 密码修改复选框未选中，密码输入框隐藏
- ✅ **选中状态**: 勾选复选框后显示密码输入框
- ✅ **取消选中**: 取消勾选后隐藏密码输入框并清空内容
- ✅ **验证逻辑**: 选中时密码为必填，未选中时跳过密码验证
- ✅ **分离更新**: 用户信息和密码分别调用API更新

### 2. 独立密码重置
- ✅ **对话框显示**: 点击重置密码按钮正确显示对话框
- ✅ **密码确认**: 两次密码输入不一致时显示错误
- ✅ **密码可见性**: 眼睛图标正确切换密码显示/隐藏
- ✅ **长度验证**: 密码少于6位时显示错误提示
- ✅ **API调用**: 成功调用resetUserPassword API

### 3. 用户体验
- ✅ **操作反馈**: 成功/失败操作都有相应的SnackBar提示
- ✅ **加载状态**: 操作过程中按钮显示加载状态
- ✅ **错误处理**: 网络错误或API错误都有友好提示
- ✅ **界面刷新**: 操作成功后自动刷新用户列表

## 🚀 API集成

### 1. 后端API支持
- ✅ **PUT /admin/users/:id/password**: 重置用户密码API
- ✅ **密码加密**: 后端使用password_hash加密存储
- ✅ **权限验证**: 需要管理员权限才能重置密码
- ✅ **参数验证**: 验证密码长度和格式

### 2. 前端API调用
```dart
// UserService中的密码重置方法
Future<void> resetUserPassword(String userId, String newPassword) async {
  try {
    final response = await _apiService.put(
      '${ApiEndpoints.updateUser}/$userId/password',
      data: {'password': newPassword},
    );

    if (response.statusCode != 200) {
      final errorData = response.data as Map<String, dynamic>?;
      final errorMessage = errorData?['message'] ?? '重置密码失败';
      throw Exception(errorMessage);
    }
  } catch (e) {
    throw Exception('重置密码失败: $e');
  }
}
```

## 📊 构建验证

### 构建结果
```bash
flutter build windows --release
# ✅ 构建成功 (47.2秒)
# ✅ 生成 plb_kj_admin.exe
# ✅ 无编译错误
# ✅ 所有新功能正常工作
```

## 🎉 功能总结

### 实现的功能
1. **编辑用户时可选密码修改** - 灵活的密码更新选项
2. **独立密码重置对话框** - 专门的密码重置功能
3. **密码确认验证** - 确保密码输入正确
4. **密码可见性控制** - 用户友好的密码输入体验
5. **安全提示信息** - 告知用户操作的影响
6. **完整的错误处理** - 友好的错误提示和重试机制

### 用户体验提升
- **操作灵活性**: 编辑用户时可选择是否修改密码
- **专用功能**: 独立的密码重置功能，操作更直观
- **安全提示**: 明确告知密码重置的影响
- **即时反馈**: 操作成功/失败都有明确提示

### 安全性保障
- **分离更新**: 用户信息和密码分别更新，降低风险
- **密码验证**: 多重验证确保密码安全性
- **权限控制**: 只有管理员可以重置用户密码
- **加密存储**: 后端使用安全的密码加密方式

通过这次功能增强，用户管理系统的密码管理功能更加完善和安全，为管理员提供了灵活而强大的用户密码管理工具！🔐
