import 'package:flutter/material.dart';
import 'shared/routes/admin_routes.dart';
import 'shared/theme/app_theme.dart';

void main() {
  runApp(const AdminApp());
}

class AdminApp extends StatelessWidget {
  const AdminApp({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: '跨境电商管理系统 - 管理端',
      theme: AppTheme.lightTheme,
      darkTheme: AppTheme.darkTheme,
      themeMode: ThemeMode.system,
      onGenerateRoute: AdminRoutes.generateRoute,
      initialRoute: AdminRoutes.splash,
      debugShowCheckedModeBanner: false,
    );
  }
}
