class ApiEndpoints {
  // 认证相关端点
  static const String adminLogin = '/admin/login';
  static const String userLogin = '/users/login';
  static const String validateToken = '/auth/validate';
  static const String getCaptcha = '/auth/captcha';

  // 用户管理端点
  static const String users = '/admin/users';
  static const String userProfile = '/users/profile';
  static const String createUser = '/admin/users';
  static const String updateUser = '/admin/users';  // PUT /admin/users/{id}
  static const String deleteUser = '/admin/users';  // DELETE /admin/users/{id}
  static const String getUserById = '/admin/users'; // GET /admin/users/{id}

  // 统计数据端点
  static const String stats = '/admin/stats';
  static const String userStats = '/admin/stats/users';
}