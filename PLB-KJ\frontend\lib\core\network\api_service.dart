import 'dart:io';
import 'package:dio/dio.dart';
import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:cookie_jar/cookie_jar.dart';
import 'package:dio_cookie_manager/dio_cookie_manager.dart';
import '../config/app_config.dart';

class ApiService {
  late final Dio _dio;
  String? _cachedToken;
  final CookieJar _cookieJar = CookieJar();
  
  ApiService() {
    _dio = Dio(BaseOptions(
      baseUrl: AppConfig.apiBaseUrl,
      connectTimeout: const Duration(seconds: 10),
      receiveTimeout: const Duration(seconds: 10),
      sendTimeout: const Duration(seconds: 10),
      validateStatus: (status) => true, // 让Dio不要自动抛出错误
    ));

    // 添加Cookie管理器以保持会话
    _dio.interceptors.add(CookieManager(_cookieJar));

    _dio.interceptors.add(
      InterceptorsWrapper(
        onRequest: (options, handler) async {
          debugPrint('🌐 发起请求: ${options.uri}');
          debugPrint('📤 请求数据: ${options.data}');
          
          options.headers.addAll({
            'Content-Type': 'application/json',
            'Accept': 'application/json',
          });
          
          final token = _cachedToken ?? await _getAuthToken();
          if (token != null) {
            options.headers['Authorization'] = 'Bearer $token';
          }
          
          return handler.next(options);
        },
        onResponse: (response, handler) {
          debugPrint('📥 收到响应: [${response.statusCode}] ${response.requestOptions.uri}');
          debugPrint('📦 响应数据: ${response.data}');
          
          // 检查响应状态码
          if (response.statusCode == 401) {
            clearAuthToken();
          }
          
          return handler.next(response);
        },
        onError: (error, handler) async {
          debugPrint('❌ 请求错误: ${error.message}');
          
          if (error.type == DioExceptionType.connectionTimeout ||
              error.type == DioExceptionType.sendTimeout ||
              error.type == DioExceptionType.receiveTimeout) {
            debugPrint('⏱️ 连接超时');
            return handler.reject(
              DioException(
                requestOptions: error.requestOptions,
                error: '连接超时，请检查网络设置',
                type: error.type,
              ),
            );
          }
          
          if (error.type == DioExceptionType.unknown && 
              error.error is SocketException) {
            debugPrint('🔌 网络连接失败: ${error.message}');
            return handler.reject(
              DioException(
                requestOptions: error.requestOptions,
                error: '网络连接失败，请检查网络设置',
                type: error.type,
              ),
            );
          }
          
          return handler.next(error);
        },
      ),
    );

    if (kDebugMode) {
      _dio.interceptors.add(LogInterceptor(
        request: true,
        requestHeader: true,
        requestBody: true,
        responseHeader: true,
        responseBody: true,
        error: true,
      ));
    }
  }

  Future<String?> _getAuthToken() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      return prefs.getString(AppConfig.authTokenKey);
    } catch (e) {
      debugPrint('获取token失败: $e');
      return null;
    }
  }

  Future<Response<T>> get<T>(
    String path, {
    Map<String, dynamic>? queryParameters,
    Options? options,
  }) async {
    try {
      final response = await _dio.get<T>(
        path,
        queryParameters: queryParameters,
        options: options,
      );
      _validateResponse(response);
      return response;
    } on DioException catch (e) {
      throw _handleError(e);
    }
  }

  Future<Response<T>> post<T>(
    String path, {
    dynamic data,
    Map<String, dynamic>? queryParameters,
    Options? options,
  }) async {
    try {
      final response = await _dio.post<T>(
        path,
        data: data,
        queryParameters: queryParameters,
        options: options,
      );
      _validateResponse(response);
      return response;
    } on DioException catch (e) {
      throw _handleError(e);
    }
  }

  Future<Response<T>> put<T>(
    String path, {
    dynamic data,
    Map<String, dynamic>? queryParameters,
    Options? options,
  }) async {
    try {
      final response = await _dio.put<T>(
        path,
        data: data,
        queryParameters: queryParameters,
        options: options,
      );
      _validateResponse(response);
      return response;
    } on DioException catch (e) {
      throw _handleError(e);
    }
  }

  Future<Response<T>> delete<T>(
    String path, {
    dynamic data,
    Map<String, dynamic>? queryParameters,
    Options? options,
  }) async {
    try {
      final response = await _dio.delete<T>(
        path,
        data: data,
        queryParameters: queryParameters,
        options: options,
      );
      _validateResponse(response);
      return response;
    } on DioException catch (e) {
      throw _handleError(e);
    }
  }

  void _validateResponse(Response response) {
    if (response.statusCode == null || response.statusCode! >= 500) {
      throw DioException(
        requestOptions: response.requestOptions,
        response: response,
        type: DioExceptionType.badResponse,
        error: '服务器错误',
      );
    }
    
    if (response.statusCode == 404) {
      throw DioException(
        requestOptions: response.requestOptions,
        response: response,
        type: DioExceptionType.badResponse,
        error: '请求的接口不存在',
      );
    }
    
    final data = response.data;
    if (data is Map<String, dynamic> && data['success'] == false) {
      throw DioException(
        requestOptions: response.requestOptions,
        response: response,
        type: DioExceptionType.badResponse,
        error: data['error'] ?? '请求失败',
      );
    }
  }

  ApiException _handleError(DioException error) {
    debugPrint('处理错误: ${error.type} - ${error.message}');
    
    switch (error.type) {
      case DioExceptionType.connectionTimeout:
      case DioExceptionType.sendTimeout:
      case DioExceptionType.receiveTimeout:
        return ApiException('连接超时，请检查网络连接', -1);
      
      case DioExceptionType.unknown:
        if (error.error is SocketException) {
          return ApiException('网络连接失败，请检查网络设置', -1);
        }
        return ApiException('未知错误: ${error.message}', -1);
      
      default:
        final response = error.response;
        if (response != null) {
          final data = response.data;
          if (data is Map<String, dynamic>) {
            return ApiException(
              data['error'] ?? '请求失败',
              response.statusCode,
            );
          }
        }
        return ApiException('请求失败: ${error.message}', -1);
    }
  }

  void setAuthToken(String token) {
    _cachedToken = token;
  }

  void clearAuthToken() {
    _cachedToken = null;
  }

  // 获取背景设置
  static Future<Map<String, dynamic>?> getBackgroundSetting() async {
    try {
      final apiService = ApiService();
      final response = await apiService.get('/settings/background');
      return response.data as Map<String, dynamic>;
    } catch (e) {
      debugPrint('获取背景设置失败: $e');
      return null;
    }
  }

  // 更新背景设置
  static Future<Map<String, dynamic>?> updateBackgroundSetting(String backgroundImage) async {
    try {
      final apiService = ApiService();
      final response = await apiService.post('/settings/background', data: {
        'background_image': backgroundImage,
      });
      return response.data as Map<String, dynamic>;
    } catch (e) {
      debugPrint('更新背景设置失败: $e');
      return null;
    }
  }
}

// 自定义API异常类
class ApiException implements Exception {
  final String message;
  final int? statusCode;

  ApiException(this.message, this.statusCode);

  @override
  String toString() => 'ApiException: $message (Status: $statusCode)';
}

// API响应包装类
class ApiResponse<T> {
  final bool success;
  final T? data;
  final String? message;
  final int? code;

  ApiResponse({
    required this.success,
    this.data,
    this.message,
    this.code,
  });

  factory ApiResponse.fromJson(Map<String, dynamic> json, T Function(dynamic)? fromJsonT) {
    return ApiResponse<T>(
      success: json['success'] ?? false,
      data: json['data'] != null && fromJsonT != null ? fromJsonT(json['data']) : json['data'] as T?,
      message: json['message'] ?? json['error'],
      code: json['code'],
    );
  }

  factory ApiResponse.success(T data, {String? message}) {
    return ApiResponse<T>(
      success: true,
      data: data,
      message: message,
    );
  }

  factory ApiResponse.error(String message, {int? code}) {
    return ApiResponse<T>(
      success: false,
      message: message,
      code: code,
    );
  }
}