import '../../core/network/api_service.dart';
import '../../core/network/api_endpoints.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../../core/config/app_config.dart';
import 'package:flutter/foundation.dart';
import 'dart:convert';
import '../../shared/utils/local_storage.dart';
import 'package:dio/dio.dart';

class AuthService {
  final ApiService _apiService;

  AuthService(this._apiService);

  // 获取验证码
  Future<ApiResponse<Map<String, dynamic>>> getCaptcha() async {
    try {
      debugPrint('获取验证码');
      final response = await _apiService.get('/auth/captcha');
      
      final data = response.data;
      if (data is Map<String, dynamic> && data['success'] == true) {
        return ApiResponse.success(data);
      } else if (response.statusCode == -1) {
        return ApiResponse.error('网络连接失败，请检查网络设置');
      } else {
        return ApiResponse.error(data?['error'] ?? '获取验证码失败');
      }
    } catch (e) {
      debugPrint('获取验证码失败: $e');
      return ApiResponse.error('获取验证码失败: $e');
    }
  }

  Future<Map<String, dynamic>?> login(String username, String password, {String? captcha}) async {
    try {
      debugPrint('尝试登录: $username');

      final Map<String, dynamic> data = {
        'username': username,
        'password': password,
      };

      // 如果提供了验证码，则添加到请求数据中
      if (captcha != null && captcha.isNotEmpty) {
        data['captcha'] = captcha;
      }

      final response = await _apiService.post(
        ApiEndpoints.adminLogin,
        data: data,
      );

      debugPrint('登录响应状态码: ${response.statusCode}');
      debugPrint('登录响应数据: ${response.data}');

      final responseData = response.data;

      // 处理成功响应
      if (response.statusCode == 200 && responseData is Map<String, dynamic>) {
        if (responseData['success'] == true) {
          final token = responseData['token'];
          final user = responseData['user'];

          // 保存认证信息
          final prefs = await SharedPreferences.getInstance();
          await prefs.setString('auth_token', token);
          await prefs.setString('user_data', jsonEncode(user));

          debugPrint('登录成功，token已保存');
          return responseData;
        } else {
          // 登录失败，但服务器返回了错误信息
          final errorMessage = responseData['error'] ?? '登录失败';
          debugPrint('登录失败: $errorMessage');
          throw Exception(errorMessage);
        }
      } else {
        // 处理其他HTTP状态码
        String errorMessage = '登录失败';
        if (responseData is Map<String, dynamic> && responseData['error'] != null) {
          errorMessage = responseData['error'];
        } else if (response.statusCode == 401) {
          errorMessage = '用户名或密码错误';
        } else if (response.statusCode == 403) {
          errorMessage = '权限不足';
        } else if (response.statusCode == -1) {
          errorMessage = '网络连接失败，请检查网络设置';
        } else {
          errorMessage = '网络请求失败: ${response.statusCode}';
        }

        debugPrint('登录失败: $errorMessage');
        throw Exception(errorMessage);
      }
    } catch (e) {
      debugPrint('登录异常: $e');
      rethrow;
    }
  }

  // 用户注册
  Future<ApiResponse<Map<String, dynamic>>> userRegister({
    required String username,
    required String email,
    required String password,
  }) async {
    try {
      debugPrint('尝试用户注册: $username');
      
      final Map<String, dynamic> requestData = {
        'username': username,
        'email': email,
        'password_hash': password,  // 后端会处理密码哈希
      };
      
      final response = await _apiService.post(
        ApiEndpoints.users,  // 使用/users端点进行注册
        data: requestData,
      );

      final data = response.data;
      if (response.statusCode == 200 || response.statusCode == 201) {
        // 注册成功，返回用户信息
        return ApiResponse.success(data is Map<String, dynamic> ? data : {});
      } else if (response.statusCode == -1) {
        return ApiResponse.error('网络连接失败，请检查网络设置');
      } else {
        return ApiResponse.error(data?['error'] ?? '注册失败');
      }
    } catch (e) {
      debugPrint('注册失败: $e');
      return ApiResponse.error('注册失败: $e');
    }
  }

  Future<bool> logout() async {
    try {
      await LocalStorage.remove('auth_token');
      await LocalStorage.remove('user_data');
      return true;
    } catch (e) {
      return false;
    }
  }

  Future<String?> getAuthToken() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      return prefs.getString('auth_token');
    } catch (e) {
      return null;
    }
  }

  Future<bool> isLoggedIn() async {
    final token = await getAuthToken();
    if (token == null) return false;
    return await validateToken(token);
  }

  Future<Map<String, dynamic>?> getUserData() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final userDataStr = prefs.getString('user_data');
      if (userDataStr == null) return null;
      return jsonDecode(userDataStr);
    } catch (e) {
      return null;
    }
  }

  Future<String?> getUserType() async {
    final userData = await getUserData();
    return userData?['role'];
  }
  
  Future<bool> validateToken(String token) async {
    try {
      final response = await _apiService.get(
        ApiEndpoints.validateToken,
        options: Options(
          headers: {'Authorization': 'Bearer $token'}
        ),
      );

      if (response.statusCode == 200) {
        final data = response.data;
        return data is Map<String, dynamic> && data['valid'] == true;
      }
      return false;
    } catch (e) {
      return false;
    }
  }
}