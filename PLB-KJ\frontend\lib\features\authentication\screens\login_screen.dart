import 'dart:convert';
import 'dart:typed_data';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../../../shared/widgets/loading_indicator.dart';
import '../../../shared/widgets/error_display.dart';
import '../../../shared/widgets/animated_button.dart';
import '../../../shared/widgets/simple_background.dart';
import '../auth_service.dart';
import '../../../core/network/api_service.dart';
import '../../../shared/routes/app_routes.dart';

enum LoginType { admin, user }

class LoginScreen extends StatefulWidget {
  final LoginType loginType;

  const LoginScreen({Key? key, this.loginType = LoginType.admin}) : super(key: key);

  @override
  State<LoginScreen> createState() => _LoginScreenState();
}

class _LoginScreenState extends State<LoginScreen> with SingleTickerProviderStateMixin {
  final _formKey = GlobalKey<FormState>();
  final _usernameController = TextEditingController();
  final _passwordController = TextEditingController();
  final _captchaController = TextEditingController();
  final AuthService _authService = AuthService(ApiService());
  
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;
  
  bool _isLoading = false;
  bool _obscurePassword = true;
  String? _errorMessage;
  String? _captchaImage;
  bool _showCaptcha = false;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 600),
      vsync: this,
    );
    
    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeInOut),
    );
    
    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 0.3),
      end: Offset.zero,
    ).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeOutCubic),
    );
    
    // 启动动画
    _animationController.forward();
    
    // 获取验证码
    _loadCaptcha();
  }

  Future<void> _loadCaptcha() async {
    try {
      final response = await _authService.getCaptcha();
      if (response.success && response.data != null) {
        setState(() {
          _captchaImage = response.data!['image'] as String?;
          _showCaptcha = _captchaImage != null;
        });
      }
    } catch (e) {
      // 验证码加载失败，不影响主要登录流程
      setState(() {
        _showCaptcha = false;
      });
    }
  }

  @override
  void dispose() {
    _usernameController.dispose();
    _passwordController.dispose();
    _captchaController.dispose();
    _animationController.dispose();
    super.dispose();
  }

  Future<void> _handleLogin() async {
    if (!_formKey.currentState!.validate()) return;

    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      final response = await _authService.login(
        _usernameController.text,
        _passwordController.text,
        captcha: _showCaptcha ? _captchaController.text : null,
      );

      if (response != null && response.containsKey('token')) {
        // 登录成功，保存token并导航到相应主屏幕
        final token = response['token'];
        if (token != null) {
          // 使用shared_preferences保存token
          final prefs = await SharedPreferences.getInstance();
          await prefs.setString('auth_token', token);
          
          // 根据登录类型导航到不同主屏幕
          if (mounted) {
            if (widget.loginType == LoginType.admin) {
              Navigator.pushReplacementNamed(context, AppRoutes.dashboard);
            } else {
              Navigator.pushReplacementNamed(context, AppRoutes.userCenter);
            }
          }
        }
      } else {
        setState(() {
          _errorMessage = '登录失败';
          // 登录失败时刷新验证码
          _refreshCaptchaIfNeeded();
        });
      }
    } catch (e) {
      setState(() {
        _errorMessage = '登录过程中发生错误: $e';
        // 登录失败时刷新验证码
        _refreshCaptchaIfNeeded();
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  void _refreshCaptchaIfNeeded() {
    if (_showCaptcha) {
      _loadCaptcha();
      _captchaController.clear();
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: SimpleBackground(
        backgroundColor: Theme.of(context).primaryColor,
        child: SafeArea(
          child: SingleChildScrollView(
            padding: const EdgeInsets.all(24.0),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                // Logo and Title with enhanced animation
                SlideTransition(
                  position: _slideAnimation,
                  child: FadeTransition(
                    opacity: _fadeAnimation,
                    child: Column(
                      children: [
                        Container(
                          margin: const EdgeInsets.only(top: 20, bottom: 10),
                          child: Stack(
                            alignment: Alignment.center,
                            children: [
                              Container(
                                width: 100,
                                height: 100,
                                decoration: BoxDecoration(
                                  color: Colors.white.withOpacity(0.2),
                                  shape: BoxShape.circle,
                                ),
                              ),
                              Icon(
                                Icons.storefront,
                                size: 80,
                                color: Colors.white,
                              ),
                            ],
                          ),
                        ),
                        Text(
                          '跨境电商管理系统',
                          style: TextStyle(
                            fontSize: 28,
                            fontWeight: FontWeight.bold,
                            color: Colors.white,
                            shadows: [
                              Shadow(
                                offset: Offset(1, 1),
                                blurRadius: 3,
                                color: Colors.black26,
                              ),
                            ],
                          ),
                        ),
                        const SizedBox(height: 8),
                        Text(
                          widget.loginType == LoginType.admin ? '管理员登录' : '用户登录',
                          style: TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.w500,
                            color: Colors.white70,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
                const SizedBox(height: 30),
                
                // Login Card with enhanced design
                SlideTransition(
                  position: _slideAnimation,
                  child: FadeTransition(
                    opacity: _fadeAnimation,
                    child: Card(
                      elevation: 8,
                      color: Colors.white.withOpacity(0.9),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(20.0),
                      ),
                      child: Padding(
                        padding: const EdgeInsets.all(30.0),
                        child: Form(
                          key: _formKey,
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.stretch,
                            children: [
                              // Username Field with enhanced design
                              TextFormField(
                                controller: _usernameController,
                                style: TextStyle(
                                  color: Theme.of(context).brightness == Brightness.dark 
                                    ? Colors.white 
                                    : Colors.black,
                                ),
                                decoration: InputDecoration(
                                  labelText: '用户名',
                                  labelStyle: TextStyle(
                                    color: Theme.of(context).brightness == Brightness.dark 
                                      ? Colors.white70 
                                      : Colors.black54,
                                  ),
                                  prefixIcon: Icon(
                                    Icons.person_outline,
                                    color: Theme.of(context).brightness == Brightness.dark 
                                      ? Colors.white70 
                                      : Colors.black54,
                                  ),
                                  border: OutlineInputBorder(
                                    borderRadius: BorderRadius.circular(12.0),
                                    borderSide: BorderSide(color: Colors.grey.shade300),
                                  ),
                                  focusedBorder: OutlineInputBorder(
                                    borderRadius: BorderRadius.circular(12.0),
                                    borderSide: BorderSide(
                                      color: Theme.of(context).primaryColor,
                                      width: 2.0,
                                    ),
                                  ),
                                  enabledBorder: OutlineInputBorder(
                                    borderRadius: BorderRadius.circular(12.0),
                                    borderSide: BorderSide(color: Colors.grey.shade300),
                                  ),
                                  filled: true,
                                  fillColor: Theme.of(context).brightness == Brightness.dark 
                                    ? Colors.grey.shade800 
                                    : Colors.grey.shade50,
                                ),
                                validator: (value) {
                                  if (value == null || value.isEmpty) {
                                    return '请输入用户名';
                                  }
                                  return null;
                                },
                              ),
                              const SizedBox(height: 20),
                              
                              // Password Field with enhanced design
                              TextFormField(
                                controller: _passwordController,
                                obscureText: _obscurePassword,
                                style: TextStyle(
                                  color: Theme.of(context).brightness == Brightness.dark 
                                    ? Colors.white 
                                    : Colors.black,
                                ),
                                decoration: InputDecoration(
                                  labelText: '密码',
                                  labelStyle: TextStyle(
                                    color: Theme.of(context).brightness == Brightness.dark 
                                      ? Colors.white70 
                                      : Colors.black54,
                                  ),
                                  prefixIcon: Icon(
                                    Icons.lock_outline,
                                    color: Theme.of(context).brightness == Brightness.dark 
                                      ? Colors.white70 
                                      : Colors.black54,
                                  ),
                                  suffixIcon: IconButton(
                                    icon: Icon(
                                      _obscurePassword 
                                        ? Icons.visibility_off_outlined
                                        : Icons.visibility_outlined,
                                      color: Theme.of(context).brightness == Brightness.dark 
                                        ? Colors.white70 
                                        : Colors.black54,
                                    ),
                                    onPressed: () {
                                      setState(() {
                                        _obscurePassword = !_obscurePassword;
                                      });
                                    },
                                  ),
                                  border: OutlineInputBorder(
                                    borderRadius: BorderRadius.circular(12.0),
                                    borderSide: BorderSide(color: Colors.grey.shade300),
                                  ),
                                  focusedBorder: OutlineInputBorder(
                                    borderRadius: BorderRadius.circular(12.0),
                                    borderSide: BorderSide(
                                      color: Theme.of(context).primaryColor,
                                      width: 2.0,
                                    ),
                                  ),
                                  enabledBorder: OutlineInputBorder(
                                    borderRadius: BorderRadius.circular(12.0),
                                    borderSide: BorderSide(color: Colors.grey.shade300),
                                  ),
                                  filled: true,
                                  fillColor: Theme.of(context).brightness == Brightness.dark 
                                    ? Colors.grey.shade800 
                                    : Colors.grey.shade50,
                                ),
                                validator: (value) {
                                  if (value == null || value.isEmpty) {
                                    return '请输入密码';
                                  }
                                  if (value.length < 6) {
                                    return '密码至少需要6位字符';
                                  }
                                  return null;
                                },
                              ),
                              
                              // Captcha Field
                              if (_showCaptcha) ...[
                                const SizedBox(height: 20),
                                Row(
                                  children: [
                                    Expanded(
                                      flex: 2,
                                      child: TextFormField(
                                        controller: _captchaController,
                                        style: TextStyle(
                                          color: Theme.of(context).brightness == Brightness.dark 
                                            ? Colors.white 
                                            : Colors.black,
                                        ),
                                        decoration: InputDecoration(
                                          labelText: '验证码',
                                          labelStyle: TextStyle(
                                            color: Theme.of(context).brightness == Brightness.dark 
                                              ? Colors.white70 
                                              : Colors.black54,
                                          ),
                                          prefixIcon: Icon(
                                            Icons.security_outlined,
                                            color: Theme.of(context).brightness == Brightness.dark 
                                              ? Colors.white70 
                                              : Colors.black54,
                                          ),
                                          border: OutlineInputBorder(
                                            borderRadius: BorderRadius.circular(12.0),
                                            borderSide: BorderSide(color: Colors.grey.shade300),
                                          ),
                                          focusedBorder: OutlineInputBorder(
                                            borderRadius: BorderRadius.circular(12.0),
                                            borderSide: BorderSide(
                                              color: Theme.of(context).primaryColor,
                                              width: 2.0,
                                            ),
                                          ),
                                          enabledBorder: OutlineInputBorder(
                                            borderRadius: BorderRadius.circular(12.0),
                                            borderSide: BorderSide(color: Colors.grey.shade300),
                                          ),
                                          filled: true,
                                          fillColor: Theme.of(context).brightness == Brightness.dark 
                                            ? Colors.grey.shade800 
                                            : Colors.grey.shade50,
                                        ),
                                        validator: (value) {
                                          if (value == null || value.isEmpty) {
                                            return '请输入验证码';
                                          }
                                          if (value.length < 4) {
                                            return '验证码至少需要4位字符';
                                          }
                                          return null;
                                        },
                                      ),
                                    ),
                                    const SizedBox(width: 10),
                                    Expanded(
                                      flex: 1,
                                      child: Container(
                                        height: 50, // 设置固定高度以确保完整显示
                                        decoration: BoxDecoration(
                                          borderRadius: BorderRadius.circular(12.0),
                                          border: Border.all(color: Colors.grey.shade300),
                                          color: Theme.of(context).brightness == Brightness.dark 
                                            ? Colors.grey.shade800 
                                            : Colors.grey.shade50,
                                        ),
                                        child: _captchaImage != null
                                          ? Image.memory(
                                              Uint8List.fromList(
                                                base64Decode(_captchaImage!.substring(_captchaImage!.indexOf(',') + 1))
                                              ),
                                              fit: BoxFit.contain, // 使用contain确保完整显示
                                            )
                                          : const Icon(Icons.error_outline),
                                      ),
                                    ),
                                    IconButton(
                                      icon: const Icon(Icons.refresh),
                                      onPressed: _loadCaptcha,
                                    ),
                                  ],
                                ),
                              ],
                              
                              // Error Message
                              if (_errorMessage != null) ...[
                                const SizedBox(height: 16),
                                ErrorDisplay(
                                  message: _errorMessage!,
                                  color: Theme.of(context).colorScheme.error,
                                ),
                              ],
                              
                              // Login Button with enhanced styling
                              const SizedBox(height: 24),
                              AnimatedButton(
                                text: '登录',
                                onPressed: _handleLogin,
                                loading: _isLoading,
                                height: 55,
                                borderRadius: BorderRadius.circular(12.0),
                                icon: Icons.login,
                              ),
                              
                              // Forgot Password (for user login)
                              if (widget.loginType == LoginType.user) ...[
                                const SizedBox(height: 16),
                                Center(
                                  child: TextButton(
                                    onPressed: () {
                                      // TODO: Implement forgot password functionality
                                    },
                                    child: const Text(
                                      '忘记密码？',
                                      style: TextStyle(
                                        fontSize: 16,
                                        fontWeight: FontWeight.w500,
                                      ),
                                    ),
                                  ),
                                ),
                              ],
                              
                              // Back to selection
                              const SizedBox(height: 8),
                              Center(
                                child: TextButton(
                                  onPressed: () {
                                    Navigator.pop(context);
                                  },
                                  child: const Text(
                                    '返回登录选择',
                                    style: TextStyle(
                                      fontSize: 16,
                                      fontWeight: FontWeight.w500,
                                    ),
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                    ),
                  ),
                ),
                
                // Footer with enhanced styling
                const SizedBox(height: 30),
                Text(
                  '© 2025 跨境电商管理系统',
                  style: TextStyle(
                    color: Colors.white70,
                    fontSize: 14,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
  
}