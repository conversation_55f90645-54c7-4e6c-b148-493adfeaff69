import 'package:flutter/material.dart';
import '../../../shared/widgets/animated_button.dart';
import '../../../shared/routes/app_routes.dart';
import '../../../shared/widgets/simple_background.dart';

class LoginSelectionScreen extends StatelessWidget {
  const LoginSelectionScreen({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: SimpleBackground(
        backgroundColor: Theme.of(context).primaryColor,
        child: SafeArea(
          child: Center(
            child: ConstrainedBox(
              constraints: const BoxConstraints(maxWidth: 600),
              child: SingleChildScrollView(
                padding: const EdgeInsets.all(24.0),
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    // Logo and Title with enhanced styling
                    Container(
                      margin: const EdgeInsets.only(top: 30, bottom: 10),
                      child: Stack(
                        alignment: Alignment.center,
                        children: [
                          Container(
                            width: 120,
                            height: 120,
                            decoration: BoxDecoration(
                              color: Colors.white.withOpacity(0.2),
                              shape: BoxShape.circle,
                            ),
                          ),
                          Icon(
                            Icons.storefront,
                            size: 90,
                            color: Colors.white,
                          ),
                        ],
                      ),
                    ),
                    Text(
                      '跨境电商管理系统',
                      style: TextStyle(
                        fontSize: 32,
                        fontWeight: FontWeight.bold,
                        color: Colors.white,
                        shadows: [
                          Shadow(
                            offset: Offset(2, 2),
                            blurRadius: 4,
                            color: Colors.black26,
                          ),
                        ],
                      ),
                    ),
                    const SizedBox(height: 12),
                    Text(
                      '请选择您的登录方式',
                      style: TextStyle(
                        fontSize: 20,
                        fontWeight: FontWeight.w500,
                        color: Colors.white70,
                      ),
                    ),
                    const SizedBox(height: 40),
                    
                    // Login Options Card with enhanced design
                    Card(
                      elevation: 8,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(20.0),
                      ),
                      color: Colors.white.withOpacity(0.9),
                      child: Padding(
                        padding: const EdgeInsets.all(30.0),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.stretch,
                          children: [
                            Text(
                              '登录方式',
                              style: TextStyle(
                                fontSize: 24,
                                fontWeight: FontWeight.bold,
                                color: Theme.of(context).primaryColor,
                              ),
                              textAlign: TextAlign.center,
                            ),
                            const SizedBox(height: 30),
                            AnimatedButton(
                              text: '管理员登录',
                              onPressed: () {
                                Navigator.pushNamed(
                                  context, 
                                  AppRoutes.unifiedLogin, // 确保 AppRoutes.unifiedLogin 正确指向目标页面
                                  arguments: {'isAdmin': true},
                                );
                              },
                              height: 60,
                              borderRadius: BorderRadius.circular(15.0),
                              icon: Icons.admin_panel_settings_outlined,
                            ),
                            const SizedBox(height: 20),
                            AnimatedButton(
                              text: '用户登录',
                              onPressed: () {
                                Navigator.pushNamed(
                                  context, 
                                  AppRoutes.unifiedLogin, // 确保统一登录路由正确
                                  arguments: {'isAdmin': false},
                                );
                              },
                              height: 60,
                              borderRadius: BorderRadius.circular(15.0),
                              color: Colors.transparent,
                              textColor: Theme.of(context).primaryColor,
                              borderSide: BorderSide(
                                color: Theme.of(context).primaryColor,
                                width: 2.5,
                              ),
                              icon: Icons.person_outline,
                            ),
                            const SizedBox(height: 20),
                            AnimatedButton(
                              text: '用户注册',
                              onPressed: () {
                                Navigator.pushNamed(
                                  context, 
                                  AppRoutes.register,
                                  arguments: {'isAdmin': false},
                                );
                              },
                              height: 60,
                              borderRadius: BorderRadius.circular(15.0),
                              color: Colors.transparent,
                              textColor: Theme.of(context).primaryColor,
                              borderSide: BorderSide(
                                color: Theme.of(context).primaryColor,
                                width: 2.5,
                              ),
                              icon: Icons.person_add_outlined,
                            ),
                          ],
                        ),
                      ),
                    ),
                    
                    // Additional information
                    const SizedBox(height: 30),
                    Card(
                      elevation: 4,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(16.0),
                      ),
                      color: Colors.white.withOpacity(0.9),
                      child: Padding(
                        padding: const EdgeInsets.all(20.0),
                        child: Column(
                          children: [
                            Text(
                              '系统说明',
                              style: TextStyle(
                                fontSize: 18,
                                fontWeight: FontWeight.bold,
                                color: Theme.of(context).primaryColor,
                              ),
                            ),
                            const SizedBox(height: 10),
                            const Text(
                              '• 管理员可管理整个系统\n• 用户可访问个人中心和相关功能',
                              style: TextStyle(
                                fontSize: 14,
                                height: 1.5,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                    
                    // Footer with enhanced styling
                    const SizedBox(height: 30),
                    Text(
                      '© 2025 跨境电商管理系统',
                      style: TextStyle(
                        color: Colors.white70,
                        fontSize: 14,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }
}