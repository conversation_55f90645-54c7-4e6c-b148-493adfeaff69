import 'package:flutter/material.dart';
import 'package:dio/dio.dart';
import '../../../core/network/api_service.dart';
import '../../../core/network/api_endpoints.dart';
import '../../../shared/widgets/animated_button.dart';
import '../../../shared/widgets/custom_text_field.dart';
import '../../../shared/widgets/message_snackbar.dart';
import '../../authentication/auth_service.dart';

class RegisterScreen extends StatefulWidget {
  final bool isAdminRegister;

  const RegisterScreen({Key? key, this.isAdminRegister = false}) : super(key: key);

  @override
  State<RegisterScreen> createState() => _RegisterScreenState();
}

class _RegisterScreenState extends State<RegisterScreen> {
  final TextEditingController _usernameController = TextEditingController();
  final TextEditingController _emailController = TextEditingController();
  final TextEditingController _passwordController = TextEditingController();
  final TextEditingController _confirmPasswordController = TextEditingController();
  final AuthService _authService = AuthService(ApiService());
  
  bool _isLoading = false;
  String? _errorMessage;
  bool _obscurePassword = true;
  bool _obscureConfirmPassword = true;

  @override
  void dispose() {
    _usernameController.dispose();
    _emailController.dispose();
    _passwordController.dispose();
    _confirmPasswordController.dispose();
    super.dispose();
  }

  void _handleRegister() async {
    if (_usernameController.text.isEmpty || 
        _emailController.text.isEmpty || 
        _passwordController.text.isEmpty || 
        _confirmPasswordController.text.isEmpty) {
      setState(() {
        _errorMessage = '请填写所有字段';
      });
      return;
    }

    if (_passwordController.text != _confirmPasswordController.text) {
      setState(() {
        _errorMessage = '密码和确认密码不匹配';
      });
      return;
    }

    if (_passwordController.text.length < 6) {
      setState(() {
        _errorMessage = '密码长度至少为6个字符';
      });
      return;
    }

    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      final response = await _authService.userRegister(
        username: _usernameController.text,
        email: _emailController.text,
        password: _passwordController.text,
      );

      if (response.success) {
        // 注册成功
        if (mounted) {
          MessageSnackbar.show(context, '注册成功，请登录');
          
          // 返回到登录页面
          Future.delayed(const Duration(seconds: 2), () {
            Navigator.pop(context);
          });
        }
      } else {
        // 注册失败
        setState(() {
          _errorMessage = response.message ?? '注册失败';
        });
      }
    } catch (e) {
      setState(() {
        _errorMessage = '注册过程中发生错误';
      });
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(widget.isAdminRegister ? '管理员注册' : '用户注册'),
        centerTitle: true,
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(24.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            // Logo and Title
            Container(
              margin: const EdgeInsets.only(bottom: 30),
              child: Stack(
                alignment: Alignment.center,
                children: [
                  Container(
                    width: 100,
                    height: 100,
                    decoration: BoxDecoration(
                      color: Theme.of(context).primaryColor.withOpacity(0.1),
                      shape: BoxShape.circle,
                    ),
                  ),
                  Icon(
                    widget.isAdminRegister ? Icons.admin_panel_settings : Icons.person,
                    size: 60,
                    color: Theme.of(context).primaryColor,
                  ),
                ],
              ),
            ),
            
            Text(
              widget.isAdminRegister ? '管理员注册' : '用户注册',
              style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                fontWeight: FontWeight.bold,
                color: Theme.of(context).primaryColor,
              ),
              textAlign: TextAlign.center,
            ),
            
            const SizedBox(height: 30),
            
            // Error message
            if (_errorMessage != null) ...[
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Theme.of(context).colorScheme.error.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(
                    color: Theme.of(context).colorScheme.error,
                  ),
                ),
                child: Row(
                  children: [
                    Icon(
                      Icons.error_outline,
                      color: Theme.of(context).colorScheme.error,
                    ),
                    const SizedBox(width: 8),
                    Expanded(
                      child: Text(
                        _errorMessage!,
                        style: TextStyle(
                          color: Theme.of(context).colorScheme.error,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
              const SizedBox(height: 20),
            ],
            
            // Username field
            CustomTextField(
              controller: _usernameController,
              label: '用户名',
              keyboardType: TextInputType.text,
            ),
            
            const SizedBox(height: 20),
            
            // Email field
            CustomTextField(
              controller: _emailController,
              label: '邮箱',
              keyboardType: TextInputType.emailAddress,
            ),
            
            const SizedBox(height: 20),
            
            // Password field
            CustomTextField(
              controller: _passwordController,
              label: '密码',
              obscureText: _obscurePassword,
              keyboardType: TextInputType.visiblePassword,
            ),
            
            const SizedBox(height: 20),
            
            // Confirm Password field
            CustomTextField(
              controller: _confirmPasswordController,
              label: '确认密码',
              obscureText: _obscureConfirmPassword,
              keyboardType: TextInputType.visiblePassword,
            ),
            
            const SizedBox(height: 30),
            
            // Register Button
            AnimatedButton(
              text: '注册',
              onPressed: _isLoading ? null : _handleRegister,
              height: 50,
            ),
            
            const SizedBox(height: 20),
            
            // Login link
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                const Text('已有账户？'),
                TextButton(
                  onPressed: () {
                    Navigator.pop(context);
                  },
                  child: const Text('立即登录'),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}