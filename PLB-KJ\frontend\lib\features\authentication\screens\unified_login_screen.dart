import 'dart:async';
import 'dart:typed_data';
import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:dio/dio.dart';
import '../../../core/network/api_service.dart';
import '../../../core/network/api_endpoints.dart';
import '../../../shared/widgets/simple_background.dart';
import '../../../shared/widgets/animated_button.dart';
import '../../../shared/routes/app_routes.dart';

class UnifiedLoginScreen extends StatefulWidget {
  final bool isAdminLogin;

  const UnifiedLoginScreen({Key? key, this.isAdminLogin = true}) : super(key: key);

  @override
  State<UnifiedLoginScreen> createState() => _UnifiedLoginScreenState();
}

class _UnifiedLoginScreenState extends State<UnifiedLoginScreen>
    with TickerProviderStateMixin {
  final _formKey = GlobalKey<FormState>();
  final TextEditingController _usernameController = TextEditingController();
  final TextEditingController _passwordController = TextEditingController();
  final TextEditingController _captchaController = TextEditingController();
  final ApiService _apiService = ApiService();

  bool _isLoading = false;
  String? _errorMessage;
  bool _obscurePassword = true;
  String? _captchaImage;
  bool _showCaptcha = false;
  bool _captchaLoading = false;

  // 动画控制器
  late AnimationController _fadeController;
  late AnimationController _slideController;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;

  @override
  void initState() {
    super.initState();

    // 初始化动画控制器
    _fadeController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );
    _slideController = AnimationController(
      duration: const Duration(milliseconds: 600),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _fadeController, curve: Curves.easeOut),
    );
    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 0.3),
      end: Offset.zero,
    ).animate(CurvedAnimation(parent: _slideController, curve: Curves.easeOut));

    // 启动动画
    _fadeController.forward();
    _slideController.forward();

    // 延迟加载验证码，确保界面先显示
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _loadCaptcha();
    });
  }

  @override
  void dispose() {
    _fadeController.dispose();
    _slideController.dispose();
    _usernameController.dispose();
    _passwordController.dispose();
    _captchaController.dispose();
    super.dispose();
  }

  Future<void> _loadCaptcha() async {
    if (_captchaLoading) return;
    
    setState(() {
      _captchaLoading = true;
      _errorMessage = null; // 清除之前的错误信息
    });
    
    try {
      final response = await _apiService.get(ApiEndpoints.getCaptcha);
      
      if (response.statusCode == 200 && response.data['success'] == true) {
        setState(() {
          _captchaImage = response.data['image'] as String?;
          _showCaptcha = true;
        });
      } else {
        // 即使获取失败也显示验证码区域，允许用户手动刷新
        setState(() {
          _showCaptcha = true;
        });
      }
    } on DioException catch (e) {
      debugPrint('获取验证码失败: ${e.message}');
      // 网络错误时仍然显示验证码区域
      setState(() {
        _showCaptcha = true;
      });
    } catch (e) {
      debugPrint('获取验证码异常: $e');
      // 其他错误时仍然显示验证码区域
      setState(() {
        _showCaptcha = true;
      });
    } finally {
      setState(() {
        _captchaLoading = false;
      });
    }
  }

  /// 解码base64图片数据，添加错误处理
  Uint8List _decodeBase64Image(String base64String) {
    try {
      // 移除可能存在的data URI前缀
      if (base64String.startsWith('data:image')) {
        base64String = base64String.substring(base64String.indexOf(',') + 1);
      }
      return base64Decode(base64String);
    } catch (e) {
      // 如果解码失败，返回一个默认的透明图片
      return Uint8List.fromList([
        0x89, 0x50, 0x4E, 0x47, 0x0D, 0x0A, 0x1A, 0x0A, 0x00, 0x00,
        0x00, 0x0D, 0x49, 0x48, 0x44, 0x52, 0x00, 0x00, 0x00, 0x01,
        0x00, 0x00, 0x00, 0x01, 0x08, 0x06, 0x00, 0x00, 0x00, 0x1F,
        0x15, 0xC4, 0x89, 0x00, 0x00, 0x00, 0x0A, 0x49, 0x44, 0x41,
        0x54, 0x78, 0x9C, 0x63, 0x00, 0x01, 0x00, 0x00, 0x05, 0x00,
        0x01, 0x0D, 0x0A, 0x2D, 0xB4, 0x00, 0x00, 0x00, 0x00, 0x49,
        0x45, 0x4E, 0x44, 0xAE, 0x42, 0x60, 0x82
      ]);
    }
  }





  void _handleLogin() async {
    // 验证表单
    if (!_formKey.currentState!.validate()) {
      return;
    }

    // 清除之前的错误信息
    setState(() {
      _errorMessage = null;
    });

    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      final Map<String, dynamic> loginData = {
        'username': _usernameController.text,
        'password': _passwordController.text,
      };

      // 如果显示了验证码且用户填写了验证码，则添加到登录数据中
      if (_showCaptcha && _captchaController.text.isNotEmpty) {
        loginData['captcha'] = _captchaController.text;
      }

      final response = await _apiService.post(
        widget.isAdminLogin ? ApiEndpoints.adminLogin : ApiEndpoints.userLogin,
        data: loginData,
      );

      if (response.statusCode == 200) {
        if (response.data['success'] == true) {
          // 登录成功
          // 这里应该保存token并跳转到主界面
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('登录成功')),
          );
          
          // 根据用户类型跳转到相应界面
          Future.delayed(const Duration(milliseconds: 1500), () {
            Navigator.pushNamedAndRemoveUntil(
              context, 
              widget.isAdminLogin ? AppRoutes.dashboard : AppRoutes.userCenter,
              (route) => false,
            );
          });
        } else {
          // 登录失败
          setState(() {
            _errorMessage = response.data['error'] ?? '登录失败';
            // 登录失败时刷新验证码
            _loadCaptcha();
            _captchaController.clear();
          });
        }
      } else {
        setState(() {
          _errorMessage = '服务器错误，请稍后重试';
          // 登录失败时刷新验证码
          _loadCaptcha();
          _captchaController.clear();
        });
      }
    } on DioException catch (e) {
      setState(() {
        _errorMessage = '网络错误，请检查网络连接';
        // 登录失败时刷新验证码
        _loadCaptcha();
        _captchaController.clear();
      });
    } catch (e) {
      setState(() {
        _errorMessage = '登录发生未知错误';
        // 登录失败时刷新验证码
        _loadCaptcha();
        _captchaController.clear();
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Scaffold(
      body: SimpleBackground(
        backgroundColor: theme.primaryColor,
        child: SafeArea(
          child: FadeTransition(
            opacity: _fadeAnimation,
            child: SlideTransition(
              position: _slideAnimation,
              child: Center(
                child: ConstrainedBox(
                  constraints: const BoxConstraints(maxWidth: 450),
                  child: SingleChildScrollView(
                    padding: const EdgeInsets.all(24.0),
                    child: Form(
                      key: _formKey,
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          // Logo和标题区域
                          _buildHeader(),
                          const SizedBox(height: 40),

                          // 登录卡片
                          _buildLoginCard(theme),
                        ],
                      ),
                    ),
                  ),
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Column(
      children: [
        // Logo容器
        Container(
          width: 120,
          height: 120,
          decoration: BoxDecoration(
            color: Colors.white.withValues(alpha: 0.15),
            shape: BoxShape.circle,
            boxShadow: [
              BoxShadow(
                color: Colors.black.withValues(alpha: 0.1),
                blurRadius: 20,
                offset: const Offset(0, 10),
              ),
            ],
          ),
          child: Icon(
            widget.isAdminLogin ? Icons.admin_panel_settings : Icons.storefront,
            size: 60,
            color: Colors.white,
          ),
        ),
        const SizedBox(height: 24),

        // 系统标题
        Text(
          '跨境电商管理系统',
          style: TextStyle(
            fontSize: 28,
            fontWeight: FontWeight.bold,
            color: Colors.white,
            shadows: [
              Shadow(
                offset: const Offset(0, 2),
                blurRadius: 4,
                color: Colors.black.withOpacity(0.3),
              ),
            ],
          ),
        ),
        const SizedBox(height: 8),

        // 登录类型标题
        Text(
          widget.isAdminLogin ? '管理员登录' : '用户登录',
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.w500,
            color: Colors.white.withValues(alpha: 0.9),
          ),
        ),
      ],
    );
  }

  Widget _buildLoginCard(ThemeData theme) {
    return Card(
      elevation: 12,
      color: Colors.white.withValues(alpha: 0.95),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(24.0),
      ),
      child: Padding(
        padding: const EdgeInsets.all(32.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            // 欢迎文本
            Text(
              '欢迎回来',
              style: TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
                color: theme.primaryColor,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 8),
            Text(
              '请输入您的登录信息',
              style: TextStyle(
                fontSize: 16,
                color: Colors.grey[600],
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 32),

            // 用户名输入框
            _buildUsernameField(),
            const SizedBox(height: 20),

            // 密码输入框
            _buildPasswordField(),
            const SizedBox(height: 20),

            // 验证码区域
            if (_showCaptcha) ...[
              _buildCaptchaField(),
              const SizedBox(height: 20),
            ],

            // 错误信息显示
            if (_errorMessage != null) ...[
              _buildErrorMessage(),
              const SizedBox(height: 20),
            ],

            // 登录按钮
            _buildLoginButton(),
            const SizedBox(height: 24),

            // 注册链接
            _buildRegisterLink(),
          ],
        ),
      ),
    );
  }

  Widget _buildUsernameField() {
    return TextFormField(
      controller: _usernameController,
      decoration: InputDecoration(
        labelText: '用户名',
        hintText: '请输入用户名',
        prefixIcon: const Icon(Icons.person_outline),
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide(
            color: Theme.of(context).primaryColor,
            width: 2,
          ),
        ),
        filled: true,
        fillColor: Colors.grey[50],
      ),
      validator: (value) {
        if (value == null || value.isEmpty) {
          return '请输入用户名';
        }
        return null;
      },
    );
  }

  Widget _buildPasswordField() {
    return TextFormField(
      controller: _passwordController,
      obscureText: _obscurePassword,
      decoration: InputDecoration(
        labelText: '密码',
        hintText: '请输入密码',
        prefixIcon: const Icon(Icons.lock_outline),
        suffixIcon: IconButton(
          icon: Icon(
            _obscurePassword ? Icons.visibility_off : Icons.visibility,
          ),
          onPressed: () {
            setState(() {
              _obscurePassword = !_obscurePassword;
            });
          },
        ),
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide(
            color: Theme.of(context).primaryColor,
            width: 2,
          ),
        ),
        filled: true,
        fillColor: Colors.grey[50],
      ),
      validator: (value) {
        if (value == null || value.isEmpty) {
          return '请输入密码';
        }
        if (value.length < 6) {
          return '密码长度至少6位';
        }
        return null;
      },
    );
  }

  Widget _buildCaptchaField() {
    return Row(
      children: [
        Expanded(
          flex: 2,
          child: TextFormField(
            controller: _captchaController,
            decoration: InputDecoration(
              labelText: '验证码',
              hintText: '请输入验证码',
              prefixIcon: const Icon(Icons.security),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
              ),
              focusedBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
                borderSide: BorderSide(
                  color: Theme.of(context).primaryColor,
                  width: 2,
                ),
              ),
              filled: true,
              fillColor: Colors.grey[50],
            ),
            validator: (value) {
              if (_showCaptcha && (value == null || value.isEmpty)) {
                return '请输入验证码';
              }
              return null;
            },
          ),
        ),
        const SizedBox(width: 12),
        Expanded(
          flex: 1,
          child: GestureDetector(
            onTap: _loadCaptcha,
            child: Container(
              height: 56,
              decoration: BoxDecoration(
                border: Border.all(color: Colors.grey[300]!),
                borderRadius: BorderRadius.circular(12),
                color: Colors.white,
              ),
              child: _captchaLoading
                  ? const Center(
                      child: SizedBox(
                        width: 20,
                        height: 20,
                        child: CircularProgressIndicator(strokeWidth: 2),
                      ),
                    )
                  : _captchaImage != null
                      ? ClipRRect(
                          borderRadius: BorderRadius.circular(12),
                          child: Image.memory(
                            _decodeBase64Image(_captchaImage!),
                            fit: BoxFit.cover,
                          ),
                        )
                      : const Center(
                          child: Text(
                            '点击刷新',
                            style: TextStyle(
                              fontSize: 12,
                              color: Colors.grey,
                            ),
                            textAlign: TextAlign.center,
                          ),
                        ),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildErrorMessage() {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.red[50],
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.red[200]!),
      ),
      child: Row(
        children: [
          Icon(Icons.error_outline, color: Colors.red[600], size: 20),
          const SizedBox(width: 8),
          Expanded(
            child: Text(
              _errorMessage!,
              style: TextStyle(
                color: Colors.red[700],
                fontSize: 14,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildLoginButton() {
    return AnimatedButton(
      text: '登录',
      onPressed: _isLoading ? null : _handleLogin,
      loading: _isLoading,
      height: 56,
      borderRadius: BorderRadius.circular(12),
      icon: Icons.login,
    );
  }

  Widget _buildRegisterLink() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Text(
          '还没有账户？',
          style: TextStyle(
            color: Colors.grey[600],
            fontSize: 14,
          ),
        ),
        TextButton(
          onPressed: () {
            Navigator.pushNamed(
              context,
              AppRoutes.register,
              arguments: {'isAdmin': widget.isAdminLogin},
            );
          },
          child: Text(
            '立即注册',
            style: TextStyle(
              color: Theme.of(context).primaryColor,
              fontWeight: FontWeight.w600,
              fontSize: 14,
            ),
          ),
        ),
      ],
    );
  }
}