import 'dart:async';
import 'dart:typed_data';
import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:dio/dio.dart';
import '../../../core/network/api_service.dart';
import '../../../core/network/api_endpoints.dart';
import '../../../shared/widgets/simple_background.dart';
import '../../../shared/routes/app_routes.dart';

class UnifiedLoginScreen extends StatefulWidget {
  final bool isAdminLogin;

  const UnifiedLoginScreen({Key? key, this.isAdminLogin = true}) : super(key: key);

  @override
  State<UnifiedLoginScreen> createState() => _UnifiedLoginScreenState();
}

class _UnifiedLoginScreenState extends State<UnifiedLoginScreen> {
  final TextEditingController _usernameController = TextEditingController();
  final TextEditingController _passwordController = TextEditingController();
  final TextEditingController _captchaController = TextEditingController();
  final ApiService _apiService = ApiService();
  
  bool _isLoading = false;
  String? _errorMessage;
  bool _obscurePassword = true;
  String? _captchaImage;
  bool _showCaptcha = false;
  bool _captchaLoading = false;

  @override
  void initState() {
    super.initState();
    // 延迟加载验证码，确保界面先显示
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _loadCaptcha();
    });
  }

  Future<void> _loadCaptcha() async {
    if (_captchaLoading) return;
    
    setState(() {
      _captchaLoading = true;
      _errorMessage = null; // 清除之前的错误信息
    });
    
    try {
      final response = await _apiService.get(ApiEndpoints.getCaptcha);
      
      if (response.statusCode == 200 && response.data['success'] == true) {
        setState(() {
          _captchaImage = response.data['image'] as String?;
          _showCaptcha = true;
        });
      } else {
        // 即使获取失败也显示验证码区域，允许用户手动刷新
        setState(() {
          _showCaptcha = true;
        });
      }
    } on DioException catch (e) {
      debugPrint('获取验证码失败: ${e.message}');
      // 网络错误时仍然显示验证码区域
      setState(() {
        _showCaptcha = true;
      });
    } catch (e) {
      debugPrint('获取验证码异常: $e');
      // 其他错误时仍然显示验证码区域
      setState(() {
        _showCaptcha = true;
      });
    } finally {
      setState(() {
        _captchaLoading = false;
      });
    }
  }

  /// 解码base64图片数据，添加错误处理
  Uint8List _decodeBase64Image(String base64String) {
    try {
      // 移除可能存在的data URI前缀
      if (base64String.startsWith('data:image')) {
        base64String = base64String.substring(base64String.indexOf(',') + 1);
      }
      return base64Decode(base64String);
    } catch (e) {
      // 如果解码失败，返回一个默认的透明图片
      return Uint8List.fromList([
        0x89, 0x50, 0x4E, 0x47, 0x0D, 0x0A, 0x1A, 0x0A, 0x00, 0x00,
        0x00, 0x0D, 0x49, 0x48, 0x44, 0x52, 0x00, 0x00, 0x00, 0x01,
        0x00, 0x00, 0x00, 0x01, 0x08, 0x06, 0x00, 0x00, 0x00, 0x1F,
        0x15, 0xC4, 0x89, 0x00, 0x00, 0x00, 0x0A, 0x49, 0x44, 0x41,
        0x54, 0x78, 0x9C, 0x63, 0x00, 0x01, 0x00, 0x00, 0x05, 0x00,
        0x01, 0x0D, 0x0A, 0x2D, 0xB4, 0x00, 0x00, 0x00, 0x00, 0x49,
        0x45, 0x4E, 0x44, 0xAE, 0x42, 0x60, 0x82
      ]);
    }
  }

  // 模拟后端验证码获取
  Future<Map<String, dynamic>> _mockGetCaptcha() async {
    // 模拟网络延迟
    await Future.delayed(const Duration(milliseconds: 300));
    
    // 返回模拟的验证码图片数据（base64格式）
    // 这是一个简单的模拟，实际应该从后端获取
    return {
      'success': true,
      'image': 'iVBORw0KGgoAAAANSUhEUgAAAKAAAACgAQMAAADXFxrrAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAGUExURQAAAP///6XZn90AAAAJcEhZcwAADsMAAA7DAcdvqGQAAAA9SURBVFjD7cExAQAAAMKg9U9tDjcgAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA4EhXaAAB+zd6UwAAAABJRU5ErkJggg=='
    };
  }

  @override
  void dispose() {
    _usernameController.dispose();
    _passwordController.dispose();
    _captchaController.dispose();
    super.dispose();
  }

  void _handleLogin() async {
    if (_usernameController.text.isEmpty || _passwordController.text.isEmpty) {
      setState(() {
        _errorMessage = '请输入用户名和密码';
      });
      return;
    }

    // 如果显示了验证码，则必须填写
    if (_showCaptcha && _captchaController.text.isEmpty) {
      setState(() {
        _errorMessage = '请输入验证码';
      });
      return;
    }

    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      final Map<String, dynamic> loginData = {
        'username': _usernameController.text,
        'password': _passwordController.text,
      };

      // 如果显示了验证码且用户填写了验证码，则添加到登录数据中
      if (_showCaptcha && _captchaController.text.isNotEmpty) {
        loginData['captcha'] = _captchaController.text;
      }

      final response = await _apiService.post(
        widget.isAdminLogin ? ApiEndpoints.adminLogin : ApiEndpoints.userLogin,
        data: loginData,
      );

      if (response.statusCode == 200) {
        if (response.data['success'] == true) {
          // 登录成功
          // 这里应该保存token并跳转到主界面
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('登录成功')),
          );
          
          // 根据用户类型跳转到相应界面
          Future.delayed(const Duration(milliseconds: 1500), () {
            Navigator.pushNamedAndRemoveUntil(
              context, 
              widget.isAdminLogin ? AppRoutes.dashboard : AppRoutes.userCenter,
              (route) => false,
            );
          });
        } else {
          // 登录失败
          setState(() {
            _errorMessage = response.data['error'] ?? '登录失败';
            // 登录失败时刷新验证码
            _loadCaptcha();
            _captchaController.clear();
          });
        }
      } else {
        setState(() {
          _errorMessage = '服务器错误，请稍后重试';
          // 登录失败时刷新验证码
          _loadCaptcha();
          _captchaController.clear();
        });
      }
    } on DioException catch (e) {
      setState(() {
        _errorMessage = '网络错误，请检查网络连接';
        // 登录失败时刷新验证码
        _loadCaptcha();
        _captchaController.clear();
      });
    } catch (e) {
      setState(() {
        _errorMessage = '登录发生未知错误';
        // 登录失败时刷新验证码
        _loadCaptcha();
        _captchaController.clear();
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('系统登录'),
        backgroundColor: Colors.blue,
        foregroundColor: Colors.white,
      ),
      body: SimpleBackground(
        backgroundColor: Colors.blue,
        child: SafeArea(
          child: Center(
            child: ConstrainedBox(
              constraints: const BoxConstraints(maxWidth: 500),
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Card(
                  color: Colors.white.withOpacity(0.9),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(20.0),
                  ),
                  child: Padding(
                    padding: const EdgeInsets.all(24.0),
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        const Icon(
                          Icons.lock,
                          size: 60,
                          color: Colors.blue,
                        ),
                        const SizedBox(height: 16),
                        const Text(
                          '用户登录',
                          style: TextStyle(
                            fontSize: 24,
                            fontWeight: FontWeight.bold,
                            color: Colors.blue,
                          ),
                        ),
                        const SizedBox(height: 32),
                        
                        TextField(
                          controller: _usernameController,
                          style: const TextStyle(color: Colors.black), // 设置输入文字颜色为黑色
                          decoration: const InputDecoration(
                            labelText: '用户名',
                            border: OutlineInputBorder(),
                          ),
                        ),
                        const SizedBox(height: 16),
                        
                        TextField(
                          controller: _passwordController,
                          obscureText: _obscurePassword,
                          style: const TextStyle(color: Colors.black), // 设置输入文字颜色为黑色
                          decoration: InputDecoration(
                            labelText: '密码',
                            border: const OutlineInputBorder(),
                            suffixIcon: IconButton(
                              icon: Icon(
                                _obscurePassword 
                                  ? Icons.visibility_off 
                                  : Icons.visibility,
                              ),
                              onPressed: () {
                                setState(() {
                                  _obscurePassword = !_obscurePassword;
                                });
                              },
                            ),
                          ),
                        ),
                        const SizedBox(height: 16),
                        
                        // 只有在需要时才显示验证码区域
                        if (_showCaptcha) ...[
                          Row(
                            children: [
                              Expanded(
                                child: TextField(
                                  controller: _captchaController,
                                  style: const TextStyle(color: Colors.black), // 设置输入文字颜色为黑色
                                  decoration: const InputDecoration(
                                    labelText: '验证码',
                                    border: OutlineInputBorder(),
                                  ),
                                ),
                              ),
                              const SizedBox(width: 16),
                              GestureDetector(
                                onTap: _loadCaptcha,
                                child: Container(
                                  width: 100,
                                  height: 40,
                                  decoration: BoxDecoration(
                                    border: Border.all(color: Colors.grey),
                                    color: Colors.white, // 明确设置背景色
                                  ),
                                  child: _captchaLoading
                                      ? const Center(
                                          child: SizedBox(
                                            width: 20,
                                            height: 20,
                                            child: CircularProgressIndicator(
                                              strokeWidth: 2,
                                              valueColor: AlwaysStoppedAnimation<Color>(Colors.blue),
                                            ),
                                          ),
                                        )
                                      : _captchaImage != null
                                          ? Image.memory(
                                              _decodeBase64Image(_captchaImage!),
                                              fit: BoxFit.contain,
                                            )
                                          : const Center(
                                              child: Text(
                                                '刷新',
                                                style: TextStyle(color: Colors.black), // 确保文字可见
                                                textAlign: TextAlign.center,
                                              ),
                                            ),
                                ),
                              ),
                            ],
                          ),
                          const SizedBox(height: 16),
                        ],
                        
                        SizedBox(
                          width: double.infinity,
                          height: 48,
                          child: ElevatedButton(
                            onPressed: _isLoading ? null : _handleLogin,
                            child: _isLoading
                                ? const CircularProgressIndicator()
                                : const Text('登录'),
                          ),
                        ),
                        
                        // 添加注册链接
                        Padding(
                          padding: const EdgeInsets.only(top: 16),
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              const Text('还没有账户？'),
                              TextButton(
                                onPressed: () {
                                  Navigator.pushNamed(
                                    context,
                                    AppRoutes.register,
                                    arguments: {'isAdmin': widget.isAdminLogin},
                                  );
                                },
                                child: const Text('立即注册'),
                              ),
                            ],
                          ),
                        ),
                        
                        if (_errorMessage != null)
                          Padding(
                            padding: const EdgeInsets.only(top: 16),
                            child: Text(
                              _errorMessage!,
                              style: const TextStyle(color: Colors.red),
                              textAlign: TextAlign.center,
                            ),
                          ),
                      ],
                    ),
                  ),
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }
}