import 'package:flutter/material.dart';
import 'package:plb_kj_admin/shared/widgets/custom_card.dart';
import 'package:plb_kj_admin/shared/widgets/custom_list_tile.dart';
import 'package:plb_kj_admin/shared/widgets/error_display.dart';
import 'package:plb_kj_admin/shared/widgets/loading_indicator.dart';
import '../customer_service.dart';

class CustomersScreen extends StatefulWidget {
  const CustomersScreen({Key? key}) : super(key: key);

  @override
  State<CustomersScreen> createState() => _CustomersScreenState();
}

class _CustomersScreenState extends State<CustomersScreen> {
  late Future<List<Customer>> _customersFuture;
  String _error = '';

  @override
  void initState() {
    super.initState();
    _loadCustomers();
  }

  void _loadCustomers() {
    setState(() {
      _error = '';
    });
    _customersFuture = CustomerService.getCustomers().catchError((error) {
      setState(() {
        _error = error.toString();
      });
      return <Customer>[];
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('客户管理'),
        actions: [
          IconButton(
            icon: const Icon(Icons.add),
            onPressed: () {
              // Navigate to add customer screen
              // Navigator.push(context, MaterialPageRoute(builder: (context) => AddCustomerScreen()));
            },
          ),
        ],
      ),
      body: RefreshIndicator(
        onRefresh: () async {
          _loadCustomers();
          await _customersFuture;
        },
        child: FutureBuilder<List<Customer>>(
          future: _customersFuture,
          builder: (context, snapshot) {
            if (snapshot.connectionState == ConnectionState.waiting) {
              return const LoadingIndicator();
            }

            if (snapshot.hasError || _error.isNotEmpty) {
              return ErrorDisplay(
                message: _error.isNotEmpty ? _error : snapshot.error.toString(),
                onRetry: _loadCustomers,
              );
            }

            final customers = snapshot.data ?? [];
            if (customers.isEmpty) {
              return const Center(
                child: Text('暂无客户数据'),
              );
            }

            return ListView.builder(
              itemCount: customers.length,
              itemBuilder: (context, index) {
                final customer = customers[index];
                return CustomCard(
                  child: CustomListTile(
                    leading: CircleAvatar(
                      backgroundColor: Colors.grey[200],
                      child: Text(
                        customer.name.substring(0, 1),
                        style: const TextStyle(color: Colors.black),
                      ),
                    ),
                    title: Text(customer.name),
                    subtitle: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(customer.email),
                        Text('${customer.city}, ${customer.country}'),
                      ],
                    ),
                    trailing: IconButton(
                      icon: const Icon(Icons.edit),
                      onPressed: () {
                        // Navigate to edit customer screen
                        // Navigator.push(context, MaterialPageRoute(builder: (context) => EditCustomerScreen(customer: customer)));
                      },
                    ),
                    onTap: () {
                      // Navigate to customer detail screen
                      // Navigator.push(context, MaterialPageRoute(builder: (context) => CustomerDetailScreen(customer: customer)));
                    },
                  ),
                );
              },
            );
          },
        ),
      ),
    );
  }
}