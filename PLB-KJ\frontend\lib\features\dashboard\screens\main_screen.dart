import 'package:flutter/material.dart';
import '../widgets/bottom_nav_bar.dart';
import '../../../shared/widgets/breadcrumb.dart';

// 屏幕导入
import '../../product_management/screens/products_screen.dart';
import '../../order_management/screens/orders_screen.dart';
import '../../inventory_management/screens/inventory_screen.dart';
import '../../shipping_management/screens/shipping_screen.dart';
import '../../payment_management/screens/payments_screen.dart';
import '../../customer_management/screens/customers_screen.dart';
import '../screens/dashboard_screen.dart';
import '../../settings/screens/settings_screen.dart';

class MainScreen extends StatefulWidget {
  const MainScreen({Key? key}) : super(key: key);

  @override
  State<MainScreen> createState() => _MainScreenState();
}

class _MainScreenState extends State<MainScreen> {
  int _currentIndex = 0;

  final List<Widget> _screens = [
    const DashboardScreen(),
    const ProductsScreen(),
    const OrdersScreen(),
    const InventoryScreen(),
    const ShippingScreen(),
    const PaymentsScreen(),
    const CustomersScreen(),
    const SettingsScreen(),
  ];

  final List<String> _screenTitles = [
    '仪表盘',
    '商品管理',
    '订单管理',
    '库存管理',
    '物流管理',
    '支付管理',
    '客户管理',
    '设置',
  ];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Column(
        children: [
          Breadcrumb(
            items: [
              BreadcrumbItem(title: '首页'),
              BreadcrumbItem(title: _screenTitles[_currentIndex]),
            ],
          ),
          Expanded(
            child: _screens[_currentIndex],
          ),
        ],
      ),
      bottomNavigationBar: BottomNavBar(
        currentIndex: _currentIndex,
        onTap: (index) {
          setState(() {
            _currentIndex = index;
          });
        },
      ),
    );
  }
}