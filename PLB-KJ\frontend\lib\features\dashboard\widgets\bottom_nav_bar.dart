import 'package:flutter/material.dart';

class BottomNavBar extends StatelessWidget {
  final int currentIndex;
  final Function(int) onTap;

  const BottomNavBar({
    Key? key,
    required this.currentIndex,
    required this.onTap,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return BottomNavigationBar(
      type: BottomNavigationBarType.fixed,
      currentIndex: currentIndex,
      onTap: onTap,
      items: const [
        BottomNavigationBarItem(
          icon: Icon(Icons.dashboard),
          label: '仪表盘',
        ),
        BottomNavigationBarItem(
          icon: Icon(Icons.shopping_cart),
          label: '商品',
        ),
        BottomNavigationBarItem(
          icon: Icon(Icons.receipt),
          label: '订单',
        ),
        BottomNavigationBarItem(
          icon: Icon(Icons.inventory),
          label: '库存',
        ),
        BottomNavigationBarItem(
          icon: Icon(Icons.local_shipping),
          label: '物流',
        ),
        BottomNavigationBarItem(
          icon: Icon(Icons.payment),
          label: '支付',
        ),
        BottomNavigationBarItem(
          icon: Icon(Icons.group),
          label: '客户',
        ),
        BottomNavigationBarItem(
          icon: Icon(Icons.settings),
          label: '设置',
        ),
      ],
    );
  }
}