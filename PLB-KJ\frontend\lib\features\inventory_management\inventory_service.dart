import 'package:flutter/foundation.dart';

class Inventory {
  final int id;
  final int productId;
  final int quantity;
  final String location;
  final DateTime lastUpdated;

  Inventory({
    required this.id,
    required this.productId,
    required this.quantity,
    required this.location,
    required this.lastUpdated,
  });

  factory Inventory.fromJson(Map<String, dynamic> json) {
    return Inventory(
      id: json['id'] as int,
      productId: json['product_id'] as int,
      quantity: json['quantity'] as int,
      location: json['location'] as String,
      lastUpdated: DateTime.parse(json['last_updated'] as String),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'product_id': productId,
      'quantity': quantity,
      'location': location,
      'last_updated': lastUpdated.toIso8601String(),
    };
  }
}

class InventoryService {
  // Mock data for demonstration
  static final List<Inventory> _inventories = [
    Inventory(
      id: 1,
      productId: 1,
      quantity: 50,
      location: 'Warehouse A',
      lastUpdated: DateTime.now().subtract(const Duration(days: 1)),
    ),
    Inventory(
      id: 2,
      productId: 2,
      quantity: 30,
      location: 'Warehouse B',
      lastUpdated: DateTime.now().subtract(const Duration(days: 2)),
    ),
  ];

  static Future<List<Inventory>> getInventoryItems() async {
    // Simulate network delay
    await Future.delayed(const Duration(milliseconds: 500));
    return _inventories;
  }

  static Future<Inventory?> getInventoryByProductId(int productId) async {
    // Simulate network delay
    await Future.delayed(const Duration(milliseconds: 500));
    try {
      return _inventories.firstWhere((inventory) => inventory.productId == productId);
    } catch (e) {
      return null;
    }
  }

  static Future<Inventory> updateInventory(Inventory inventory) async {
    // Simulate network delay
    await Future.delayed(const Duration(milliseconds: 500));
    // In a real app, this would make an API call
    final index = _inventories.indexWhere((i) => i.id == inventory.id);
    if (index != -1) {
      _inventories[index] = inventory.copyWith(lastUpdated: DateTime.now());
      return _inventories[index];
    }
    
    // If inventory item doesn't exist, create a new one
    final newInventory = Inventory(
      id: _inventories.length + 1,
      productId: inventory.productId,
      quantity: inventory.quantity,
      location: inventory.location,
      lastUpdated: DateTime.now(),
    );
    _inventories.add(newInventory);
    return newInventory;
  }

  static Future<void> adjustInventory(int productId, int quantityChange) async {
    // Simulate network delay
    await Future.delayed(const Duration(milliseconds: 500));
    // In a real app, this would make an API call
    final index = _inventories.indexWhere((i) => i.productId == productId);
    if (index != -1) {
      _inventories[index] = _inventories[index].copyWith(
        quantity: _inventories[index].quantity + quantityChange,
        lastUpdated: DateTime.now(),
      );
    } else {
      throw Exception('Inventory item not found for product ID: $productId');
    }
  }
}

// Extension to help with copying inventory items with updated fields
extension InventoryCopyWith on Inventory {
  Inventory copyWith({
    int? id,
    int? productId,
    int? quantity,
    String? location,
    DateTime? lastUpdated,
  }) {
    return Inventory(
      id: id ?? this.id,
      productId: productId ?? this.productId,
      quantity: quantity ?? this.quantity,
      location: location ?? this.location,
      lastUpdated: lastUpdated ?? this.lastUpdated,
    );
  }
}