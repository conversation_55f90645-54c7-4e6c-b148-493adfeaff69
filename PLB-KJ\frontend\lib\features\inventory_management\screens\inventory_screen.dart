import 'package:flutter/material.dart';
import 'package:plb_kj_admin/shared/widgets/custom_card.dart';
import 'package:plb_kj_admin/shared/widgets/custom_list_tile.dart';
import 'package:plb_kj_admin/shared/widgets/error_display.dart';
import 'package:plb_kj_admin/shared/widgets/loading_indicator.dart';
import '../inventory_service.dart';

class InventoryScreen extends StatefulWidget {
  const InventoryScreen({Key? key}) : super(key: key);

  @override
  State<InventoryScreen> createState() => _InventoryScreenState();
}

class _InventoryScreenState extends State<InventoryScreen> {
  late Future<List<Inventory>> _inventoryFuture;
  String _error = '';

  @override
  void initState() {
    super.initState();
    _loadInventory();
  }

  void _loadInventory() {
    setState(() {
      _error = '';
    });
    _inventoryFuture = InventoryService.getInventoryItems().catchError((error) {
      setState(() {
        _error = error.toString();
      });
      return <Inventory>[];
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('库存管理'),
      ),
      body: RefreshIndicator(
        onRefresh: () async {
          _loadInventory();
          await _inventoryFuture;
        },
        child: FutureBuilder<List<Inventory>>(
          future: _inventoryFuture,
          builder: (context, snapshot) {
            if (snapshot.connectionState == ConnectionState.waiting) {
              return const LoadingIndicator();
            }

            if (snapshot.hasError || _error.isNotEmpty) {
              return ErrorDisplay(
                message: _error.isNotEmpty ? _error : snapshot.error.toString(),
                onRetry: _loadInventory,
              );
            }

            final inventoryItems = snapshot.data ?? [];
            if (inventoryItems.isEmpty) {
              return const Center(
                child: Text('暂无库存数据'),
              );
            }

            return ListView.builder(
              itemCount: inventoryItems.length,
              itemBuilder: (context, index) {
                final inventory = inventoryItems[index];
                return CustomCard(
                  child: CustomListTile(
                    title: Text('商品ID: ${inventory.productId}'),
                    subtitle: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text('数量: ${inventory.quantity}'),
                        Text('位置: ${inventory.location}'),
                        Text('最后更新: ${inventory.lastUpdated.toString()}'),
                      ],
                    ),
                    trailing: IconButton(
                      icon: const Icon(Icons.edit),
                      onPressed: () {
                        // Navigate to edit inventory screen
                        // Navigator.push(context, MaterialPageRoute(builder: (context) => EditInventoryScreen(inventory: inventory)));
                      },
                    ),
                  ),
                );
              },
            );
          },
        ),
      ),
    );
  }
}