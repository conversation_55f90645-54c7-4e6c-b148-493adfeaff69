import 'package:flutter/foundation.dart';

class Order {
  final int id;
  final String orderNumber;
  final int customerId;
  final int currencyId;
  final double subtotal;
  final double taxAmount;
  final double shippingAmount;
  final double discountAmount;
  final double totalAmount;
  final String status;
  final String paymentStatus;
  final String shippingAddress;
  final String billingAddress;
  final String notes;
  final DateTime createdAt;
  final DateTime updatedAt;

  Order({
    required this.id,
    required this.orderNumber,
    required this.customerId,
    required this.currencyId,
    required this.subtotal,
    required this.taxAmount,
    required this.shippingAmount,
    required this.discountAmount,
    required this.totalAmount,
    required this.status,
    required this.paymentStatus,
    required this.shippingAddress,
    required this.billingAddress,
    required this.notes,
    required this.createdAt,
    required this.updatedAt,
  });

  factory Order.fromJson(Map<String, dynamic> json) {
    return Order(
      id: json['id'] as int,
      orderNumber: json['order_number'] as String,
      customerId: json['customer_id'] as int,
      currencyId: json['currency_id'] as int,
      subtotal: (json['subtotal'] as num).toDouble(),
      taxAmount: (json['tax_amount'] as num).toDouble(),
      shippingAmount: (json['shipping_amount'] as num).toDouble(),
      discountAmount: (json['discount_amount'] as num).toDouble(),
      totalAmount: (json['total_amount'] as num).toDouble(),
      status: json['status'] as String,
      paymentStatus: json['payment_status'] as String,
      shippingAddress: json['shipping_address'] as String,
      billingAddress: json['billing_address'] as String,
      notes: json['notes'] as String,
      createdAt: DateTime.parse(json['created_at'] as String),
      updatedAt: DateTime.parse(json['updated_at'] as String),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'order_number': orderNumber,
      'customer_id': customerId,
      'currency_id': currencyId,
      'subtotal': subtotal,
      'tax_amount': taxAmount,
      'shipping_amount': shippingAmount,
      'discount_amount': discountAmount,
      'total_amount': totalAmount,
      'status': status,
      'payment_status': paymentStatus,
      'shipping_address': shippingAddress,
      'billing_address': billingAddress,
      'notes': notes,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }
}

class OrderItem {
  final int id;
  final int orderId;
  final int productId;
  final int quantity;
  final double unitPrice;
  final double totalPrice;
  final DateTime createdAt;

  OrderItem({
    required this.id,
    required this.orderId,
    required this.productId,
    required this.quantity,
    required this.unitPrice,
    required this.totalPrice,
    required this.createdAt,
  });

  factory OrderItem.fromJson(Map<String, dynamic> json) {
    return OrderItem(
      id: json['id'] as int,
      orderId: json['order_id'] as int,
      productId: json['product_id'] as int,
      quantity: json['quantity'] as int,
      unitPrice: (json['unit_price'] as num).toDouble(),
      totalPrice: (json['total_price'] as num).toDouble(),
      createdAt: DateTime.parse(json['created_at'] as String),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'order_id': orderId,
      'product_id': productId,
      'quantity': quantity,
      'unit_price': unitPrice,
      'total_price': totalPrice,
      'created_at': createdAt.toIso8601String(),
    };
  }
}

class OrderService {
  // Mock data for demonstration
  static final List<Order> _orders = [
    Order(
      id: 1,
      orderNumber: 'ORD-001',
      customerId: 1,
      currencyId: 1,
      subtotal: 999.99,
      taxAmount: 89.99,
      shippingAmount: 29.99,
      discountAmount: 0.0,
      totalAmount: 1119.97,
      status: 'processing',
      paymentStatus: 'paid',
      shippingAddress: '123 Main St, New York, NY 10001',
      billingAddress: '123 Main St, New York, NY 10001',
      notes: 'Fragile item',
      createdAt: DateTime.now().subtract(const Duration(days: 5)),
      updatedAt: DateTime.now().subtract(const Duration(days: 2)),
    ),
    Order(
      id: 2,
      orderNumber: 'ORD-002',
      customerId: 2,
      currencyId: 1,
      subtotal: 899.99,
      taxAmount: 80.99,
      shippingAmount: 29.99,
      discountAmount: 0.0,
      totalAmount: 1010.97,
      status: 'shipped',
      paymentStatus: 'paid',
      shippingAddress: '456 Park Ave, Los Angeles, CA 90001',
      billingAddress: '456 Park Ave, Los Angeles, CA 90001',
      notes: '',
      createdAt: DateTime.now().subtract(const Duration(days: 3)),
      updatedAt: DateTime.now().subtract(const Duration(days: 1)),
    ),
  ];

  static final List<OrderItem> _orderItems = [
    OrderItem(
      id: 1,
      orderId: 1,
      productId: 1,
      quantity: 1,
      unitPrice: 999.99,
      totalPrice: 999.99,
      createdAt: DateTime.now().subtract(const Duration(days: 5)),
    ),
    OrderItem(
      id: 2,
      orderId: 2,
      productId: 2,
      quantity: 1,
      unitPrice: 899.99,
      totalPrice: 899.99,
      createdAt: DateTime.now().subtract(const Duration(days: 3)),
    ),
  ];

  static Future<List<Order>> getOrders() async {
    // Simulate network delay
    await Future.delayed(const Duration(milliseconds: 500));
    return _orders;
  }

  static Future<Order?> getOrderById(int id) async {
    // Simulate network delay
    await Future.delayed(const Duration(milliseconds: 500));
    try {
      return _orders.firstWhere((order) => order.id == id);
    } catch (e) {
      return null;
    }
  }

  static Future<List<OrderItem>> getOrderItemsByOrderId(int orderId) async {
    // Simulate network delay
    await Future.delayed(const Duration(milliseconds: 500));
    return _orderItems.where((item) => item.orderId == orderId).toList();
  }

  static Future<Order> createOrder(Order order) async {
    // Simulate network delay
    await Future.delayed(const Duration(milliseconds: 500));
    // In a real app, this would make an API call
    final newOrder = Order(
      id: _orders.length + 1,
      orderNumber: order.orderNumber,
      customerId: order.customerId,
      currencyId: order.currencyId,
      subtotal: order.subtotal,
      taxAmount: order.taxAmount,
      shippingAmount: order.shippingAmount,
      discountAmount: order.discountAmount,
      totalAmount: order.totalAmount,
      status: order.status,
      paymentStatus: order.paymentStatus,
      shippingAddress: order.shippingAddress,
      billingAddress: order.billingAddress,
      notes: order.notes,
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
    );
    _orders.add(newOrder);
    return newOrder;
  }

  static Future<Order> updateOrder(Order order) async {
    // Simulate network delay
    await Future.delayed(const Duration(milliseconds: 500));
    // In a real app, this would make an API call
    final index = _orders.indexWhere((o) => o.id == order.id);
    if (index != -1) {
      _orders[index] = order.copyWith(updatedAt: DateTime.now());
      return _orders[index];
    }
    throw Exception('Order not found');
  }

  static Future<void> deleteOrder(int id) async {
    // Simulate network delay
    await Future.delayed(const Duration(milliseconds: 500));
    // In a real app, this would make an API call
    _orders.removeWhere((order) => order.id == id);
  }
}

// Extension to help with copying orders with updated fields
extension OrderCopyWith on Order {
  Order copyWith({
    int? id,
    String? orderNumber,
    int? customerId,
    int? currencyId,
    double? subtotal,
    double? taxAmount,
    double? shippingAmount,
    double? discountAmount,
    double? totalAmount,
    String? status,
    String? paymentStatus,
    String? shippingAddress,
    String? billingAddress,
    String? notes,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return Order(
      id: id ?? this.id,
      orderNumber: orderNumber ?? this.orderNumber,
      customerId: customerId ?? this.customerId,
      currencyId: currencyId ?? this.currencyId,
      subtotal: subtotal ?? this.subtotal,
      taxAmount: taxAmount ?? this.taxAmount,
      shippingAmount: shippingAmount ?? this.shippingAmount,
      discountAmount: discountAmount ?? this.discountAmount,
      totalAmount: totalAmount ?? this.totalAmount,
      status: status ?? this.status,
      paymentStatus: paymentStatus ?? this.paymentStatus,
      shippingAddress: shippingAddress ?? this.shippingAddress,
      billingAddress: billingAddress ?? this.billingAddress,
      notes: notes ?? this.notes,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }
}