import 'package:flutter/material.dart';
import 'package:plb_kj_admin/shared/widgets/custom_card.dart';
import 'package:plb_kj_admin/shared/widgets/custom_list_tile.dart';
import 'package:plb_kj_admin/shared/widgets/error_display.dart';
import 'package:plb_kj_admin/shared/widgets/loading_indicator.dart';
import '../order_service.dart';

class OrdersScreen extends StatefulWidget {
  const OrdersScreen({Key? key}) : super(key: key);

  @override
  State<OrdersScreen> createState() => _OrdersScreenState();
}

class _OrdersScreenState extends State<OrdersScreen> {
  late Future<List<Order>> _ordersFuture;
  String _error = '';

  @override
  void initState() {
    super.initState();
    _loadOrders();
  }

  void _loadOrders() {
    setState(() {
      _error = '';
    });
    _ordersFuture = OrderService.getOrders().catchError((error) {
      setState(() {
        _error = error.toString();
      });
      return <Order>[];
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('订单管理'),
      ),
      body: RefreshIndicator(
        onRefresh: () async {
          _loadOrders();
          await _ordersFuture;
        },
        child: FutureBuilder<List<Order>>(
          future: _ordersFuture,
          builder: (context, snapshot) {
            if (snapshot.connectionState == ConnectionState.waiting) {
              return const LoadingIndicator();
            }

            if (snapshot.hasError || _error.isNotEmpty) {
              return ErrorDisplay(
                message: _error.isNotEmpty ? _error : snapshot.error.toString(),
                onRetry: _loadOrders,
              );
            }

            final orders = snapshot.data ?? [];
            if (orders.isEmpty) {
              return const Center(
                child: Text('暂无订单数据'),
              );
            }

            return ListView.builder(
              itemCount: orders.length,
              itemBuilder: (context, index) {
                final order = orders[index];
                return CustomCard(
                  child: CustomListTile(
                    title: Text(order.orderNumber),
                    subtitle: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text('状态: ${order.status}'),
                        Text('支付状态: ${order.paymentStatus}'),
                        Text('总金额: ¥${order.totalAmount.toStringAsFixed(2)}'),
                      ],
                    ),
                    trailing: const Icon(Icons.arrow_forward_ios),
                    onTap: () {
                      // Navigate to order detail screen
                      // Navigator.push(context, MaterialPageRoute(builder: (context) => OrderDetailScreen(order: order)));
                    },
                  ),
                );
              },
            );
          },
        ),
      ),
    );
  }
}