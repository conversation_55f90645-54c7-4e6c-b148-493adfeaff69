import 'package:flutter/foundation.dart';

class PaymentMethod {
  final int id;
  final String name;
  final String code;
  final String description;
  final bool isActive;
  final DateTime createdAt;
  final DateTime updatedAt;

  PaymentMethod({
    required this.id,
    required this.name,
    required this.code,
    required this.description,
    required this.isActive,
    required this.createdAt,
    required this.updatedAt,
  });

  factory PaymentMethod.fromJson(Map<String, dynamic> json) {
    return PaymentMethod(
      id: json['id'] as int,
      name: json['name'] as String,
      code: json['code'] as String,
      description: json['description'] as String,
      isActive: json['is_active'] == 1,
      createdAt: DateTime.parse(json['created_at'] as String),
      updatedAt: DateTime.parse(json['updated_at'] as String),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'code': code,
      'description': description,
      'is_active': isActive ? 1 : 0,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }
}

class OrderPayment {
  final int id;
  final int orderId;
  final int paymentMethodId;
  final double amount;
  final String transactionId;
  final String paymentStatus;
  final DateTime? paymentDate;
  final String notes;
  final DateTime createdAt;
  final DateTime updatedAt;

  OrderPayment({
    required this.id,
    required this.orderId,
    required this.paymentMethodId,
    required this.amount,
    required this.transactionId,
    required this.paymentStatus,
    this.paymentDate,
    required this.notes,
    required this.createdAt,
    required this.updatedAt,
  });

  factory OrderPayment.fromJson(Map<String, dynamic> json) {
    return OrderPayment(
      id: json['id'] as int,
      orderId: json['order_id'] as int,
      paymentMethodId: json['payment_method_id'] as int,
      amount: (json['amount'] as num).toDouble(),
      transactionId: json['transaction_id'] as String,
      paymentStatus: json['payment_status'] as String,
      paymentDate: json['payment_date'] != null ? DateTime.parse(json['payment_date'] as String) : null,
      notes: json['notes'] as String,
      createdAt: DateTime.parse(json['created_at'] as String),
      updatedAt: DateTime.parse(json['updated_at'] as String),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'order_id': orderId,
      'payment_method_id': paymentMethodId,
      'amount': amount,
      'transaction_id': transactionId,
      'payment_status': paymentStatus,
      'payment_date': paymentDate?.toIso8601String(),
      'notes': notes,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }
}

class PaymentService {
  // Mock data for demonstration
  static final List<PaymentMethod> _paymentMethods = [
    PaymentMethod(
      id: 1,
      name: 'Credit Card',
      code: 'credit_card',
      description: 'Credit card payment',
      isActive: true,
      createdAt: DateTime.now().subtract(const Duration(days: 30)),
      updatedAt: DateTime.now().subtract(const Duration(days: 30)),
    ),
    PaymentMethod(
      id: 2,
      name: 'PayPal',
      code: 'paypal',
      description: 'PayPal payment',
      isActive: true,
      createdAt: DateTime.now().subtract(const Duration(days: 30)),
      updatedAt: DateTime.now().subtract(const Duration(days: 30)),
    ),
    PaymentMethod(
      id: 3,
      name: 'Bank Transfer',
      code: 'bank_transfer',
      description: 'Bank transfer payment',
      isActive: true,
      createdAt: DateTime.now().subtract(const Duration(days: 30)),
      updatedAt: DateTime.now().subtract(const Duration(days: 30)),
    ),
  ];

  static final List<OrderPayment> _orderPayments = [
    OrderPayment(
      id: 1,
      orderId: 1,
      paymentMethodId: 1,
      amount: 1119.97,
      transactionId: 'TXN-001',
      paymentStatus: 'completed',
      paymentDate: DateTime.now().subtract(const Duration(days: 5)),
      notes: '',
      createdAt: DateTime.now().subtract(const Duration(days: 5)),
      updatedAt: DateTime.now().subtract(const Duration(days: 5)),
    ),
    OrderPayment(
      id: 2,
      orderId: 2,
      paymentMethodId: 2,
      amount: 1010.97,
      transactionId: 'TXN-002',
      paymentStatus: 'completed',
      paymentDate: DateTime.now().subtract(const Duration(days: 3)),
      notes: '',
      createdAt: DateTime.now().subtract(const Duration(days: 3)),
      updatedAt: DateTime.now().subtract(const Duration(days: 3)),
    ),
  ];

  static Future<List<PaymentMethod>> getPaymentMethods() async {
    // Simulate network delay
    await Future.delayed(const Duration(milliseconds: 500));
    return _paymentMethods;
  }

  static Future<List<OrderPayment>> getOrderPayments() async {
    // Simulate network delay
    await Future.delayed(const Duration(milliseconds: 500));
    return _orderPayments;
  }

  static Future<PaymentMethod?> getPaymentMethodById(int id) async {
    // Simulate network delay
    await Future.delayed(const Duration(milliseconds: 500));
    try {
      return _paymentMethods.firstWhere((method) => method.id == id);
    } catch (e) {
      return null;
    }
  }

  static Future<OrderPayment?> getOrderPaymentByOrderId(int orderId) async {
    // Simulate network delay
    await Future.delayed(const Duration(milliseconds: 500));
    try {
      return _orderPayments.firstWhere((payment) => payment.orderId == orderId);
    } catch (e) {
      return null;
    }
  }

  static Future<OrderPayment> createOrderPayment(OrderPayment orderPayment) async {
    // Simulate network delay
    await Future.delayed(const Duration(milliseconds: 500));
    // In a real app, this would make an API call
    final newOrderPayment = OrderPayment(
      id: _orderPayments.length + 1,
      orderId: orderPayment.orderId,
      paymentMethodId: orderPayment.paymentMethodId,
      amount: orderPayment.amount,
      transactionId: orderPayment.transactionId,
      paymentStatus: orderPayment.paymentStatus,
      paymentDate: orderPayment.paymentDate,
      notes: orderPayment.notes,
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
    );
    _orderPayments.add(newOrderPayment);
    return newOrderPayment;
  }

  static Future<OrderPayment> updateOrderPayment(OrderPayment orderPayment) async {
    // Simulate network delay
    await Future.delayed(const Duration(milliseconds: 500));
    // In a real app, this would make an API call
    final index = _orderPayments.indexWhere((p) => p.id == orderPayment.id);
    if (index != -1) {
      _orderPayments[index] = orderPayment.copyWith(updatedAt: DateTime.now());
      return _orderPayments[index];
    }
    throw Exception('Order payment not found');
  }
}

// Extension to help with copying payment items with updated fields
extension OrderPaymentCopyWith on OrderPayment {
  OrderPayment copyWith({
    int? id,
    int? orderId,
    int? paymentMethodId,
    double? amount,
    String? transactionId,
    String? paymentStatus,
    DateTime? paymentDate,
    String? notes,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return OrderPayment(
      id: id ?? this.id,
      orderId: orderId ?? this.orderId,
      paymentMethodId: paymentMethodId ?? this.paymentMethodId,
      amount: amount ?? this.amount,
      transactionId: transactionId ?? this.transactionId,
      paymentStatus: paymentStatus ?? this.paymentStatus,
      paymentDate: paymentDate ?? this.paymentDate,
      notes: notes ?? this.notes,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }
}