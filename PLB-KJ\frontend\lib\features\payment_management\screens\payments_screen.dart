import 'package:flutter/material.dart';
import 'package:plb_kj_admin/shared/widgets/custom_card.dart';
import 'package:plb_kj_admin/shared/widgets/custom_list_tile.dart';
import 'package:plb_kj_admin/shared/widgets/error_display.dart';
import 'package:plb_kj_admin/shared/widgets/loading_indicator.dart';
import '../payment_service.dart';

class PaymentsScreen extends StatefulWidget {
  const PaymentsScreen({Key? key}) : super(key: key);

  @override
  State<PaymentsScreen> createState() => _PaymentsScreenState();
}

class _PaymentsScreenState extends State<PaymentsScreen> {
  late Future<List<OrderPayment>> _paymentsFuture;
  String _error = '';

  @override
  void initState() {
    super.initState();
    _loadPayments();
  }

  void _loadPayments() {
    setState(() {
      _error = '';
    });
    _paymentsFuture = PaymentService.getOrderPayments().catchError((error) {
      setState(() {
        _error = error.toString();
      });
      return <OrderPayment>[];
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('支付管理'),
      ),
      body: RefreshIndicator(
        onRefresh: () async {
          _loadPayments();
          await _paymentsFuture;
        },
        child: FutureBuilder<List<OrderPayment>>(
          future: _paymentsFuture,
          builder: (context, snapshot) {
            if (snapshot.connectionState == ConnectionState.waiting) {
              return const LoadingIndicator();
            }

            if (snapshot.hasError || _error.isNotEmpty) {
              return ErrorDisplay(
                message: _error.isNotEmpty ? _error : snapshot.error.toString(),
                onRetry: _loadPayments,
              );
            }

            final payments = snapshot.data ?? [];
            if (payments.isEmpty) {
              return const Center(
                child: Text('暂无支付数据'),
              );
            }

            return ListView.builder(
              itemCount: payments.length,
              itemBuilder: (context, index) {
                final payment = payments[index];
                return CustomCard(
                  child: CustomListTile(
                    title: Text('订单ID: ${payment.orderId}'),
                    subtitle: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text('支付方式ID: ${payment.paymentMethodId}'),
                        Text('金额: ¥${payment.amount.toStringAsFixed(2)}'),
                        Text('状态: ${payment.paymentStatus}'),
                        Text('交易ID: ${payment.transactionId}'),
                      ],
                    ),
                    trailing: const Icon(Icons.arrow_forward_ios),
                    onTap: () {
                      // Navigate to payment detail screen
                      // Navigator.push(context, MaterialPageRoute(builder: (context) => PaymentDetailScreen(payment: payment)));
                    },
                  ),
                );
              },
            );
          },
        ),
      ),
    );
  }
}