import 'package:flutter/material.dart';
import 'package:plb_kj_admin/shared/widgets/custom_card.dart';
import 'package:plb_kj_admin/shared/widgets/custom_list_tile.dart';
import 'package:plb_kj_admin/shared/widgets/error_display.dart';
import 'package:plb_kj_admin/shared/widgets/loading_indicator.dart';
import '../../../shared/widgets/breadcrumb.dart';
import '../product_service.dart';

class ProductsScreen extends StatefulWidget {
  const ProductsScreen({Key? key}) : super(key: key);

  @override
  State<ProductsScreen> createState() => _ProductsScreenState();
}

class _ProductsScreenState extends State<ProductsScreen> {
  late Future<List<Product>> _productsFuture;
  String _error = '';

  @override
  void initState() {
    super.initState();
    _loadProducts();
  }

  void _loadProducts() {
    setState(() {
      _error = '';
    });
    _productsFuture = ProductService.getProducts().catchError((error) {
      setState(() {
        _error = error.toString();
      });
      return <Product>[];
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Column(
        children: [
          Breadcrumb(
            items: [
              BreadcrumbItem(title: '首页', onTap: () {
                // 导航到首页
              }),
              BreadcrumbItem(title: '商品管理'),
            ],
          ),
          Expanded(
            child: Scaffold(
              appBar: AppBar(
                title: const Text('商品管理'),
                actions: [
                  IconButton(
                    icon: const Icon(Icons.add),
                    onPressed: () {
                      // Navigate to add product screen
                      // Navigator.push(context, MaterialPageRoute(builder: (context) => AddProductScreen()));
                    },
                  ),
                ],
              ),
              body: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  children: [
                    Expanded(
                      child: FutureBuilder<List<Product>>(
                        future: _productsFuture,
                        builder: (context, snapshot) {
                          if (snapshot.connectionState == ConnectionState.waiting) {
                            return const LoadingIndicator();
                          } else if (snapshot.hasError) {
                            return ErrorDisplay(message: '加载商品失败: ${snapshot.error}');
                          } else if (snapshot.hasData) {
                            final products = snapshot.data!;
                            if (products.isEmpty) {
                              return const Center(
                                child: Text('暂无商品数据'),
                              );
                            }
                            return ListView.builder(
                              itemCount: products.length,
                              itemBuilder: (context, index) {
                                final product = products[index];
                                return CustomCard(
                                  child: CustomListTile(
                                    leading: CircleAvatar(
                                      backgroundColor: Theme.of(context).primaryColor.withOpacity(0.1),
                                      child: Icon(
                                        Icons.inventory,
                                        color: Theme.of(context).primaryColor,
                                      ),
                                    ),
                                    title: Text(product.name),
                                    subtitle: Text('价格: ¥${product.price.toStringAsFixed(2)}'),
                                    trailing: Row(
                                      mainAxisSize: MainAxisSize.min,
                                      children: [
                                        IconButton(
                                          icon: const Icon(Icons.edit),
                                          onPressed: () {
                                            // 编辑商品
                                          },
                                        ),
                                        IconButton(
                                          icon: const Icon(Icons.delete),
                                          onPressed: () {
                                            // 删除商品
                                          },
                                        ),
                                      ],
                                    ),
                                  ),
                                );
                              },
                            );
                          } else {
                            return const ErrorDisplay(message: '未知错误');
                          }
                        },
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}