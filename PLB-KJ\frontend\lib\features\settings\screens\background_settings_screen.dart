import 'package:flutter/material.dart';
import '../../../core/network/api_service.dart';

class BackgroundSettingsScreen extends StatefulWidget {
  const BackgroundSettingsScreen({Key? key}) : super(key: key);

  @override
  State<BackgroundSettingsScreen> createState() =>
      _BackgroundSettingsScreenState();
}

class _BackgroundSettingsScreenState extends State<BackgroundSettingsScreen> {
  final TextEditingController _imageUrlController = TextEditingController();
  bool _isLoading = false;
  String? _currentBackgroundImage;
  String? _errorMessage;

  @override
  void initState() {
    super.initState();
    _loadCurrentBackground();
  }

  Future<void> _loadCurrentBackground() async {
    try {
      final response = await ApiService.getBackgroundSetting();
      if (response != null && response['background_image'] != null) {
        setState(() {
          _currentBackgroundImage = response['background_image']['data'];
          _imageUrlController.text = _currentBackgroundImage ?? '';
        });
      }
    } catch (e) {
      setState(() {
        _errorMessage = '加载当前背景设置失败';
      });
    }
  }

  Future<void> _saveBackgroundSetting() async {
    if (_imageUrlController.text.isEmpty) {
      setState(() {
        _errorMessage = '请输入背景图片URL';
      });
      return;
    }

    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      final response = await ApiService.updateBackgroundSetting(
        _imageUrlController.text,
      );

      if (response != null && response['status'] == 'success') {
        setState(() {
          _currentBackgroundImage = _imageUrlController.text;
        });
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('背景设置已保存')),
          );
        }
      } else {
        setState(() {
          _errorMessage = response?['message'] ?? '保存失败';
        });
      }
    } catch (e) {
      setState(() {
        _errorMessage = '保存背景设置失败: $e';
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _resetBackgroundSetting() async {
    setState(() {
      _imageUrlController.text = '';
      _errorMessage = null;
    });

    try {
      final response = await ApiService.updateBackgroundSetting('');

      if (response != null && response['status'] == 'success') {
        setState(() {
          _currentBackgroundImage = null;
        });
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('背景已重置为默认')),
          );
        }
      } else {
        setState(() {
          _errorMessage = response?['message'] ?? '重置失败';
        });
      }
    } catch (e) {
      setState(() {
        _errorMessage = '重置背景设置失败: $e';
      });
    }
  }

  @override
  void dispose() {
    _imageUrlController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('背景设置'),
        backgroundColor: Theme.of(context).primaryColor,
        foregroundColor: Colors.white,
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              '管理后台登录界面壁纸',
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),
            const Text(
              '设置管理后台登录界面的背景壁纸',
              style: TextStyle(
                fontSize: 14,
                color: Colors.grey,
              ),
            ),
            const SizedBox(height: 32),
            TextField(
              controller: _imageUrlController,
              decoration: const InputDecoration(
                labelText: '背景图片URL',
                border: OutlineInputBorder(),
                hintText: '请输入背景图片的完整URL地址',
              ),
            ),
            const SizedBox(height: 16),
            if (_currentBackgroundImage != null) ...[
              const Text(
                '当前背景预览:',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 8),
              Container(
                height: 200,
                decoration: BoxDecoration(
                  border: Border.all(color: Colors.grey),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: ClipRRect(
                  borderRadius: BorderRadius.circular(8),
                  child: Image.network(
                    _currentBackgroundImage!,
                    fit: BoxFit.cover,
                    errorBuilder: (context, error, stackTrace) {
                      return const Center(
                        child: Text('图片加载失败'),
                      );
                    },
                  ),
                ),
              ),
              const SizedBox(height: 16),
            ] else ...[
              Container(
                height: 200,
                decoration: BoxDecoration(
                  border: Border.all(color: Colors.grey),
                  borderRadius: BorderRadius.circular(8),
                  color: Colors.grey[200],
                ),
                child: const Center(
                  child: Text(
                    '暂无自定义背景\n系统将使用默认背景',
                    textAlign: TextAlign.center,
                    style: TextStyle(
                      color: Colors.grey,
                      fontSize: 16,
                    ),
                  ),
                ),
              ),
              const SizedBox(height: 16),
            ],
            if (_errorMessage != null) ...[
              Text(
                _errorMessage!,
                style: const TextStyle(
                  color: Colors.red,
                  fontSize: 14,
                ),
              ),
              const SizedBox(height: 16),
            ],
            Row(
              children: [
                Expanded(
                  child: ElevatedButton(
                    onPressed: _isLoading ? null : _saveBackgroundSetting,
                    style: ElevatedButton.styleFrom(
                      padding: const EdgeInsets.symmetric(vertical: 16),
                      backgroundColor: Theme.of(context).primaryColor,
                    ),
                    child: _isLoading
                        ? const SizedBox(
                            width: 20,
                            height: 20,
                            child: CircularProgressIndicator(
                              strokeWidth: 2,
                              valueColor:
                                  AlwaysStoppedAnimation<Color>(Colors.white),
                            ),
                          )
                        : const Text(
                            '保存设置',
                            style: TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.bold,
                              color: Colors.white,
                            ),
                          ),
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: OutlinedButton(
                    onPressed: _isLoading ? null : _resetBackgroundSetting,
                    style: OutlinedButton.styleFrom(
                      padding: const EdgeInsets.symmetric(vertical: 16),
                      side: BorderSide(
                        color: Theme.of(context).primaryColor,
                      ),
                    ),
                    child: const Text(
                      '重置默认',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                        color: Colors.red,
                      ),
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 32),
            const Card(
              child: Padding(
                padding: EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      '使用说明',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    SizedBox(height: 8),
                    Text(
                      '1. 输入有效的图片URL地址来设置自定义背景\n'
                      '2. 推荐使用高清图片以获得最佳显示效果\n'
                      '3. 点击"重置默认"可恢复系统默认背景\n'
                      '4. 背景图片将在下次登录时生效',
                      style: TextStyle(
                        fontSize: 14,
                        height: 1.5,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}