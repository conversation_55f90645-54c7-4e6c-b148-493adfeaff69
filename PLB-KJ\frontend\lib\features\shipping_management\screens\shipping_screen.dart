import 'package:flutter/material.dart';
import 'package:plb_kj_admin/shared/widgets/custom_card.dart';
import 'package:plb_kj_admin/shared/widgets/custom_list_tile.dart';
import 'package:plb_kj_admin/shared/widgets/error_display.dart';
import 'package:plb_kj_admin/shared/widgets/loading_indicator.dart';
import '../shipping_service.dart';

class ShippingScreen extends StatefulWidget {
  const ShippingScreen({Key? key}) : super(key: key);

  @override
  State<ShippingScreen> createState() => _ShippingScreenState();
}

class _ShippingScreenState extends State<ShippingScreen> {
  late Future<List<OrderShipping>> _shippingFuture;
  String _error = '';

  @override
  void initState() {
    super.initState();
    _loadShipping();
  }

  void _loadShipping() {
    setState(() {
      _error = '';
    });
    _shippingFuture = ShippingService.getOrderShipping().catchError((error) {
      setState(() {
        _error = error.toString();
      });
      return <OrderShipping>[];
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('物流管理'),
      ),
      body: RefreshIndicator(
        onRefresh: () async {
          _loadShipping();
          await _shippingFuture;
        },
        child: FutureBuilder<List<OrderShipping>>(
          future: _shippingFuture,
          builder: (context, snapshot) {
            if (snapshot.connectionState == ConnectionState.waiting) {
              return const LoadingIndicator();
            }

            if (snapshot.hasError || _error.isNotEmpty) {
              return ErrorDisplay(
                message: _error.isNotEmpty ? _error : snapshot.error.toString(),
                onRetry: _loadShipping,
              );
            }

            final shippingItems = snapshot.data ?? [];
            if (shippingItems.isEmpty) {
              return const Center(
                child: Text('暂无物流数据'),
              );
            }

            return ListView.builder(
              itemCount: shippingItems.length,
              itemBuilder: (context, index) {
                final shipping = shippingItems[index];
                return CustomCard(
                  child: CustomListTile(
                    title: Text('订单ID: ${shipping.orderId}'),
                    subtitle: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text('承运商ID: ${shipping.carrierId}'),
                        Text('追踪号: ${shipping.trackingNumber}'),
                        Text('状态: ${shipping.status}'),
                        Text('运费: ¥${shipping.shippingCost.toStringAsFixed(2)}'),
                      ],
                    ),
                    trailing: const Icon(Icons.arrow_forward_ios),
                    onTap: () {
                      // Navigate to shipping detail screen
                      // Navigator.push(context, MaterialPageRoute(builder: (context) => ShippingDetailScreen(shipping: shipping)));
                    },
                  ),
                );
              },
            );
          },
        ),
      ),
    );
  }
}