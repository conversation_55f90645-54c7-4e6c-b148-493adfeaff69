import 'package:flutter/foundation.dart';

class ShippingCarrier {
  final int id;
  final String name;
  final String code;
  final String trackingUrl;
  final bool isActive;
  final DateTime createdAt;
  final DateTime updatedAt;

  ShippingCarrier({
    required this.id,
    required this.name,
    required this.code,
    required this.trackingUrl,
    required this.isActive,
    required this.createdAt,
    required this.updatedAt,
  });

  factory ShippingCarrier.fromJson(Map<String, dynamic> json) {
    return ShippingCarrier(
      id: json['id'] as int,
      name: json['name'] as String,
      code: json['code'] as String,
      trackingUrl: json['tracking_url'] as String,
      isActive: json['is_active'] == 1,
      createdAt: DateTime.parse(json['created_at'] as String),
      updatedAt: DateTime.parse(json['updated_at'] as String),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'code': code,
      'tracking_url': trackingUrl,
      'is_active': isActive ? 1 : 0,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }
}

class ShippingRate {
  final int id;
  final int carrierId;
  final String name;
  final double minWeight;
  final double maxWeight;
  final double minAmount;
  final double maxAmount;
  final double rate;
  final String country;
  final String region;
  final bool isActive;
  final DateTime createdAt;
  final DateTime updatedAt;

  ShippingRate({
    required this.id,
    required this.carrierId,
    required this.name,
    required this.minWeight,
    required this.maxWeight,
    required this.minAmount,
    required this.maxAmount,
    required this.rate,
    required this.country,
    required this.region,
    required this.isActive,
    required this.createdAt,
    required this.updatedAt,
  });

  factory ShippingRate.fromJson(Map<String, dynamic> json) {
    return ShippingRate(
      id: json['id'] as int,
      carrierId: json['carrier_id'] as int,
      name: json['name'] as String,
      minWeight: (json['min_weight'] as num).toDouble(),
      maxWeight: (json['max_weight'] as num).toDouble(),
      minAmount: (json['min_amount'] as num).toDouble(),
      maxAmount: (json['max_amount'] as num).toDouble(),
      rate: (json['rate'] as num).toDouble(),
      country: json['country'] as String,
      region: json['region'] as String,
      isActive: json['is_active'] == 1,
      createdAt: DateTime.parse(json['created_at'] as String),
      updatedAt: DateTime.parse(json['updated_at'] as String),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'carrier_id': carrierId,
      'name': name,
      'min_weight': minWeight,
      'max_weight': maxWeight,
      'min_amount': minAmount,
      'max_amount': maxAmount,
      'rate': rate,
      'country': country,
      'region': region,
      'is_active': isActive ? 1 : 0,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }
}

class OrderShipping {
  final int id;
  final int orderId;
  final int carrierId;
  final String trackingNumber;
  final DateTime? shippingDate;
  final DateTime? estimatedDeliveryDate;
  final DateTime? actualDeliveryDate;
  final double shippingCost;
  final String status;
  final DateTime createdAt;
  final DateTime updatedAt;

  OrderShipping({
    required this.id,
    required this.orderId,
    required this.carrierId,
    required this.trackingNumber,
    this.shippingDate,
    this.estimatedDeliveryDate,
    this.actualDeliveryDate,
    required this.shippingCost,
    required this.status,
    required this.createdAt,
    required this.updatedAt,
  });

  factory OrderShipping.fromJson(Map<String, dynamic> json) {
    return OrderShipping(
      id: json['id'] as int,
      orderId: json['order_id'] as int,
      carrierId: json['carrier_id'] as int,
      trackingNumber: json['tracking_number'] as String,
      shippingDate: json['shipping_date'] != null ? DateTime.parse(json['shipping_date'] as String) : null,
      estimatedDeliveryDate: json['estimated_delivery_date'] != null ? DateTime.parse(json['estimated_delivery_date'] as String) : null,
      actualDeliveryDate: json['actual_delivery_date'] != null ? DateTime.parse(json['actual_delivery_date'] as String) : null,
      shippingCost: (json['shipping_cost'] as num).toDouble(),
      status: json['status'] as String,
      createdAt: DateTime.parse(json['created_at'] as String),
      updatedAt: DateTime.parse(json['updated_at'] as String),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'order_id': orderId,
      'carrier_id': carrierId,
      'tracking_number': trackingNumber,
      'shipping_date': shippingDate?.toIso8601String(),
      'estimated_delivery_date': estimatedDeliveryDate?.toIso8601String(),
      'actual_delivery_date': actualDeliveryDate?.toIso8601String(),
      'shipping_cost': shippingCost,
      'status': status,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }
}

class ShippingService {
  // Mock data for demonstration
  static final List<ShippingCarrier> _carriers = [
    ShippingCarrier(
      id: 1,
      name: 'DHL',
      code: 'dhl',
      trackingUrl: 'https://www.dhl.com/en/express/tracking.html?AWB={tracking_number}',
      isActive: true,
      createdAt: DateTime.now().subtract(const Duration(days: 30)),
      updatedAt: DateTime.now().subtract(const Duration(days: 30)),
    ),
    ShippingCarrier(
      id: 2,
      name: 'FedEx',
      code: 'fedex',
      trackingUrl: 'https://www.fedex.com/apps/fedextrack/?tracknumbers={tracking_number}',
      isActive: true,
      createdAt: DateTime.now().subtract(const Duration(days: 30)),
      updatedAt: DateTime.now().subtract(const Duration(days: 30)),
    ),
    ShippingCarrier(
      id: 3,
      name: 'UPS',
      code: 'ups',
      trackingUrl: 'https://www.ups.com/track?loc=en_US&tracknum={tracking_number}',
      isActive: true,
      createdAt: DateTime.now().subtract(const Duration(days: 30)),
      updatedAt: DateTime.now().subtract(const Duration(days: 30)),
    ),
  ];

  static final List<ShippingRate> _rates = [
    ShippingRate(
      id: 1,
      carrierId: 1,
      name: 'DHL Express',
      minWeight: 0.0,
      maxWeight: 10.0,
      minAmount: 0.0,
      maxAmount: 1000.0,
      rate: 15.99,
      country: 'US',
      region: 'All',
      isActive: true,
      createdAt: DateTime.now().subtract(const Duration(days: 30)),
      updatedAt: DateTime.now().subtract(const Duration(days: 30)),
    ),
    ShippingRate(
      id: 2,
      carrierId: 2,
      name: 'FedEx Ground',
      minWeight: 0.0,
      maxWeight: 20.0,
      minAmount: 0.0,
      maxAmount: 500.0,
      rate: 9.99,
      country: 'US',
      region: 'All',
      isActive: true,
      createdAt: DateTime.now().subtract(const Duration(days: 30)),
      updatedAt: DateTime.now().subtract(const Duration(days: 30)),
    ),
  ];

  static final List<OrderShipping> _orderShipping = [
    OrderShipping(
      id: 1,
      orderId: 1,
      carrierId: 1,
      trackingNumber: 'DHL1234567890',
      shippingDate: DateTime.now().subtract(const Duration(days: 2)),
      estimatedDeliveryDate: DateTime.now().add(const Duration(days: 3)),
      actualDeliveryDate: null,
      shippingCost: 15.99,
      status: 'in_transit',
      createdAt: DateTime.now().subtract(const Duration(days: 2)),
      updatedAt: DateTime.now().subtract(const Duration(days: 2)),
    ),
    OrderShipping(
      id: 2,
      orderId: 2,
      carrierId: 2,
      trackingNumber: 'FDX0987654321',
      shippingDate: DateTime.now().subtract(const Duration(days: 1)),
      estimatedDeliveryDate: DateTime.now().add(const Duration(days: 5)),
      actualDeliveryDate: null,
      shippingCost: 9.99,
      status: 'shipped',
      createdAt: DateTime.now().subtract(const Duration(days: 1)),
      updatedAt: DateTime.now().subtract(const Duration(days: 1)),
    ),
  ];

  static Future<List<ShippingCarrier>> getShippingCarriers() async {
    // Simulate network delay
    await Future.delayed(const Duration(milliseconds: 500));
    return _carriers;
  }

  static Future<List<ShippingRate>> getShippingRates() async {
    // Simulate network delay
    await Future.delayed(const Duration(milliseconds: 500));
    return _rates;
  }

  static Future<List<OrderShipping>> getOrderShipping() async {
    // Simulate network delay
    await Future.delayed(const Duration(milliseconds: 500));
    return _orderShipping;
  }

  static Future<ShippingCarrier?> getCarrierById(int id) async {
    // Simulate network delay
    await Future.delayed(const Duration(milliseconds: 500));
    try {
      return _carriers.firstWhere((carrier) => carrier.id == id);
    } catch (e) {
      return null;
    }
  }

  static Future<OrderShipping?> getOrderShippingByOrderId(int orderId) async {
    // Simulate network delay
    await Future.delayed(const Duration(milliseconds: 500));
    try {
      return _orderShipping.firstWhere((shipping) => shipping.orderId == orderId);
    } catch (e) {
      return null;
    }
  }

  static Future<OrderShipping> createOrderShipping(OrderShipping orderShipping) async {
    // Simulate network delay
    await Future.delayed(const Duration(milliseconds: 500));
    // In a real app, this would make an API call
    final newOrderShipping = OrderShipping(
      id: _orderShipping.length + 1,
      orderId: orderShipping.orderId,
      carrierId: orderShipping.carrierId,
      trackingNumber: orderShipping.trackingNumber,
      shippingDate: orderShipping.shippingDate,
      estimatedDeliveryDate: orderShipping.estimatedDeliveryDate,
      actualDeliveryDate: orderShipping.actualDeliveryDate,
      shippingCost: orderShipping.shippingCost,
      status: orderShipping.status,
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
    );
    _orderShipping.add(newOrderShipping);
    return newOrderShipping;
  }

  static Future<OrderShipping> updateOrderShipping(OrderShipping orderShipping) async {
    // Simulate network delay
    await Future.delayed(const Duration(milliseconds: 500));
    // In a real app, this would make an API call
    final index = _orderShipping.indexWhere((s) => s.id == orderShipping.id);
    if (index != -1) {
      _orderShipping[index] = orderShipping.copyWith(updatedAt: DateTime.now());
      return _orderShipping[index];
    }
    throw Exception('Order shipping not found');
  }
}

// Extension to help with copying shipping items with updated fields
extension OrderShippingCopyWith on OrderShipping {
  OrderShipping copyWith({
    int? id,
    int? orderId,
    int? carrierId,
    String? trackingNumber,
    DateTime? shippingDate,
    DateTime? estimatedDeliveryDate,
    DateTime? actualDeliveryDate,
    double? shippingCost,
    String? status,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return OrderShipping(
      id: id ?? this.id,
      orderId: orderId ?? this.orderId,
      carrierId: carrierId ?? this.carrierId,
      trackingNumber: trackingNumber ?? this.trackingNumber,
      shippingDate: shippingDate ?? this.shippingDate,
      estimatedDeliveryDate: estimatedDeliveryDate ?? this.estimatedDeliveryDate,
      actualDeliveryDate: actualDeliveryDate ?? this.actualDeliveryDate,
      shippingCost: shippingCost ?? this.shippingCost,
      status: status ?? this.status,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }
}