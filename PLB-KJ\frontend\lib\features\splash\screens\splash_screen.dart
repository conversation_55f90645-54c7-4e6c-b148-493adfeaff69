import 'package:flutter/material.dart';
import '../../../shared/routes/app_routes.dart';
import '../../../shared/routes/admin_routes.dart';
import '../../../shared/routes/user_routes.dart';
import '../../authentication/auth_service.dart';

class SplashScreen extends StatefulWidget {
  final AuthService authService;
  final bool isAdminApp;

  const SplashScreen({
    Key? key,
    required this.authService,
    this.isAdminApp = true,
  }) : super(key: key);

  @override
  State<SplashScreen> createState() => _SplashScreenState();
}

class _SplashScreenState extends State<SplashScreen> {
  bool _initialized = false;

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    if (!_initialized) {
      _initializeApp();
      _initialized = true;
    }
  }

  Future<void> _initializeApp() async {
    // 模拟初始化过程
    await Future.delayed(const Duration(seconds: 2));
    
    // 检查是否已登录
    final token = await widget.authService.getAuthToken();
    bool isLoggedIn = false;
    String? userType;
    
    if (token != null) {
      // 验证token有效性
      isLoggedIn = await widget.authService.validateToken(token);
      
      if (isLoggedIn) {
        // 获取用户数据以确定用户类型
        final userData = await widget.authService.getUserData();
        if (userData != null && userData['role'] != null) {
          userType = userData['role'];
        }
      }
    }
    
    if (!mounted) return;
    
    if (isLoggedIn) {
      // 如果已登录，直接导航到相应主界面
      if (userType == 'admin') {
        Navigator.pushReplacementNamed(context, AppRoutes.dashboard);
      } else {
        Navigator.pushReplacementNamed(context, AppRoutes.userCenter);
      }
    } else {
      // 如果未登录，导航到统一登录界面
      Navigator.pushReplacementNamed(context, AppRoutes.unifiedLogin);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: const [
            Text(
              '系统初始化中...',
              style: TextStyle(fontSize: 18),
            ),
            SizedBox(height: 20),
            CircularProgressIndicator(),
          ],
        ),
      ),
    );
  }
}