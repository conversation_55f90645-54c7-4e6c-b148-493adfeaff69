import '../../core/network/api_service.dart';
import '../../core/network/api_endpoints.dart';

class StatsService {
  final ApiService _apiService;

  StatsService(this._apiService);

  // 获取统计数据
  Future<ApiResponse<Map<String, dynamic>>> getStats() async {
    try {
      final response = await _apiService.get(ApiEndpoints.stats);
      
      if (response.statusCode == 200) {
        return ApiResponse.success(response.data);
      } else {
        return ApiResponse.error('获取统计数据失败');
      }
    } on ApiException catch (e) {
      return ApiResponse.error(e.message, code: e.statusCode);
    } catch (e) {
      return ApiResponse.error('获取统计数据失败: $e');
    }
  }

  // 获取用户增长数据
  Future<ApiResponse<List<Map<String, dynamic>>>> getUserGrowthData() async {
    try {
      final response = await _apiService.get('${ApiEndpoints.stats}/user-growth');
      
      if (response.statusCode == 200) {
        final List<dynamic> data = response.data;
        return ApiResponse.success(
          data.map((item) => item as Map<String, dynamic>).toList(),
        );
      } else {
        return ApiResponse.error('获取用户增长数据失败');
      }
    } on ApiException catch (e) {
      return ApiResponse.error(e.message, code: e.statusCode);
    } catch (e) {
      return ApiResponse.error('获取用户增长数据失败: $e');
    }
  }

  // 获取销售数据
  Future<ApiResponse<List<Map<String, dynamic>>>> getSalesData() async {
    try {
      final response = await _apiService.get('${ApiEndpoints.stats}/sales');
      
      if (response.statusCode == 200) {
        final List<dynamic> data = response.data;
        return ApiResponse.success(
          data.map((item) => item as Map<String, dynamic>).toList(),
        );
      } else {
        return ApiResponse.error('获取销售数据失败');
      }
    } on ApiException catch (e) {
      return ApiResponse.error(e.message, code: e.statusCode);
    } catch (e) {
      return ApiResponse.error('获取销售数据失败: $e');
    }
  }
}