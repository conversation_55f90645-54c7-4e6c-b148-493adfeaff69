import 'package:flutter/material.dart';
import '../../../shared/widgets/custom_card.dart';
import '../../../shared/widgets/breadcrumb.dart';
import '../widgets/user_info_card.dart';
import '../widgets/user_bottom_nav_bar.dart';
import 'my_orders_screen.dart';
import 'shipping_addresses_screen.dart';
import 'my_favorites_screen.dart';
import 'my_reviews_screen.dart';
import 'my_wallet_screen.dart';
import 'settings_screen.dart';

class UserCenterScreen extends StatefulWidget {
  const UserCenterScreen({Key? key}) : super(key: key);

  @override
  State<UserCenterScreen> createState() => _UserCenterScreenState();
}

class _UserCenterScreenState extends State<UserCenterScreen> {
  int _currentIndex = 0;

  late final List<Widget> _screens;

  final List<String> _screenTitles = [
    '首页',
    '我的订单',
    '收货地址',
    '我的收藏',
    '我的评价',
    '我的钱包',
    '设置',
  ];

  @override
  void initState() {
    super.initState();
    _screens = [
      const HomeScreen(),
      const MyOrdersScreen(),
      const ShippingAddressesScreen(),
      const MyFavoritesScreen(),
      const MyReviewsScreen(),
      const MyWalletScreen(),
      const SettingsScreen(),
    ];
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('用户中心'),
      ),
      body: Column(
        children: [
          Breadcrumb(
            items: [
              BreadcrumbItem(title: '首页'),
              BreadcrumbItem(title: _screenTitles[_currentIndex]),
            ],
          ),
          Expanded(
            child: _screens[_currentIndex],
          ),
        ],
      ),
      bottomNavigationBar: UserBottomNavBar(
        currentIndex: _currentIndex,
        onTap: (index) {
          setState(() {
            _currentIndex = index;
          });
        },
      ),
    );
  }

  // 公开方法供子组件访问
  void setState(VoidCallback fn) {
    super.setState(fn);
  }

  int get currentIndex => _currentIndex;
}

class HomeScreen extends StatefulWidget {
  const HomeScreen({Key? key}) : super(key: key);

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> {
  @override
  Widget build(BuildContext context) {
    // 获取父级状态对象
    final userCenterState = context.findAncestorStateOfType<_UserCenterScreenState>();
    
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          UserInfoCard(
            username: '张三',
            email: '<EMAIL>',
            avatarUrl: '',
            userId: '1',
          ),
          const SizedBox(height: 20),
          const Text(
            '我的服务',
            style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 10),
          GridView.count(
            crossAxisCount: 3,
            crossAxisSpacing: 10,
            mainAxisSpacing: 10,
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            children: [
              _ServiceButton(
                icon: Icons.shopping_bag, 
                label: '我的订单',
                onTap: () {
                  // 导航到我的订单页面 (索引1)
                  userCenterState?.setState(() {
                    userCenterState!._currentIndex = 1;
                  });
                },
              ),
              _ServiceButton(
                icon: Icons.location_on, 
                label: '收货地址',
                onTap: () {
                  // 导航到收货地址页面 (索引2)
                  userCenterState?.setState(() {
                    userCenterState!._currentIndex = 2;
                  });
                },
              ),
              _ServiceButton(
                icon: Icons.favorite, 
                label: '我的收藏',
                onTap: () {
                  // 导航到我的收藏页面 (索引3)
                  userCenterState?.setState(() {
                    userCenterState!._currentIndex = 3;
                  });
                },
              ),
              _ServiceButton(
                icon: Icons.comment, 
                label: '我的评价',
                onTap: () {
                  // 导航到我的评价页面 (索引4)
                  userCenterState?.setState(() {
                    userCenterState!._currentIndex = 4;
                  });
                },
              ),
              _ServiceButton(
                icon: Icons.account_balance_wallet, 
                label: '我的钱包',
                onTap: () {
                  // 导航到我的钱包页面 (索引5)
                  userCenterState?.setState(() {
                    userCenterState!._currentIndex = 5;
                  });
                },
              ),
              _ServiceButton(
                icon: Icons.settings, 
                label: '设置',
                onTap: () {
                  // 导航到设置页面 (索引6)
                  userCenterState?.setState(() {
                    userCenterState!._currentIndex = 6;
                  });
                },
              ),
            ],
          ),
        ],
      ),
    );
  }
}

class _ServiceButton extends StatelessWidget {
  final IconData icon;
  final String label;
  final VoidCallback? onTap;

  const _ServiceButton({required this.icon, required this.label, this.onTap});

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(icon, size: 30),
          const SizedBox(height: 5),
          Text(label, textAlign: TextAlign.center),
        ],
      ),
    );
  }
}