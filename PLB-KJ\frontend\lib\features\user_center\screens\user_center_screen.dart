import 'package:flutter/material.dart';
import '../../../shared/widgets/custom_card.dart';
import '../../../shared/routes/app_routes.dart';
import '../widgets/user_stats_card.dart';
import '../widgets/user_quick_actions.dart';
import 'my_orders_screen.dart';
import 'shipping_addresses_screen.dart';
import 'my_favorites_screen.dart';
import 'my_reviews_screen.dart';
import 'my_wallet_screen.dart';
import 'settings_screen.dart';

class UserCenterScreen extends StatefulWidget {
  const UserCenterScreen({Key? key}) : super(key: key);

  @override
  State<UserCenterScreen> createState() => _UserCenterScreenState();
}

class _UserCenterScreenState extends State<UserCenterScreen> {
  int _currentIndex = 0;

  late final List<Widget> _screens;

  final List<String> _screenTitles = [
    '首页',
    '我的订单',
    '收货地址',
    '我的收藏',
    '我的评价',
    '我的钱包',
    '设置',
  ];

  final List<IconData> _screenIcons = [
    Icons.home,
    Icons.shopping_bag,
    Icons.location_on,
    Icons.favorite,
    Icons.rate_review,
    Icons.account_balance_wallet,
    Icons.settings,
  ];

  @override
  void initState() {
    super.initState();
    _screens = [
      const HomeScreen(),
      const MyOrdersScreen(),
      const ShippingAddressesScreen(),
      const MyFavoritesScreen(),
      const MyReviewsScreen(),
      const MyWalletScreen(),
      const SettingsScreen(),
    ];
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey[100],
      body: Row(
        children: [
          // 侧边导航栏
          _buildSidebar(),

          // 主内容区域
          Expanded(
            child: Column(
              children: [
                // 顶部导航栏
                _buildTopBar(),

                // 主内容
                Expanded(
                  child: _screens[_currentIndex],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSidebar() {
    return Container(
      width: 280,
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(2, 0),
          ),
        ],
      ),
      child: Column(
        children: [
          // 用户信息头部
          Container(
            height: 120,
            padding: const EdgeInsets.all(24),
            decoration: BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
                colors: [
                  Theme.of(context).primaryColor,
                  Theme.of(context).primaryColor.withValues(alpha: 0.8),
                ],
              ),
            ),
            child: Row(
              children: [
                CircleAvatar(
                  radius: 30,
                  backgroundColor: Colors.white.withValues(alpha: 0.2),
                  child: const Icon(
                    Icons.person,
                    color: Colors.white,
                    size: 32,
                  ),
                ),
                const SizedBox(width: 16),
                const Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Text(
                        '张三',
                        style: TextStyle(
                          color: Colors.white,
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      SizedBox(height: 4),
                      Text(
                        '<EMAIL>',
                        style: TextStyle(
                          color: Colors.white70,
                          fontSize: 12,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),

          // 导航菜单
          Expanded(
            child: ListView.builder(
              padding: const EdgeInsets.symmetric(vertical: 16),
              itemCount: _screenTitles.length,
              itemBuilder: (context, index) {
                final isSelected = index == _currentIndex;
                return Container(
                  margin: const EdgeInsets.symmetric(horizontal: 12, vertical: 2),
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(8),
                    color: isSelected
                        ? Theme.of(context).primaryColor.withValues(alpha: 0.1)
                        : Colors.transparent,
                  ),
                  child: ListTile(
                    leading: Icon(
                      _screenIcons[index],
                      color: isSelected
                          ? Theme.of(context).primaryColor
                          : Colors.grey[600],
                      size: 22,
                    ),
                    title: Text(
                      _screenTitles[index],
                      style: TextStyle(
                        color: isSelected
                            ? Theme.of(context).primaryColor
                            : Colors.grey[700],
                        fontWeight: isSelected
                            ? FontWeight.w600
                            : FontWeight.normal,
                        fontSize: 14,
                      ),
                    ),
                    onTap: () {
                      setState(() {
                        _currentIndex = index;
                      });
                    },
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                    contentPadding: const EdgeInsets.symmetric(
                      horizontal: 16,
                      vertical: 4,
                    ),
                  ),
                );
              },
            ),
          ),

          // 底部退出按钮
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              border: Border(
                top: BorderSide(
                  color: Colors.grey[200]!,
                  width: 1,
                ),
              ),
            ),
            child: ListTile(
              leading: const Icon(
                Icons.logout,
                color: Colors.red,
                size: 22,
              ),
              title: const Text(
                '退出登录',
                style: TextStyle(
                  color: Colors.red,
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                ),
              ),
              onTap: _handleLogout,
              contentPadding: const EdgeInsets.symmetric(horizontal: 16),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTopBar() {
    return Container(
      height: 70,
      padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 16),
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        children: [
          Text(
            _screenTitles[_currentIndex],
            style: const TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.bold,
              color: Colors.black87,
            ),
          ),
          const Spacer(),

          // 通知图标
          IconButton(
            onPressed: () {
              // TODO: 显示通知
            },
            icon: Stack(
              children: [
                const Icon(Icons.notifications_outlined, size: 24),
                Positioned(
                  right: 0,
                  top: 0,
                  child: Container(
                    width: 8,
                    height: 8,
                    decoration: const BoxDecoration(
                      color: Colors.red,
                      shape: BoxShape.circle,
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  void _handleLogout() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('确认退出'),
        content: const Text('您确定要退出用户中心吗？'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('取消'),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              Navigator.pushNamedAndRemoveUntil(
                context,
                AppRoutes.loginSelection,
                (route) => false,
              );
            },
            child: const Text('确定'),
          ),
        ],
      ),
    );
  }

  // 公开方法供子组件访问
  void setState(VoidCallback fn) {
    super.setState(fn);
  }

  int get currentIndex => _currentIndex;
}

class HomeScreen extends StatefulWidget {
  const HomeScreen({Key? key}) : super(key: key);

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> {
  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(24),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 用户统计卡片
          Row(
            children: [
              Expanded(
                child: UserStatsCard(
                  title: '待付款订单',
                  value: '3',
                  icon: Icons.payment,
                  color: Colors.orange,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: UserStatsCard(
                  title: '待收货订单',
                  value: '2',
                  icon: Icons.local_shipping,
                  color: Colors.blue,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: UserStatsCard(
                  title: '我的收藏',
                  value: '15',
                  icon: Icons.favorite,
                  color: Colors.red,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: UserStatsCard(
                  title: '钱包余额',
                  value: '¥1,280',
                  icon: Icons.account_balance_wallet,
                  color: Colors.green,
                ),
              ),
            ],
          ),

          const SizedBox(height: 32),

          // 快捷操作
          const Text(
            '快捷操作',
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
              color: Colors.black87,
            ),
          ),
          const SizedBox(height: 16),

          UserQuickActions(
            onViewOrders: () {
              final userCenterState = context.findAncestorStateOfType<_UserCenterScreenState>();
              userCenterState?.setState(() {
                userCenterState._currentIndex = 1;
              });
            },
            onViewAddresses: () {
              final userCenterState = context.findAncestorStateOfType<_UserCenterScreenState>();
              userCenterState?.setState(() {
                userCenterState._currentIndex = 2;
              });
            },
            onViewFavorites: () {
              final userCenterState = context.findAncestorStateOfType<_UserCenterScreenState>();
              userCenterState?.setState(() {
                userCenterState._currentIndex = 3;
              });
            },
            onViewWallet: () {
              final userCenterState = context.findAncestorStateOfType<_UserCenterScreenState>();
              userCenterState?.setState(() {
                userCenterState._currentIndex = 5;
              });
            },
          ),

          const SizedBox(height: 32),

          // 最近订单
          const Text(
            '最近订单',
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
              color: Colors.black87,
            ),
          ),
          const SizedBox(height: 16),

          CustomCard(
            child: Column(
              children: [
                _buildOrderItem(
                  orderNumber: '#12345',
                  productName: 'iPhone 15 Pro Max',
                  status: '待付款',
                  amount: '¥9,999',
                  statusColor: Colors.orange,
                ),
                const Divider(),
                _buildOrderItem(
                  orderNumber: '#12344',
                  productName: 'MacBook Pro 16"',
                  status: '已发货',
                  amount: '¥19,999',
                  statusColor: Colors.blue,
                ),
                const Divider(),
                _buildOrderItem(
                  orderNumber: '#12343',
                  productName: 'AirPods Pro',
                  status: '已完成',
                  amount: '¥1,999',
                  statusColor: Colors.green,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildOrderItem({
    required String orderNumber,
    required String productName,
    required String status,
    required String amount,
    required Color statusColor,
  }) {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Row(
        children: [
          Container(
            width: 50,
            height: 50,
            decoration: BoxDecoration(
              color: Colors.grey[200],
              borderRadius: BorderRadius.circular(8),
            ),
            child: Icon(
              Icons.shopping_bag,
              color: Colors.grey[600],
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  orderNumber,
                  style: TextStyle(
                    fontSize: 12,
                    color: Colors.grey[600],
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  productName,
                  style: const TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w600,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  amount,
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: Colors.black87,
                  ),
                ),
              ],
            ),
          ),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
            decoration: BoxDecoration(
              color: statusColor.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Text(
              status,
              style: TextStyle(
                color: statusColor,
                fontSize: 12,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
        ],
      ),
    );
  }
}