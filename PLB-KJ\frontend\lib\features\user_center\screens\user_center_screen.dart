import 'package:flutter/material.dart';
import '../../../shared/widgets/custom_card.dart';
import '../../../shared/routes/app_routes.dart';
import '../widgets/user_info_card.dart';
import '../widgets/user_stats_card.dart';
import '../widgets/user_quick_actions.dart';
import 'my_orders_screen.dart';
import 'shipping_addresses_screen.dart';
import 'my_favorites_screen.dart';
import 'my_reviews_screen.dart';
import 'my_wallet_screen.dart';
import 'settings_screen.dart';

class UserCenterScreen extends StatefulWidget {
  const UserCenterScreen({Key? key}) : super(key: key);

  @override
  State<UserCenterScreen> createState() => _UserCenterScreenState();
}

class _UserCenterScreenState extends State<UserCenterScreen> {
  int _currentIndex = 0;

  late final List<Widget> _screens;

  final List<String> _screenTitles = [
    '首页',
    '我的订单',
    '收货地址',
    '我的收藏',
    '我的评价',
    '我的钱包',
    '设置',
  ];

  final List<IconData> _screenIcons = [
    Icons.home,
    Icons.shopping_bag,
    Icons.location_on,
    Icons.favorite,
    Icons.rate_review,
    Icons.account_balance_wallet,
    Icons.settings,
  ];

  @override
  void initState() {
    super.initState();
    _screens = [
      const UserHomeScreen(),
      const MyOrdersScreen(),
      const ShippingAddressesScreen(),
      const MyFavoritesScreen(),
      const MyReviewsScreen(),
      const MyWalletScreen(),
      const SettingsScreen(),
    ];
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey[100],
      body: Row(
        children: [
          // 侧边导航栏
          _buildSidebar(),

          // 主内容区域
          Expanded(
            child: Column(
              children: [
                // 顶部导航栏
                _buildTopBar(),

                // 主内容
                Expanded(
                  child: _screens[_currentIndex],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSidebar() {
    return Container(
      width: 280,
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(2, 0),
          ),
        ],
      ),
      child: Column(
        children: [
          // 用户信息头部
          Container(
            height: 120,
            padding: const EdgeInsets.all(24),
            decoration: BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
                colors: [
                  Theme.of(context).primaryColor,
                  Theme.of(context).primaryColor.withValues(alpha: 0.8),
                ],
              ),
            ),
            child: Row(
              children: [
                CircleAvatar(
                  radius: 30,
                  backgroundColor: Colors.white.withValues(alpha: 0.2),
                  child: const Icon(
                    Icons.person,
                    color: Colors.white,
                    size: 32,
                  ),
                ),
                const SizedBox(width: 16),
                const Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Text(
                        '张三',
                        style: TextStyle(
                          color: Colors.white,
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      SizedBox(height: 4),
                      Text(
                        '<EMAIL>',
                        style: TextStyle(
                          color: Colors.white70,
                          fontSize: 12,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),

          // 导航菜单
          Expanded(
            child: ListView.builder(
              padding: const EdgeInsets.symmetric(vertical: 16),
              itemCount: _screenTitles.length,
              itemBuilder: (context, index) {
                final isSelected = index == _currentIndex;
                return Container(
                  margin: const EdgeInsets.symmetric(horizontal: 12, vertical: 2),
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(8),
                    color: isSelected
                        ? Theme.of(context).primaryColor.withValues(alpha: 0.1)
                        : Colors.transparent,
                  ),
                  child: ListTile(
                    leading: Icon(
                      _screenIcons[index],
                      color: isSelected
                          ? Theme.of(context).primaryColor
                          : Colors.grey[600],
                      size: 22,
                    ),
                    title: Text(
                      _screenTitles[index],
                      style: TextStyle(
                        color: isSelected
                            ? Theme.of(context).primaryColor
                            : Colors.grey[700],
                        fontWeight: isSelected
                            ? FontWeight.w600
                            : FontWeight.normal,
                        fontSize: 14,
                      ),
                    ),
                    onTap: () {
                      setState(() {
                        _currentIndex = index;
                      });
                    },
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                    contentPadding: const EdgeInsets.symmetric(
                      horizontal: 16,
                      vertical: 4,
                    ),
                  ),
                );
              },
            ),
          ),

          // 底部退出按钮
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              border: Border(
                top: BorderSide(
                  color: Colors.grey[200]!,
                  width: 1,
                ),
              ),
            ),
            child: ListTile(
              leading: const Icon(
                Icons.logout,
                color: Colors.red,
                size: 22,
              ),
              title: const Text(
                '退出登录',
                style: TextStyle(
                  color: Colors.red,
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                ),
              ),
              onTap: _handleLogout,
              contentPadding: const EdgeInsets.symmetric(horizontal: 16),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTopBar() {
    return Container(
      height: 70,
      padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 16),
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        children: [
          Text(
            _screenTitles[_currentIndex],
            style: const TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.bold,
              color: Colors.black87,
            ),
          ),
          const Spacer(),

          // 通知图标
          IconButton(
            onPressed: () {
              // TODO: 显示通知
            },
            icon: Stack(
              children: [
                const Icon(Icons.notifications_outlined, size: 24),
                Positioned(
                  right: 0,
                  top: 0,
                  child: Container(
                    width: 8,
                    height: 8,
                    decoration: const BoxDecoration(
                      color: Colors.red,
                      shape: BoxShape.circle,
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  void _handleLogout() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('确认退出'),
        content: const Text('您确定要退出用户中心吗？'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('取消'),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              Navigator.pushNamedAndRemoveUntil(
                context,
                AppRoutes.loginSelection,
                (route) => false,
              );
            },
            child: const Text('确定'),
          ),
        ],
      ),
    );
  }

  // 公开方法供子组件访问
  void setState(VoidCallback fn) {
    super.setState(fn);
  }

  int get currentIndex => _currentIndex;
}

class HomeScreen extends StatefulWidget {
  const HomeScreen({Key? key}) : super(key: key);

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> {
  @override
  Widget build(BuildContext context) {
    // 获取父级状态对象
    final userCenterState = context.findAncestorStateOfType<_UserCenterScreenState>();
    
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          UserInfoCard(
            username: '张三',
            email: '<EMAIL>',
            avatarUrl: '',
            userId: '1',
          ),
          const SizedBox(height: 20),
          const Text(
            '我的服务',
            style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 10),
          GridView.count(
            crossAxisCount: 3,
            crossAxisSpacing: 10,
            mainAxisSpacing: 10,
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            children: [
              _ServiceButton(
                icon: Icons.shopping_bag, 
                label: '我的订单',
                onTap: () {
                  // 导航到我的订单页面 (索引1)
                  userCenterState?.setState(() {
                    userCenterState!._currentIndex = 1;
                  });
                },
              ),
              _ServiceButton(
                icon: Icons.location_on, 
                label: '收货地址',
                onTap: () {
                  // 导航到收货地址页面 (索引2)
                  userCenterState?.setState(() {
                    userCenterState!._currentIndex = 2;
                  });
                },
              ),
              _ServiceButton(
                icon: Icons.favorite, 
                label: '我的收藏',
                onTap: () {
                  // 导航到我的收藏页面 (索引3)
                  userCenterState?.setState(() {
                    userCenterState!._currentIndex = 3;
                  });
                },
              ),
              _ServiceButton(
                icon: Icons.comment, 
                label: '我的评价',
                onTap: () {
                  // 导航到我的评价页面 (索引4)
                  userCenterState?.setState(() {
                    userCenterState!._currentIndex = 4;
                  });
                },
              ),
              _ServiceButton(
                icon: Icons.account_balance_wallet, 
                label: '我的钱包',
                onTap: () {
                  // 导航到我的钱包页面 (索引5)
                  userCenterState?.setState(() {
                    userCenterState!._currentIndex = 5;
                  });
                },
              ),
              _ServiceButton(
                icon: Icons.settings, 
                label: '设置',
                onTap: () {
                  // 导航到设置页面 (索引6)
                  userCenterState?.setState(() {
                    userCenterState!._currentIndex = 6;
                  });
                },
              ),
            ],
          ),
        ],
      ),
    );
  }
}

class _ServiceButton extends StatelessWidget {
  final IconData icon;
  final String label;
  final VoidCallback? onTap;

  const _ServiceButton({required this.icon, required this.label, this.onTap});

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(icon, size: 30),
          const SizedBox(height: 5),
          Text(label, textAlign: TextAlign.center),
        ],
      ),
    );
  }
}