import 'package:flutter/material.dart';

class UserBottomNavBar extends StatelessWidget {
  final int currentIndex;
  final Function(int) onTap;

  const UserBottomNavBar({Key? key, required this.currentIndex, required this.onTap}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return BottomNavigationBar(
      type: BottomNavigationBarType.fixed,
      currentIndex: currentIndex,
      onTap: onTap,
      items: const [
        BottomNavigationBarItem(
          icon: Icon(Icons.home),
          label: '首页',
        ),
        BottomNavigationBarItem(
          icon: Icon(Icons.shopping_bag),
          label: '我的订单',
        ),
        BottomNavigationBarItem(
          icon: Icon(Icons.location_on),
          label: '收货地址',
        ),
        BottomNavigationBarItem(
          icon: Icon(Icons.favorite),
          label: '我的收藏',
        ),
        BottomNavigationBarItem(
          icon: Icon(Icons.comment),
          label: '我的评价',
        ),
        BottomNavigationBarItem(
          icon: Icon(Icons.account_balance_wallet),
          label: '我的钱包',
        ),
        BottomNavigationBarItem(
          icon: Icon(Icons.settings),
          label: '设置',
        ),
      ],
    );
  }
}