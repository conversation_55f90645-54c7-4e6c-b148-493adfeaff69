import 'package:flutter/material.dart';
import '../../../shared/routes/app_routes.dart';

class UserInfoCard extends StatelessWidget {
  final String username;
  final String email;
  final String avatarUrl;
  final String userId;

  const UserInfoCard({
    Key? key,
    required this.username,
    required this.email,
    required this.avatarUrl,
    required this.userId,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          children: [
            CircleAvatar(
              radius: 40,
              backgroundImage: avatarUrl.isNotEmpty
                  ? NetworkImage(avatarUrl)
                  : null,
              child: avatarUrl.isEmpty
                  ? const Icon(Icons.person, size: 40)
                  : null,
            ),
            const SizedBox(height: 16),
            Text(
              username,
              style: const TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              email,
              style: const TextStyle(
                fontSize: 16,
                color: Colors.grey,
              ),
            ),
            const SizedBox(height: 16),
            Sized<PERSON>ox(
              width: double.infinity,
              child: ElevatedButton(
                onPressed: () {
                  // 导航到用户详情页面
                  Navigator.pushNamed(context, AppRoutes.userProfile, arguments: {
                    'userId': userId,
                  });
                },
                child: const Text('编辑资料'),
              ),
            ),
          ],
        ),
      ),
    );
  }
}