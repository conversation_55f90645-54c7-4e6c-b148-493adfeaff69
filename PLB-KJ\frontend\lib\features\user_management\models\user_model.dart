class User {
  final String id;
  final String username;
  final String email;
  final String role;
  final int status;
  final DateTime? createdAt;
  final DateTime? updatedAt;
  final DateTime? lastLogin;
  final String? avatar;
  final String? phone;
  final String? firstName;
  final String? lastName;

  User({
    required this.id,
    required this.username,
    required this.email,
    required this.role,
    required this.status,
    this.createdAt,
    this.updatedAt,
    this.lastLogin,
    this.avatar,
    this.phone,
    this.firstName,
    this.lastName,
  });

  factory User.fromJson(Map<String, dynamic> json) {
    return User(
      id: json['id']?.toString() ?? '',
      username: json['username'] ?? '',
      email: json['email'] ?? '',
      role: json['role'] ?? 'user',
      status: json['status'] ?? 1,
      createdAt: json['created_at'] != null 
          ? DateTime.tryParse(json['created_at']) 
          : null,
      updatedAt: json['updated_at'] != null 
          ? DateTime.tryParse(json['updated_at']) 
          : null,
      lastLogin: json['last_login'] != null 
          ? DateTime.tryParse(json['last_login']) 
          : null,
      avatar: json['avatar'],
      phone: json['phone'],
      firstName: json['first_name'],
      lastName: json['last_name'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'username': username,
      'email': email,
      'role': role,
      'status': status,
      'created_at': createdAt?.toIso8601String(),
      'updated_at': updatedAt?.toIso8601String(),
      'last_login': lastLogin?.toIso8601String(),
      'avatar': avatar,
      'phone': phone,
      'first_name': firstName,
      'last_name': lastName,
    };
  }

  User copyWith({
    String? id,
    String? username,
    String? email,
    String? role,
    int? status,
    DateTime? createdAt,
    DateTime? updatedAt,
    DateTime? lastLogin,
    String? avatar,
    String? phone,
    String? firstName,
    String? lastName,
  }) {
    return User(
      id: id ?? this.id,
      username: username ?? this.username,
      email: email ?? this.email,
      role: role ?? this.role,
      status: status ?? this.status,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      lastLogin: lastLogin ?? this.lastLogin,
      avatar: avatar ?? this.avatar,
      phone: phone ?? this.phone,
      firstName: firstName ?? this.firstName,
      lastName: lastName ?? this.lastName,
    );
  }

  @override
  String toString() {
    return 'User{id: $id, username: $username, email: $email, role: $role, status: $status}';
  }

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is User &&
          runtimeType == other.runtimeType &&
          id == other.id;

  @override
  int get hashCode => id.hashCode;
}

class CreateUserRequest {
  final String username;
  final String email;
  final String password;
  final String role;
  final int status;
  final String? phone;
  final String? firstName;
  final String? lastName;

  CreateUserRequest({
    required this.username,
    required this.email,
    required this.password,
    required this.role,
    this.status = 1,
    this.phone,
    this.firstName,
    this.lastName,
  });

  Map<String, dynamic> toJson() {
    return {
      'username': username,
      'email': email,
      'password': password,
      'role': role,
      'status': status,
      'phone': phone,
      'first_name': firstName,
      'last_name': lastName,
    };
  }
}

class UpdateUserRequest {
  final String? username;
  final String? email;
  final String? role;
  final int? status;
  final String? phone;
  final String? firstName;
  final String? lastName;

  UpdateUserRequest({
    this.username,
    this.email,
    this.role,
    this.status,
    this.phone,
    this.firstName,
    this.lastName,
  });

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = {};
    if (username != null) data['username'] = username;
    if (email != null) data['email'] = email;
    if (role != null) data['role'] = role;
    if (status != null) data['status'] = status;
    if (phone != null) data['phone'] = phone;
    if (firstName != null) data['first_name'] = firstName;
    if (lastName != null) data['last_name'] = lastName;
    return data;
  }
}

class UserListResponse {
  final List<User> users;
  final int total;
  final int page;
  final int pageSize;
  final int totalPages;

  UserListResponse({
    required this.users,
    required this.total,
    required this.page,
    required this.pageSize,
    required this.totalPages,
  });

  factory UserListResponse.fromJson(Map<String, dynamic> json) {
    return UserListResponse(
      users: (json['data'] as List<dynamic>?)
          ?.map((item) => User.fromJson(item as Map<String, dynamic>))
          .toList() ?? [],
      total: json['total'] ?? 0,
      page: json['page'] ?? 1,
      pageSize: json['page_size'] ?? 20,
      totalPages: json['total_pages'] ?? 0,
    );
  }
}

class UserStatsResponse {
  final int totalUsers;
  final int activeUsers;
  final int adminUsers;
  final int regularUsers;
  final int customerUsers;
  final int inactiveUsers;

  UserStatsResponse({
    required this.totalUsers,
    required this.activeUsers,
    required this.adminUsers,
    required this.regularUsers,
    required this.customerUsers,
    required this.inactiveUsers,
  });

  factory UserStatsResponse.fromJson(Map<String, dynamic> json) {
    return UserStatsResponse(
      totalUsers: json['total_users'] ?? 0,
      activeUsers: json['active_users'] ?? 0,
      adminUsers: json['admin_users'] ?? 0,
      regularUsers: json['regular_users'] ?? 0,
      customerUsers: json['customer_users'] ?? 0,
      inactiveUsers: json['inactive_users'] ?? 0,
    );
  }
}
