import 'package:flutter/material.dart';
import '../../../shared/widgets/custom_card.dart';
import '../../../shared/widgets/custom_list_tile.dart';
import '../../../shared/widgets/loading_indicator.dart';
import '../../../shared/widgets/error_display.dart';
import '../user_service.dart';
import '../../../core/network/api_service.dart';

class UserProfileScreen extends StatefulWidget {
  final String userId;

  const UserProfileScreen({Key? key, required this.userId}) : super(key: key);

  @override
  State<UserProfileScreen> createState() => _UserProfileScreenState();
}

class _UserProfileScreenState extends State<UserProfileScreen> {
  final UserService _userService = UserService(ApiService());
  Map<String, dynamic>? _user;
  bool _isLoading = false;
  String? _errorMessage;

  @override
  void initState() {
    super.initState();
    _loadUserDetails();
  }

  Future<void> _loadUserDetails() async {
    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      final response = await _userService.getUserDetails(widget.userId);
      if (response.success && response.data != null) {
        setState(() {
          _user = response.data!;
        });
      } else {
        setState(() {
          _errorMessage = response.message ?? '获取用户详情失败';
        });
      }
    } catch (e) {
      setState(() {
        _errorMessage = '获取用户详情时发生错误';
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('用户详情'),
        centerTitle: true,
        actions: [
          IconButton(
            onPressed: _loadUserDetails,
            icon: const Icon(Icons.refresh),
          ),
        ],
      ),
      body: RefreshIndicator(
        onRefresh: _loadUserDetails,
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: _isLoading
              ? const LoadingIndicator()
              : _errorMessage != null
                  ? ErrorDisplay(
                      message: _errorMessage!,
                      onRetry: _loadUserDetails,
                    )
                  : _user == null
                      ? const Center(
                          child: Text('暂无用户数据'),
                        )
                      : ListView(
                          children: [
                            CustomCard(
                              child: Column(
                                children: [
                                  CircleAvatar(
                                    radius: 50,
                                    backgroundColor: Theme.of(context).primaryColor,
                                    child: Text(
                                      _user!['username']
                                              ?.toString()
                                              .substring(0, 1)
                                              .toUpperCase() ??
                                          'U',
                                      style: const TextStyle(
                                        fontSize: 40,
                                        color: Colors.white,
                                      ),
                                    ),
                                  ),
                                  const SizedBox(height: 16),
                                  Text(
                                    _user!['username'] ?? '未知用户',
                                    style: Theme.of(context).textTheme.headlineSmall,
                                  ),
                                  const SizedBox(height: 8),
                                  Text(
                                    _user!['email'] ?? '无邮箱信息',
                                    style: Theme.of(context).textTheme.bodyMedium,
                                  ),
                                ],
                              ),
                            ),
                            const SizedBox(height: 16),
                            CustomCard(
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text(
                                    '用户信息',
                                    style: Theme.of(context).textTheme.headlineSmall,
                                  ),
                                  const SizedBox(height: 16),
                                  CustomListTile(
                                    title: const Text('用户ID'),
                                    subtitle: Text(_user!['id'].toString()),
                                  ),
                                  CustomListTile(
                                    title: const Text('用户名'),
                                    subtitle: Text(_user!['username'] ?? '未知'),
                                  ),
                                  CustomListTile(
                                    title: const Text('邮箱'),
                                    subtitle: Text(_user!['email'] ?? '无'),
                                  ),
                                  CustomListTile(
                                    title: const Text('创建时间'),
                                    subtitle: Text(_user!['created_at'] ?? '未知'),
                                  ),
                                  CustomListTile(
                                    title: const Text('更新时间'),
                                    subtitle: Text(_user!['updated_at'] ?? '未知'),
                                  ),
                                ],
                              ),
                            ),
                          ],
                        ),
        ),
      ),
    );
  }
}