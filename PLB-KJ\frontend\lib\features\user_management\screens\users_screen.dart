import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import '../../../shared/widgets/loading_indicator.dart';
import '../../../shared/widgets/error_display.dart';
import '../../../shared/widgets/custom_card.dart';
import '../../../shared/widgets/custom_button.dart';
import '../../../shared/widgets/custom_search_bar.dart';
import '../../../shared/widgets/custom_chip.dart';
import '../../../shared/widgets/message_snackbar.dart';
import '../../../shared/widgets/custom_modal_bottom_sheet.dart';
import '../user_service.dart';
import '../../../core/network/api_service.dart';
import '../../../shared/utils/date_formatter.dart';

class UsersScreen extends StatefulWidget {
  const UsersScreen({Key? key}) : super(key: key);

  @override
  State<UsersScreen> createState() => _UsersScreenState();
}

class _UsersScreenState extends State<UsersScreen> {
  final UserService _userService = UserService(ApiService());
  List<Map<String, dynamic>> _users = [];
  List<Map<String, dynamic>> _filteredUsers = [];
  bool _isLoading = false;
  String? _errorMessage;
  String _searchQuery = '';
  String _selectedRole = '全部';
  final List<String> _roles = ['全部', 'admin', 'user', 'customer'];

  @override
  void initState() {
    super.initState();
    _loadUsers();
  }

  Future<void> _loadUsers() async {
    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      final response = await _userService.getUsers();
      if (response.success && response.data != null) {
        setState(() {
          _users = response.data!;
          _filteredUsers = _users;
          _applyFilters();
        });
      } else {
        setState(() {
          _errorMessage = response.message ?? '获取用户列表失败';
        });
      }
    } catch (e) {
      setState(() {
        _errorMessage = '获取用户列表时发生错误';
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  void _applyFilters() {
    setState(() {
      _filteredUsers = _users.where((user) {
        final matchesSearch = _searchQuery.isEmpty ||
            user['username']
                .toString()
                .toLowerCase()
                .contains(_searchQuery.toLowerCase()) ||
            user['email']
                .toString()
                .toLowerCase()
                .contains(_searchQuery.toLowerCase());

        final matchesRole = _selectedRole == '全部' ||
            user['role'].toString() == _selectedRole;

        return matchesSearch && matchesRole;
      }).toList();
    });
  }

  void _onSearchChanged(String query) {
    setState(() {
      _searchQuery = query;
      _applyFilters();
    });
  }

  void _onRoleSelected(String role) {
    setState(() {
      _selectedRole = role;
      _applyFilters();
    });
  }

  void _showUserDetails(Map<String, dynamic> user) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder: (BuildContext context) {
        return CustomModalBottomSheet(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Padding(
                padding: const EdgeInsets.all(16.0),
                child: Text(
                  '用户详情',
                  style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                ),
              ),
              _buildUserDetailsContent(user),
            ],
          ),
        );
      },
    );
  }

  Widget _buildUserDetailsContent(Map<String, dynamic> user) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildDetailRow('用户ID', user['id'].toString()),
        _buildDetailRow('用户名', user['username'].toString()),
        _buildDetailRow('邮箱', user['email'].toString()),
        _buildDetailRow('角色', user['role'].toString()),
        _buildDetailRow('状态', user['status'] == 1 ? '启用' : '禁用'),
        _buildDetailRow(
          '创建时间',
          DateFormatter.parse(user['created_at']) != null
              ? DateFormatter.formatYYYYMMDDHHMMSS(
                  DateFormatter.parse(user['created_at'])!)
              : '未知',
        ),
        _buildDetailRow(
          '更新时间',
          DateFormatter.parse(user['updated_at']) != null
              ? DateFormatter.formatYYYYMMDDHHMMSS(
                  DateFormatter.parse(user['updated_at'])!)
              : '未知',
        ),
        const SizedBox(height: 20),
        Center(
          child: CustomButton(
            text: '编辑用户',
            onPressed: () {
              Navigator.pop(context);
              // _showEditUserDialog(user);
            },
          ),
        ),
      ],
    );
  }

  Widget _buildDetailRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8.0),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 80,
            child: Text(
              '$label:',
              style: const TextStyle(fontWeight: FontWeight.bold),
            ),
          ),
          const SizedBox(width: 10),
          Expanded(
            child: Text(value),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('用户管理'),
        centerTitle: true,
        actions: [
          IconButton(
            onPressed: _loadUsers,
            icon: const Icon(Icons.refresh),
          ),
        ],
      ),
      body: RefreshIndicator(
        onRefresh: _loadUsers,
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: _isLoading
              ? const LoadingIndicator()
              : _errorMessage != null
                  ? ErrorDisplay(
                      message: _errorMessage!,
                      onRetry: _loadUsers,
                    )
                  : _users.isEmpty
                      ? const Center(
                          child: Text('暂无用户数据'),
                        )
                      : ListView.builder(
                          itemCount: _filteredUsers.length,
                          itemBuilder: (context, index) {
                            final user = _filteredUsers[index];
                            return CustomCard(
                              child: ListTile(
                                leading: CircleAvatar(
                                  child: Text(
                                    user['username']
                                            ?.toString()
                                            .substring(0, 1)
                                            .toUpperCase() ??
                                        'U',
                                  ),
                                ),
                                title: Text(user['username'] ?? '未知用户'),
                                subtitle: Text(user['email'] ?? '无邮箱信息'),
                                trailing: const Icon(Icons.arrow_forward_ios),
                                onTap: () {
                                  _showUserDetails(user);
                                },
                              ),
                            );
                          },
                        ),
        ),
      ),
    );
  }
}