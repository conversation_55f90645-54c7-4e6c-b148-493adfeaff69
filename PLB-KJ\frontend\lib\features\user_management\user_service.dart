import '../../core/network/api_service.dart';
import '../../core/network/api_endpoints.dart';
import 'models/user_model.dart';

class UserService {
  final ApiService _apiService;

  UserService(this._apiService);

  /// 获取用户列表
  Future<UserListResponse> getUsers({
    int page = 1,
    int pageSize = 20,
    String? search,
    String? role,
    int? status,
  }) async {
    try {
      final queryParams = <String, dynamic>{
        'page': page,
        'page_size': pageSize,
      };

      if (search != null && search.isNotEmpty) {
        queryParams['search'] = search;
      }
      if (role != null && role != '全部') {
        queryParams['role'] = role;
      }
      if (status != null) {
        queryParams['status'] = status;
      }

      final response = await _apiService.get(
        ApiEndpoints.users,
        queryParameters: queryParams,
      );

      if (response.statusCode == 200) {
        return UserListResponse.fromJson(response.data as Map<String, dynamic>);
      } else {
        throw Exception('获取用户列表失败: HTTP ${response.statusCode}');
      }
    } catch (e) {
      throw Exception('获取用户列表失败: $e');
    }
  }

  /// 获取用户统计信息
  Future<UserStatsResponse> getUserStats() async {
    try {
      final response = await _apiService.get(ApiEndpoints.userStats);

      if (response.statusCode == 200) {
        return UserStatsResponse.fromJson(response.data as Map<String, dynamic>);
      } else {
        throw Exception('获取用户统计失败: HTTP ${response.statusCode}');
      }
    } catch (e) {
      throw Exception('获取用户统计失败: $e');
    }
  }

  /// 根据ID获取用户详情
  Future<User> getUserById(String userId) async {
    try {
      final response = await _apiService.get('${ApiEndpoints.getUserById}/$userId');

      if (response.statusCode == 200) {
        final data = response.data as Map<String, dynamic>;
        return User.fromJson(data['data'] ?? data);
      } else {
        throw Exception('获取用户详情失败: HTTP ${response.statusCode}');
      }
    } catch (e) {
      throw Exception('获取用户详情失败: $e');
    }
  }

  /// 创建用户
  Future<User> createUser(CreateUserRequest request) async {
    try {
      final response = await _apiService.post(
        ApiEndpoints.createUser,
        data: request.toJson(),
      );

      if (response.statusCode == 200 || response.statusCode == 201) {
        final data = response.data as Map<String, dynamic>;
        return User.fromJson(data['data'] ?? data);
      } else {
        final errorData = response.data as Map<String, dynamic>?;
        final errorMessage = errorData?['message'] ?? errorData?['error'] ?? '创建用户失败';
        throw Exception(errorMessage);
      }
    } catch (e) {
      if (e is Exception) {
        rethrow;
      }
      throw Exception('创建用户失败: $e');
    }
  }

  /// 更新用户
  Future<User> updateUser(String userId, UpdateUserRequest request) async {
    try {
      final response = await _apiService.put(
        '${ApiEndpoints.updateUser}/$userId',
        data: request.toJson(),
      );

      if (response.statusCode == 200) {
        final data = response.data as Map<String, dynamic>;
        return User.fromJson(data['data'] ?? data);
      } else {
        final errorData = response.data as Map<String, dynamic>?;
        final errorMessage = errorData?['message'] ?? errorData?['error'] ?? '更新用户失败';
        throw Exception(errorMessage);
      }
    } catch (e) {
      if (e is Exception) {
        rethrow;
      }
      throw Exception('更新用户失败: $e');
    }
  }

  /// 删除用户
  Future<void> deleteUser(String userId) async {
    try {
      final response = await _apiService.delete('${ApiEndpoints.deleteUser}/$userId');

      if (response.statusCode != 200 && response.statusCode != 204) {
        final errorData = response.data as Map<String, dynamic>?;
        final errorMessage = errorData?['message'] ?? errorData?['error'] ?? '删除用户失败';
        throw Exception(errorMessage);
      }
    } catch (e) {
      if (e is Exception) {
        rethrow;
      }
      throw Exception('删除用户失败: $e');
    }
  }

  /// 批量删除用户
  Future<void> batchDeleteUsers(List<String> userIds) async {
    try {
      final response = await _apiService.post(
        '${ApiEndpoints.deleteUser}/batch',
        data: {'user_ids': userIds},
      );

      if (response.statusCode != 200) {
        final errorData = response.data as Map<String, dynamic>?;
        final errorMessage = errorData?['message'] ?? errorData?['error'] ?? '批量删除用户失败';
        throw Exception(errorMessage);
      }
    } catch (e) {
      if (e is Exception) {
        rethrow;
      }
      throw Exception('批量删除用户失败: $e');
    }
  }

  /// 更新用户状态
  Future<User> updateUserStatus(String userId, int status) async {
    try {
      final response = await _apiService.put(
        '${ApiEndpoints.updateUser}/$userId/status',
        data: {'status': status},
      );

      if (response.statusCode == 200) {
        final data = response.data as Map<String, dynamic>;
        return User.fromJson(data['data'] ?? data);
      } else {
        final errorData = response.data as Map<String, dynamic>?;
        final errorMessage = errorData?['message'] ?? errorData?['error'] ?? '更新用户状态失败';
        throw Exception(errorMessage);
      }
    } catch (e) {
      if (e is Exception) {
        rethrow;
      }
      throw Exception('更新用户状态失败: $e');
    }
  }

  /// 重置用户密码
  Future<void> resetUserPassword(String userId, String newPassword) async {
    try {
      final response = await _apiService.put(
        '${ApiEndpoints.updateUser}/$userId/password',
        data: {'password': newPassword},
      );

      if (response.statusCode != 200) {
        final errorData = response.data as Map<String, dynamic>?;
        final errorMessage = errorData?['message'] ?? errorData?['error'] ?? '重置密码失败';
        throw Exception(errorMessage);
      }
    } catch (e) {
      if (e is Exception) {
        rethrow;
      }
      throw Exception('重置密码失败: $e');
    }
  }
}