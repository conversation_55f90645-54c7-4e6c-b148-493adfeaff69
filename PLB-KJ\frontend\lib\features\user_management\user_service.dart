import '../../core/network/api_service.dart';
import '../../core/network/api_endpoints.dart';

class UserService {
  final ApiService _apiService;

  UserService(this._apiService);

  // 获取所有用户
  Future<ApiResponse<List<Map<String, dynamic>>>> getUsers() async {
    try {
      final response = await _apiService.get(ApiEndpoints.users);
      
      if (response.statusCode == 200) {
        final List<dynamic> data = response.data;
        return ApiResponse.success(
          data.map((item) => item as Map<String, dynamic>).toList(),
        );
      } else {
        return ApiResponse.error('获取用户列表失败');
      }
    } on ApiException catch (e) {
      return ApiResponse.error(e.message, code: e.statusCode);
    } catch (e) {
      return ApiResponse.error('获取用户列表失败: $e');
    }
  }

  // 创建用户
  Future<ApiResponse<Map<String, dynamic>>> createUser({
    required String username,
    required String email,
    required String password,
  }) async {
    try {
      final response = await _apiService.post(
        ApiEndpoints.users,
        data: {
          'username': username,
          'email': email,
          'password_hash': password, // 注意：这里应该是hash后的密码
        },
      );
      
      if (response.statusCode == 201) {
        return ApiResponse.success(response.data);
      } else {
        return ApiResponse.error('创建用户失败');
      }
    } on ApiException catch (e) {
      return ApiResponse.error(e.message, code: e.statusCode);
    } catch (e) {
      return ApiResponse.error('创建用户失败: $e');
    }
  }

  // 获取用户详情
  Future<ApiResponse<Map<String, dynamic>>> getUserDetails(String userId) async {
    try {
      final path = '${ApiEndpoints.users}/$userId';
      final response = await _apiService.get(path);
      
      if (response.statusCode == 200) {
        return ApiResponse.success(response.data);
      } else {
        return ApiResponse.error('获取用户详情失败');
      }
    } on ApiException catch (e) {
      return ApiResponse.error(e.message, code: e.statusCode);
    } catch (e) {
      return ApiResponse.error('获取用户详情失败: $e');
    }
  }

  // 更新用户
  Future<ApiResponse<Map<String, dynamic>>> updateUser({
    required String userId,
    required Map<String, dynamic> userData,
  }) async {
    try {
      final path = '${ApiEndpoints.users}/$userId';
      final response = await _apiService.put(path, data: userData);
      
      if (response.statusCode == 200) {
        return ApiResponse.success(response.data);
      } else {
        return ApiResponse.error('更新用户失败');
      }
    } on ApiException catch (e) {
      return ApiResponse.error(e.message, code: e.statusCode);
    } catch (e) {
      return ApiResponse.error('更新用户失败: $e');
    }
  }

  // 删除用户
  Future<ApiResponse<void>> deleteUser(String userId) async {
    try {
      final path = '${ApiEndpoints.users}/$userId';
      final response = await _apiService.delete(path);
      
      if (response.statusCode == 200) {
        return ApiResponse.success(null);
      } else {
        return ApiResponse.error('删除用户失败');
      }
    } on ApiException catch (e) {
      return ApiResponse.error(e.message, code: e.statusCode);
    } catch (e) {
      return ApiResponse.error('删除用户失败: $e');
    }
  }
}