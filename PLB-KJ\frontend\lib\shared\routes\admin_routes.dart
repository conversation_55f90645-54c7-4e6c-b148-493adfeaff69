import 'package:flutter/material.dart';

// 管理端屏幕导入
import '../../features/authentication/screens/login_screen.dart';
import '../../features/authentication/screens/register_screen.dart';
import '../../features/user_management/screens/users_screen.dart';
import '../../features/user_management/screens/user_profile_screen.dart';
import '../../features/product_management/screens/products_screen.dart';
import '../../features/order_management/screens/orders_screen.dart';
import '../../features/inventory_management/screens/inventory_screen.dart';
import '../../features/shipping_management/screens/shipping_screen.dart';
import '../../features/payment_management/screens/payments_screen.dart';
import '../../features/customer_management/screens/customers_screen.dart';
import '../../features/dashboard/screens/main_screen.dart';
import '../../features/splash/screens/splash_screen.dart';
import '../../features/admin/screens/admin_dashboard_screen.dart';
import '../../features/settings/screens/settings_screen.dart';
import '../../features/settings/screens/background_settings_screen.dart';

// 服务导入
import '../../features/authentication/auth_service.dart';
import '../../core/network/api_service.dart';

class AdminRoutes {
  static const String splash = '/';
  static const String login = '/login';
  static const String register = '/register';
  static const String dashboard = '/dashboard';
  static const String users = '/users';
  static const String userProfile = '/user-profile';
  static const String products = '/products';
  static const String orders = '/orders';
  static const String inventory = '/inventory';
  static const String shipping = '/shipping';
  static const String payments = '/payments';
  static const String customers = '/customers';
  static const String settings = '/settings';
  static const String backgroundSettings = '/settings/background';

  static Route<dynamic> generateRoute(RouteSettings settings) {
    switch (settings.name) {
      case AdminRoutes.splash:
        return MaterialPageRoute(
          builder: (_) => SplashScreen(
            authService: AuthService(ApiService()),
            isAdminApp: true,
          ),
        );
      case AdminRoutes.login:
        return MaterialPageRoute(
          builder: (_) => const LoginScreen(
            loginType: LoginType.admin,
            title: '管理员登录',
          ),
        );
      case AdminRoutes.register:
        return MaterialPageRoute(
          builder: (_) => const RegisterScreen(
            isAdminRegister: true,
          ),
        );
      case AdminRoutes.dashboard:
        return MaterialPageRoute(builder: (_) => const AdminDashboardScreen());
      case AdminRoutes.users:
        return MaterialPageRoute(builder: (_) => const UsersScreen());
      case AdminRoutes.userProfile:
        final args = settings.arguments as Map<String, dynamic>?;
        return MaterialPageRoute(
          builder: (_) => UserProfileScreen(
            userId: args?['userId'] ?? '',
          ),
        );
      case AdminRoutes.products:
        return MaterialPageRoute(builder: (_) => const ProductsScreen());
      case AdminRoutes.orders:
        return MaterialPageRoute(builder: (_) => const OrdersScreen());
      case AdminRoutes.inventory:
        return MaterialPageRoute(builder: (_) => const InventoryScreen());
      case AdminRoutes.shipping:
        return MaterialPageRoute(builder: (_) => const ShippingScreen());
      case AdminRoutes.payments:
        return MaterialPageRoute(builder: (_) => const PaymentsScreen());
      case AdminRoutes.customers:
        return MaterialPageRoute(builder: (_) => const CustomersScreen());
      case AdminRoutes.settings:
        return MaterialPageRoute(builder: (_) => const SettingsScreen());
      case AdminRoutes.backgroundSettings:
        return MaterialPageRoute(builder: (_) => const BackgroundSettingsScreen());
      default:
        return _errorRoute();
    }
  }

  static Route<dynamic> _errorRoute() {
    return MaterialPageRoute(
      builder: (_) => Scaffold(
        appBar: AppBar(title: const Text('错误')),
        body: const Center(
          child: Text('页面未找到'),
        ),
      ),
    );
  }
}

class UserProfileScreen extends StatelessWidget {
  final String userId;

  const UserProfileScreen({Key? key, required this.userId}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('用户详情')),
      body: Center(
        child: Text('用户详情屏幕 - 用户ID: $userId'),
      ),
    );
  }
}
