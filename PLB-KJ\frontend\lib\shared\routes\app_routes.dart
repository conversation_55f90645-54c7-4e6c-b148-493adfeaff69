import 'package:flutter/material.dart';

// 屏幕导入
import '../../features/authentication/screens/login_screen.dart';
import '../../features/authentication/screens/login_selection_screen.dart';
import '../../features/authentication/screens/unified_login_screen.dart';
import '../../features/authentication/screens/register_screen.dart';
import '../../features/user_management/screens/users_screen.dart';
import '../../features/user_management/screens/user_profile_screen.dart';
import '../../features/product_management/screens/products_screen.dart';
import '../../features/order_management/screens/orders_screen.dart';
import '../../features/inventory_management/screens/inventory_screen.dart';
import '../../features/shipping_management/screens/shipping_screen.dart';
import '../../features/payment_management/screens/payments_screen.dart';
import '../../features/customer_management/screens/customers_screen.dart';
import '../../features/dashboard/screens/main_screen.dart';
import '../../features/splash/screens/splash_screen.dart';
import '../../features/user_center/screens/user_center_screen.dart';
import '../../features/settings/screens/settings_screen.dart';
import '../../features/settings/screens/background_settings_screen.dart';
// import '../../features/dashboard/screens/dashboard_screen.dart';

// 服务导入
import '../../features/authentication/auth_service.dart';
import '../../core/network/api_service.dart';

class AppRoutes {
  static const String splash = '/';
  static const String loginSelection = '/login-selection';
  static const String login = '/login';
  static const String unifiedLogin = '/unified-login';
  static const String register = '/register';
  static const String dashboard = '/dashboard';
  static const String userCenter = '/user-center';
  static const String adminUsers = '/admin/users';
  static const String userProfile = '/user-profile';
  static const String settings = '/settings';
  static const String backgroundSettings = '/settings/background';

  static Route<dynamic> generateRoute(RouteSettings settings) {
    switch (settings.name) {
      case splash:
        return MaterialPageRoute(
            builder: (_) => SplashScreen(authService: AuthService(ApiService())));
      case loginSelection:
        return MaterialPageRoute(builder: (_) => const LoginSelectionScreen());
      case unifiedLogin:
        final args = settings.arguments as Map<String, dynamic>?;
        return MaterialPageRoute(
          builder: (_) => UnifiedLoginScreen(
            isAdminLogin: args?['isAdmin'] ?? true,
          ),
        );
      case register:
        final args = settings.arguments as Map<String, dynamic>?;
        return MaterialPageRoute(
          builder: (_) => RegisterScreen(
            isAdminRegister: args?['isAdmin'] ?? false,
          ),
        );
      case dashboard:
        return MaterialPageRoute(builder: (_) => const DashboardScreen());
      case userCenter:
        return MaterialPageRoute(builder: (_) => const UserCenterScreen());
      case adminUsers:
        return MaterialPageRoute(builder: (_) => const UsersScreen());
      case userProfile:
        final args = settings.arguments as Map<String, dynamic>?;
        return MaterialPageRoute(
          builder: (_) => UserProfileScreen(
            userId: args?['userId'] ?? '',
          ),
        );
      case settings:
        return MaterialPageRoute(builder: (_) => const SettingsScreen());
      case backgroundSettings:
        return MaterialPageRoute(builder: (_) => const BackgroundSettingsScreen());
      default:
        return _errorRoute();
    }
  }

  static Route<dynamic> _errorRoute() {
    return MaterialPageRoute(
      builder: (_) => Scaffold(
        body: Center(
          child: Text('页面未找到'),
        ),
      ),
    );
  }
}

// 占位屏幕组件
// 已移动到 features/splash/screens/splash_screen.dart

class DashboardScreen extends StatelessWidget {
  const DashboardScreen({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return const MainScreen();
  }
}

class UserProfileScreen extends StatelessWidget {
  final String userId;

  const UserProfileScreen({Key? key, required this.userId}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Center(
        child: Text('用户详情屏幕 - 用户ID: $userId'),
      ),
    );
  }
}