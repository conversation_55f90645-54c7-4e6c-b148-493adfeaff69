import 'package:flutter/material.dart';

// 用户端屏幕导入
import '../../features/authentication/screens/login_screen.dart';
import '../../features/authentication/screens/register_screen.dart';
import '../../features/splash/screens/splash_screen.dart';
import '../../features/user_center/screens/user_center_screen.dart';
import '../../features/user_center/screens/my_orders_screen.dart';
import '../../features/user_center/screens/shipping_addresses_screen.dart';
import '../../features/user_center/screens/my_favorites_screen.dart';
import '../../features/user_center/screens/my_reviews_screen.dart';
import '../../features/user_center/screens/my_wallet_screen.dart';
import '../../features/user_center/screens/settings_screen.dart';

// 服务导入
import '../../features/authentication/auth_service.dart';
import '../../core/network/api_service.dart';

class UserRoutes {
  static const String splash = '/';
  static const String login = '/login';
  static const String register = '/register';
  static const String userCenter = '/user-center';
  static const String myOrders = '/my-orders';
  static const String shippingAddresses = '/shipping-addresses';
  static const String myFavorites = '/my-favorites';
  static const String myReviews = '/my-reviews';
  static const String myWallet = '/my-wallet';
  static const String settings = '/settings';

  static Route<dynamic> generateRoute(RouteSettings settings) {
    switch (settings.name) {
      case UserRoutes.splash:
        return MaterialPageRoute(
          builder: (_) => SplashScreen(
            authService: AuthService(ApiService()),
            isAdminApp: false,
          ),
        );
      case UserRoutes.login:
        return MaterialPageRoute(
          builder: (_) => const LoginScreen(
            loginType: LoginType.user,
            title: '用户登录',
          ),
        );
      case UserRoutes.register:
        return MaterialPageRoute(
          builder: (_) => const RegisterScreen(
            isAdminRegister: false,
          ),
        );
      case UserRoutes.userCenter:
        return MaterialPageRoute(builder: (_) => const UserCenterScreen());
      case UserRoutes.myOrders:
        return MaterialPageRoute(builder: (_) => const MyOrdersScreen());
      case UserRoutes.shippingAddresses:
        return MaterialPageRoute(builder: (_) => const ShippingAddressesScreen());
      case UserRoutes.myFavorites:
        return MaterialPageRoute(builder: (_) => const MyFavoritesScreen());
      case UserRoutes.myReviews:
        return MaterialPageRoute(builder: (_) => const MyReviewsScreen());
      case UserRoutes.myWallet:
        return MaterialPageRoute(builder: (_) => const MyWalletScreen());
      case UserRoutes.settings:
        return MaterialPageRoute(
          builder: (_) => const SettingsScreen(),
        );
      default:
        return _errorRoute();
    }
  }

  static Route<dynamic> _errorRoute() {
    return MaterialPageRoute(
      builder: (_) => Scaffold(
        appBar: AppBar(title: const Text('错误')),
        body: const Center(
          child: Text('页面未找到'),
        ),
      ),
    );
  }
}
