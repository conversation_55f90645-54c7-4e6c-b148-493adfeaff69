class DateFormatter {
  // 格式化日期为 YYYY-MM-DD
  static String formatYYYYMMDD(DateTime date) {
    return "${date.year}-${date.month.toString().padLeft(2, '0')}-${date.day.toString().padLeft(2, '0')}";
  }

  // 格式化日期为 YYYY-MM-DD HH:MM:SS
  static String formatYYYYMMDDHHMMSS(DateTime date) {
    return "${date.year}-${date.month.toString().padLeft(2, '0')}-${date.day.toString().padLeft(2, '0')} "
        "${date.hour.toString().padLeft(2, '0')}:${date.minute.toString().padLeft(2, '0')}:${date.second.toString().padLeft(2, '0')}";
  }

  // 从字符串解析日期
  static DateTime? parse(String dateString) {
    try {
      return DateTime.parse(dateString);
    } catch (e) {
      return null;
    }
  }
}