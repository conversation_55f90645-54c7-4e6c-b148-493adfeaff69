import 'package:shared_preferences/shared_preferences.dart';
import 'dart:convert';

class LocalStorage {
  static SharedPreferences? _prefs;

  static Future<void> init() async {
    _prefs = await SharedPreferences.getInstance();
  }

  // 保存字符串
  static Future<bool> setString(String key, String value) async {
    return await _prefs?.setString(key, value) ?? false;
  }

  // 获取字符串
  static String? getString(String key) {
    return _prefs?.getString(key);
  }

  // 保存整数
  static Future<bool> setInt(String key, int value) async {
    return await _prefs?.setInt(key, value) ?? false;
  }

  // 获取整数
  static int? getInt(String key) {
    return _prefs?.getInt(key);
  }

  // 保存布尔值
  static Future<bool> setBool(String key, bool value) async {
    return await _prefs?.setBool(key, value) ?? false;
  }

  // 获取布尔值
  static bool? getBool(String key) {
    return _prefs?.getBool(key);
  }

  // 保存对象（转换为JSON字符串）
  static Future<bool> setObject(String key, dynamic value) async {
    final jsonString = jsonEncode(value);
    return await setString(key, jsonString);
  }

  // 获取对象（从JSON字符串解析）
  static T? getObject<T>(String key, T Function(Map<String, dynamic>) fromJson) {
    final jsonString = getString(key);
    if (jsonString == null) return null;
    
    try {
      final Map<String, dynamic> jsonMap = jsonDecode(jsonString);
      return fromJson(jsonMap);
    } catch (e) {
      return null;
    }
  }

  // 删除键值
  static Future<bool> remove(String key) async {
    return await _prefs?.remove(key) ?? false;
  }

  // 清空所有数据
  static Future<bool> clear() async {
    return await _prefs?.clear() ?? false;
  }
}