import 'package:intl/intl.dart';

class NumberFormatter {
  // 格式化货币
  static String formatCurrency(double amount, {String symbol = '¥'}) {
    final formatter = NumberFormat.currency(
      locale: 'zh_CN',
      symbol: symbol,
      decimalDigits: 2,
    );
    return formatter.format(amount);
  }

  // 格式化大数字（带千位分隔符）
  static String formatLargeNumber(int number) {
    final formatter = NumberFormat.decimalPattern('zh_CN');
    return formatter.format(number);
  }

  // 格式化百分比
  static String formatPercentage(double value, {int decimalDigits = 2}) {
    final formatter = NumberFormat.decimalPatternDigits(
      locale: 'zh_CN',
      decimalDigits: decimalDigits,
    );
    return '${formatter.format(value * 100)}%';
  }
}