import 'package:flutter/material.dart';

class AnimatedButton extends StatefulWidget {
  final String text;
  final VoidCallback? onPressed;  // 修改为可空类型
  final Color? color;
  final Color? textColor;
  final double? minWidth;
  final double? height;
  final BorderSide? borderSide;
  final BorderRadius? borderRadius;
  final bool loading;
  final IconData? icon;

  const AnimatedButton({
    Key? key,
    required this.text,
    this.onPressed,  // 移除 required
    this.color,
    this.textColor,
    this.minWidth,
    this.height,
    this.borderSide,
    this.borderRadius,
    this.loading = false,
    this.icon,
  }) : super(key: key);

  @override
  State<AnimatedButton> createState() => _AnimatedButtonState();
}

class _AnimatedButtonState extends State<AnimatedButton>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _scaleAnimation;
  late Animation<double> _shadowAnimation;
  bool _isPressed = false;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: const Duration(milliseconds: 150),
      vsync: this,
    );
    _scaleAnimation = Tween<double>(begin: 1.0, end: 0.95).animate(
      CurvedAnimation(parent: _controller, curve: Curves.easeInOut),
    );
    
    _shadowAnimation = Tween<double>(begin: 4.0, end: 8.0).animate(
      CurvedAnimation(parent: _controller, curve: Curves.easeInOut),
    );
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  void _onTapDown(TapDownDetails details) {
    if (!widget.loading && widget.onPressed != null) {  // 添加 widget.onPressed != null 检查
      setState(() {
        _isPressed = true;
      });
      _controller.forward();
    }
  }

  void _onTapUp(TapUpDetails details) {
    if (!widget.loading && widget.onPressed != null) {  // 添加 widget.onPressed != null 检查
      setState(() {
        _isPressed = false;
      });
      _controller.reverse();
    }
  }

  void _onTapCancel() {
    if (!widget.loading && widget.onPressed != null) {  // 添加 widget.onPressed != null 检查
      setState(() {
        _isPressed = false;
      });
      _controller.reverse();
    }
  }

  @override
  Widget build(BuildContext context) {
    final defaultColor = widget.color ?? Theme.of(context).primaryColor;
    final defaultTextColor = widget.textColor ?? Colors.white;
    
    return GestureDetector(
      onTapDown: _onTapDown,
      onTapUp: _onTapUp,
      onTapCancel: _onTapCancel,
      child: ScaleTransition(
        scale: _scaleAnimation,
        child: SizedBox(
          width: widget.minWidth,
          height: widget.height,
          child: AnimatedBuilder(
            animation: _shadowAnimation,
            builder: (context, child) {
              return Container(
                decoration: BoxDecoration(
                  boxShadow: [
                    BoxShadow(
                      color: _isPressed 
                          ? defaultColor.withValues(alpha: 0.5)
                          : defaultColor.withValues(alpha: 0.3),
                      blurRadius: _shadowAnimation.value,
                      offset: const Offset(0, 4),
                    ),
                  ],
                ),
                child: child,
              );
            },
            child: ElevatedButton(
              onPressed: widget.loading ? null : widget.onPressed,  // 保持不变，因为 widget.onPressed 现在可以为 null
              style: ElevatedButton.styleFrom(
                backgroundColor: _isPressed 
                    ? defaultColor.withValues(alpha: 0.8)
                    : defaultColor,
                foregroundColor: defaultTextColor,
                side: widget.borderSide,
                shape: RoundedRectangleBorder(
                  borderRadius: widget.borderRadius ?? BorderRadius.circular(12.0),
                ),
                padding: const EdgeInsets.symmetric(vertical: 16, horizontal: 24),
                elevation: 0,
              ),
              child: widget.loading
                  ? const SizedBox(
                      width: 24,
                      height: 24,
                      child: CircularProgressIndicator(
                        strokeWidth: 2,
                        color: Colors.white,
                      ),
                    )
                  : Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        if (widget.icon != null) ...[
                          Icon(widget.icon, size: 20, color: defaultTextColor),
                          const SizedBox(width: 8),
                        ],
                        Text(
                          widget.text,
                          style: const TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ],
                    ),
            ),
          ),
        ),
      ),
    );
  }
}