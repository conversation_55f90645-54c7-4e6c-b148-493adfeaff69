import 'package:flutter/material.dart';

class Breadcrumb extends StatelessWidget {
  final List<BreadcrumbItem> items;
  final VoidCallback? onBack;

  const Breadcrumb({
    Key? key,
    required this.items,
    this.onBack,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      decoration: BoxDecoration(
        border: Border(
          bottom: BorderSide(
            color: Theme.of(context).dividerColor,
            width: 0.5,
          ),
        ),
      ),
      child: Row(
        children: [
          if (onBack != null)
            IconButton(
              icon: const Icon(Icons.arrow_back),
              onPressed: onBack,
              tooltip: '返回',
            ),
          if (onBack != null)
            const SizedBox(width: 8),
          Expanded(
            child: Wrap(
              spacing: 8,
              children: [
                for (int i = 0; i < items.length; i++) ...[
                  if (i > 0)
                    const Icon(
                      Icons.chevron_right,
                      size: 18,
                      color: Colors.grey,
                    ),
                  if (items[i].onTap != null && i < items.length - 1)
                    TextButton(
                      onPressed: items[i].onTap,
                      style: TextButton.styleFrom(
                        padding: const EdgeInsets.symmetric(horizontal: 4),
                        minimumSize: const Size(0, 0),
                      ),
                      child: Text(
                        items[i].title,
                        style: TextStyle(
                          color: Theme.of(context).primaryColor,
                          fontWeight: FontWeight.normal,
                        ),
                      ),
                    )
                  else
                    Text(
                      items[i].title,
                      style: TextStyle(
                        color: i == items.length - 1 
                            ? Theme.of(context).textTheme.titleMedium?.color 
                            : Theme.of(context).primaryColor,
                        fontWeight: i == items.length - 1 
                            ? FontWeight.bold 
                            : FontWeight.normal,
                      ),
                    ),
                ],
              ],
            ),
          ),
        ],
      ),
    );
  }
}

class BreadcrumbItem {
  final String title;
  final VoidCallback? onTap;

  BreadcrumbItem({
    required this.title,
    this.onTap,
  });
}