import 'package:flutter/material.dart';

class CustomChip extends StatelessWidget {
  final String label;
  final VoidCallback? onDeleted;
  final Color? color;
  final Color? labelColor;
  final IconData? icon;

  const CustomChip({
    Key? key,
    required this.label,
    this.onDeleted,
    this.color,
    this.labelColor,
    this.icon,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Chip(
      label: Text(label, style: TextStyle(color: labelColor)),
      deleteIcon: onDeleted != null ? const Icon(Icons.close, size: 18) : null,
      onDeleted: onDeleted,
      backgroundColor: color ?? Theme.of(context).primaryColor.withValues(alpha: 0.1),
      avatar: icon != null ? Icon(icon, size: 18) : null,
    );
  }
}