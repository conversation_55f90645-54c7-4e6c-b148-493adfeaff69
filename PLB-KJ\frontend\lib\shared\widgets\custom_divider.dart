import 'package:flutter/material.dart';

class CustomDivider extends StatelessWidget {
  final double? height;
  final double? thickness;
  final Color? color;
  final EdgeInsetsGeometry? margin;

  const CustomDivider({
    Key? key,
    this.height,
    this.thickness,
    this.color,
    this.margin,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      height: height ?? 1.0,
      margin: margin ?? const EdgeInsets.symmetric(vertical: 8.0),
      decoration: BoxDecoration(
        border: Border(
          bottom: BorderSide(
            color: color ?? Theme.of(context).dividerColor,
            width: thickness ?? 1.0,
          ),
        ),
      ),
    );
  }
}