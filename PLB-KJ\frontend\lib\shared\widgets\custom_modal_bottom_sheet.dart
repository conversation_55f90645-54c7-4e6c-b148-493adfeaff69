import 'package:flutter/material.dart';

class CustomModalBottomSheet extends StatelessWidget {
  final Widget child;
  final double? height;
  final Color? backgroundColor;
  final bool isScrollControlled;

  const CustomModalBottomSheet({
    Key? key,
    required this.child,
    this.height,
    this.backgroundColor,
    this.isScrollControlled = false,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      height: height,
      decoration: BoxDecoration(
        color: backgroundColor ?? Theme.of(context).canvasColor,
        borderRadius: const BorderRadius.vertical(top: Radius.circular(20.0)),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: child,
      ),
    );
  }

  static Future<T?> show<T>(
    BuildContext context,
    Widget child, {
    double? height,
    Color? backgroundColor,
    bool isScrollControlled = false,
  }) async {
    return showModalBottomSheet<T>(
      context: context,
      isScrollControlled: isScrollControlled,
      backgroundColor: Colors.transparent,
      builder: (BuildContext context) {
        return CustomModalBottomSheet(
          child: child,
          height: height,
          backgroundColor: backgroundColor,
          isScrollControlled: isScrollControlled,
        );
      },
    );
  }
}