import 'package:flutter/material.dart';

class CustomProgressBar extends StatelessWidget {
  final double value;
  final double? minHeight;
  final Color? color;
  final Color? backgroundColor;
  final BorderRadius? borderRadius;

  const CustomProgressBar({
    Key? key,
    required this.value,
    this.minHeight,
    this.color,
    this.backgroundColor,
    this.borderRadius,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return LinearProgressIndicator(
      value: value,
      minHeight: minHeight ?? 8.0,
      backgroundColor: backgroundColor ?? Colors.grey[300],
      color: color ?? Theme.of(context).primaryColor,
      borderRadius: borderRadius ?? BorderRadius.circular(4.0),
    );
  }
}