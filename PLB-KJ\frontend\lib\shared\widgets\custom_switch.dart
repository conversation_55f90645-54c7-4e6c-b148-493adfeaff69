import 'package:flutter/material.dart';

class CustomSwitch extends StatelessWidget {
  final bool value;
  final ValueChanged<bool> onChanged;
  final Color? activeThumbColor;
  final Color? inactiveColor;
  final Color? activeTrackColor;
  final Color? inactiveTrackColor;

  const CustomSwitch({
    Key? key,
    required this.value,
    required this.onChanged,
    this.activeThumbColor,
    this.inactiveColor,
    this.activeTrackColor,
    this.inactiveTrackColor,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Switch(
      value: value,
      onChanged: onChanged,
      activeThumbColor: activeThumbColor ?? Colors.white,
      inactiveThumbColor: inactiveColor ?? Colors.white,
      activeTrackColor: activeTrackColor ?? Theme.of(context).primaryColor,
      inactiveTrackColor: inactiveTrackColor ?? Colors.grey,
    );
  }
}