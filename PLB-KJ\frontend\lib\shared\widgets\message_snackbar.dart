import 'package:flutter/material.dart';

class MessageSnackbar {
  static void show(BuildContext context, String message, {bool isError = false}) {
    final color = isError ? Colors.red : Colors.green;
    final textColor = Colors.white;
    
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message, style: TextStyle(color: textColor)),
        backgroundColor: color,
        duration: const Duration(seconds: 3),
        behavior: SnackBarBehavior.floating,
      ),
    );
  }
}