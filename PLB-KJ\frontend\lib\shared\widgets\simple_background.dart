import 'package:flutter/material.dart';
import '../../core/network/api_service.dart';

class SimpleBackground extends StatefulWidget {
  final Widget child;
  final Color backgroundColor;

  const SimpleBackground({
    Key? key,
    required this.child,
    this.backgroundColor = Colors.black,
  }) : super(key: key);

  @override
  State<SimpleBackground> createState() => _SimpleBackgroundState();
}

class _SimpleBackgroundState extends State<SimpleBackground> {
  String _backgroundImage = '';

  @override
  void initState() {
    super.initState();
    _loadBackgroundSetting();
  }

  Future<void> _loadBackgroundSetting() async {
    try {
      final response = await ApiService.getBackgroundSetting();
      if (response != null && response['background_image'] != null) {
        setState(() {
          _backgroundImage = response['background_image'];
        });
      }
    } catch (e) {
      // Handle error silently
      print('Failed to load background setting: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: _backgroundImage.isNotEmpty
          ? BoxDecoration(
              image: DecorationImage(
                image: NetworkImage(_backgroundImage),
                fit: BoxFit.cover,
              ),
            )
          : BoxDecoration(
              color: widget.backgroundColor,
            ),
      child: widget.child,
    );
  }
}