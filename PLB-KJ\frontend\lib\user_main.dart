import 'package:flutter/material.dart';
import 'shared/routes/user_routes.dart';
import 'shared/theme/app_theme.dart';

void main() {
  runApp(const UserApp());
}

class UserApp extends StatelessWidget {
  const UserApp({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: '跨境电商管理系统 - 用户端',
      theme: AppTheme.lightTheme,
      darkTheme: AppTheme.darkTheme,
      themeMode: ThemeMode.system,
      onGenerateRoute: UserRoutes.generateRoute,
      initialRoute: UserRoutes.splash,
      debugShowCheckedModeBanner: false,
    );
  }
}
