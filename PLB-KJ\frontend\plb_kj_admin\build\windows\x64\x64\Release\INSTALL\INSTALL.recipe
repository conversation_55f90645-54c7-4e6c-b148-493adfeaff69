﻿<?xml version="1.0" encoding="utf-8"?>
<Project>
  <ProjectOutputs>
    <ProjectOutput>
      <FullPath>D:\PLB-Links\PLB-KJ\frontend\plb_kj_admin\build\windows\x64\x64\Release\ZERO_CHECK</FullPath>
    </ProjectOutput>
    <ProjectOutput>
      <FullPath>D:\PLB-Links\PLB-KJ\frontend\plb_kj_admin\build\windows\x64\flutter\x64\Release\flutter_assemble</FullPath>
    </ProjectOutput>
    <ProjectOutput>
      <FullPath>D:\PLB-Links\PLB-KJ\frontend\plb_kj_admin\build\windows\x64\plugins\screen_retriever\Release\screen_retriever_plugin.dll</FullPath>
    </ProjectOutput>
    <ProjectOutput>
      <FullPath>D:\PLB-Links\PLB-KJ\frontend\plb_kj_admin\build\windows\x64\plugins\window_manager\Release\window_manager_plugin.dll</FullPath>
    </ProjectOutput>
    <ProjectOutput>
      <FullPath>D:\PLB-Links\PLB-KJ\frontend\plb_kj_admin\build\windows\x64\runner\Release\plb_kj_admin.exe</FullPath>
    </ProjectOutput>
    <ProjectOutput>
      <FullPath>D:\PLB-Links\PLB-KJ\frontend\plb_kj_admin\build\windows\x64\x64\Release\ALL_BUILD</FullPath>
    </ProjectOutput>
    <ProjectOutput>
      <FullPath>D:\PLB-Links\PLB-KJ\frontend\plb_kj_admin\build\windows\x64\x64\Release\INSTALL</FullPath>
    </ProjectOutput>
  </ProjectOutputs>
  <ContentFiles />
  <SatelliteDlls />
  <NonRecipeFileRefs />
</Project>