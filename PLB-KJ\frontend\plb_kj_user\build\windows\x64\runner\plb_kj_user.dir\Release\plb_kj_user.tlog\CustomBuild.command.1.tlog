^D:\PLB-LINKS\PLB-KJ\FRONTEND\PLB_KJ_USER\WINDOWS\RUNNER\CMAKELISTS.TXT
setlocal
"C:\Program Files\Microsoft Visual Studio\2022\Enterprise\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -SD:/PLB-Links/PLB-KJ/frontend/plb_kj_user/windows -BD:/PLB-Links/PLB-KJ/frontend/plb_kj_user/build/windows/x64 --check-stamp-file D:/PLB-Links/PLB-KJ/frontend/plb_kj_user/build/windows/x64/runner/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
