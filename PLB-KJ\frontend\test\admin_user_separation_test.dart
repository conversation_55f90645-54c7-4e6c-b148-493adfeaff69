import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import '../lib/features/admin/screens/admin_dashboard_screen.dart';
import '../lib/features/user_center/screens/user_center_screen.dart';
import '../lib/features/authentication/screens/unified_login_screen.dart';
import '../lib/shared/theme/app_theme.dart';

void main() {
  group('管理员后台与用户中心分离测试', () {
    testWidgets('管理员后台界面应该正确显示', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          theme: AppTheme.lightTheme,
          home: const AdminDashboardScreen(),
        ),
      );

      await tester.pumpAndSettle();

      // 验证管理员后台基本元素
      expect(find.text('管理后台'), findsOneWidget);
      expect(find.text('跨境电商系统'), findsOneWidget);
      expect(find.text('仪表盘'), findsAtLeastNWidgets(1));
      expect(find.text('用户管理'), findsOneWidget);
      expect(find.text('产品管理'), findsOneWidget);
      expect(find.text('订单管理'), findsOneWidget);
      
      // 验证统计卡片
      expect(find.text('总用户数'), findsOneWidget);
      expect(find.text('总订单数'), findsOneWidget);
      expect(find.text('总收入'), findsOneWidget);
      expect(find.text('产品数量'), findsOneWidget);
      
      // 验证快捷操作
      expect(find.text('快捷操作'), findsOneWidget);
      expect(find.text('最近活动'), findsOneWidget);
    });

    testWidgets('用户中心界面应该正确显示', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          theme: AppTheme.lightTheme,
          home: const UserCenterScreen(),
        ),
      );

      await tester.pumpAndSettle();

      // 验证用户中心基本元素
      expect(find.text('张三'), findsOneWidget);
      expect(find.text('<EMAIL>'), findsOneWidget);
      expect(find.text('首页'), findsAtLeastNWidgets(1));
      expect(find.text('我的订单'), findsAtLeastNWidgets(1));
      expect(find.text('收货地址'), findsAtLeastNWidgets(1));
      expect(find.text('我的收藏'), findsAtLeastNWidgets(1));
      expect(find.text('我的钱包'), findsAtLeastNWidgets(1));
      expect(find.text('设置'), findsAtLeastNWidgets(1));
    });

    testWidgets('管理员登录界面应该显示正确的标题和图标', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          theme: AppTheme.lightTheme,
          home: const UnifiedLoginScreen(isAdminLogin: true),
        ),
      );

      await tester.pumpAndSettle();

      // 验证管理员登录界面
      expect(find.text('跨境电商管理系统'), findsOneWidget);
      expect(find.text('管理员登录'), findsOneWidget);
      expect(find.byIcon(Icons.admin_panel_settings), findsOneWidget);
    });

    testWidgets('用户登录界面应该显示正确的标题和图标', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          theme: AppTheme.lightTheme,
          home: const UnifiedLoginScreen(isAdminLogin: false),
        ),
      );

      await tester.pumpAndSettle();

      // 验证用户登录界面
      expect(find.text('跨境电商管理系统'), findsOneWidget);
      expect(find.text('用户登录'), findsOneWidget);
      expect(find.byIcon(Icons.storefront), findsOneWidget);
    });

    testWidgets('管理员后台侧边栏导航应该正常工作', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          theme: AppTheme.lightTheme,
          home: const AdminDashboardScreen(),
        ),
      );

      await tester.pumpAndSettle();

      // 点击用户管理菜单
      await tester.tap(find.text('用户管理'));
      await tester.pumpAndSettle();

      // 验证页面标题更新
      expect(find.text('用户管理'), findsAtLeastNWidgets(1));

      // 点击产品管理菜单
      await tester.tap(find.text('产品管理'));
      await tester.pumpAndSettle();

      // 验证页面标题更新
      expect(find.text('产品管理'), findsAtLeastNWidgets(1));
    });

    testWidgets('用户中心侧边栏导航应该正常工作', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          theme: AppTheme.lightTheme,
          home: const UserCenterScreen(),
        ),
      );

      await tester.pumpAndSettle();

      // 点击我的订单菜单
      await tester.tap(find.text('我的订单').first);
      await tester.pumpAndSettle();

      // 验证页面标题更新
      expect(find.text('我的订单'), findsAtLeastNWidgets(1));

      // 点击收货地址菜单
      await tester.tap(find.text('收货地址').first);
      await tester.pumpAndSettle();

      // 验证页面标题更新
      expect(find.text('收货地址'), findsAtLeastNWidgets(1));
    });

    testWidgets('管理员后台退出登录功能应该正常工作', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          theme: AppTheme.lightTheme,
          home: const AdminDashboardScreen(),
        ),
      );

      await tester.pumpAndSettle();

      // 点击退出登录按钮
      await tester.tap(find.text('退出登录'));
      await tester.pumpAndSettle();

      // 验证确认对话框出现
      expect(find.text('确认退出'), findsOneWidget);
      expect(find.text('您确定要退出管理后台吗？'), findsOneWidget);
      expect(find.text('取消'), findsOneWidget);
      expect(find.text('确定'), findsOneWidget);
    });

    testWidgets('用户中心退出登录功能应该正常工作', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          theme: AppTheme.lightTheme,
          home: const UserCenterScreen(),
        ),
      );

      await tester.pumpAndSettle();

      // 点击退出登录按钮
      await tester.tap(find.text('退出登录'));
      await tester.pumpAndSettle();

      // 验证确认对话框出现
      expect(find.text('确认退出'), findsOneWidget);
      expect(find.text('您确定要退出用户中心吗？'), findsOneWidget);
      expect(find.text('取消'), findsOneWidget);
      expect(find.text('确定'), findsOneWidget);
    });

    testWidgets('管理员后台统计卡片应该显示正确信息', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          theme: AppTheme.lightTheme,
          home: const AdminDashboardScreen(),
        ),
      );

      await tester.pumpAndSettle();

      // 验证统计数据
      expect(find.text('1,234'), findsOneWidget); // 总用户数
      expect(find.text('5,678'), findsOneWidget); // 总订单数
      expect(find.text('¥128,000'), findsOneWidget); // 总收入
      expect(find.text('456'), findsOneWidget); // 产品数量

      // 验证趋势指示器
      expect(find.text('+12%'), findsOneWidget);
      expect(find.text('+8%'), findsOneWidget);
      expect(find.text('+15%'), findsOneWidget);
      expect(find.text('+5%'), findsOneWidget);
    });

    testWidgets('界面应该具有正确的响应式布局', (WidgetTester tester) async {
      // 测试管理员后台在不同尺寸下的表现
      await tester.binding.setSurfaceSize(const Size(1200, 800));
      await tester.pumpWidget(
        MaterialApp(
          theme: AppTheme.lightTheme,
          home: const AdminDashboardScreen(),
        ),
      );

      await tester.pumpAndSettle();

      // 验证侧边栏存在
      expect(find.text('管理后台'), findsOneWidget);
      expect(find.text('仪表盘'), findsAtLeastNWidgets(1));

      // 测试用户中心在不同尺寸下的表现
      await tester.pumpWidget(
        MaterialApp(
          theme: AppTheme.lightTheme,
          home: const UserCenterScreen(),
        ),
      );

      await tester.pumpAndSettle();

      // 验证用户信息存在
      expect(find.text('张三'), findsOneWidget);
      expect(find.text('首页'), findsAtLeastNWidgets(1));

      // 重置屏幕尺寸
      await tester.binding.setSurfaceSize(null);
    });
  });
}
