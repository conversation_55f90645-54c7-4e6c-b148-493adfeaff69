import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import '../lib/features/authentication/screens/unified_login_screen.dart';
import '../lib/shared/theme/app_theme.dart';

void main() {
  group('UnifiedLoginScreen Tests', () {
    testWidgets('应该显示登录界面的基本元素', (WidgetTester tester) async {
      // 构建登录界面
      await tester.pumpWidget(
        MaterialApp(
          theme: AppTheme.lightTheme,
          home: const UnifiedLoginScreen(isAdminLogin: true),
        ),
      );

      // 等待动画完成
      await tester.pumpAndSettle();

      // 验证基本元素是否存在
      expect(find.text('跨境电商管理系统'), findsOneWidget);
      expect(find.text('管理员登录'), findsOneWidget);
      expect(find.text('欢迎回来'), findsOneWidget);
      expect(find.text('请输入您的登录信息'), findsOneWidget);
      
      // 验证输入框
      expect(find.byType(TextFormField), findsAtLeast(2)); // 用户名和密码
      
      // 验证登录按钮
      expect(find.text('登录'), findsOneWidget);
      
      // 验证注册链接
      expect(find.text('还没有账户？'), findsOneWidget);
      expect(find.text('立即注册'), findsOneWidget);
    });

    testWidgets('应该在用户登录模式下显示正确的标题', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          theme: AppTheme.lightTheme,
          home: const UnifiedLoginScreen(isAdminLogin: false),
        ),
      );

      await tester.pumpAndSettle();

      expect(find.text('用户登录'), findsOneWidget);
    });

    testWidgets('应该验证表单输入', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          theme: AppTheme.lightTheme,
          home: const UnifiedLoginScreen(isAdminLogin: true),
        ),
      );

      await tester.pumpAndSettle();

      // 尝试在没有输入的情况下登录
      final loginButton = find.text('登录');
      await tester.tap(loginButton);
      await tester.pumpAndSettle();

      // 应该显示验证错误
      expect(find.text('请输入用户名'), findsOneWidget);
      expect(find.text('请输入密码'), findsOneWidget);
    });

    testWidgets('应该能够切换密码可见性', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          theme: AppTheme.lightTheme,
          home: const UnifiedLoginScreen(isAdminLogin: true),
        ),
      );

      await tester.pumpAndSettle();

      // 查找密码可见性切换按钮
      final visibilityButton = find.byIcon(Icons.visibility_off);
      expect(visibilityButton, findsOneWidget);

      // 点击切换按钮
      await tester.tap(visibilityButton);
      await tester.pumpAndSettle();

      // 验证图标已更改
      expect(find.byIcon(Icons.visibility), findsOneWidget);
    });

    testWidgets('应该能够输入用户名和密码', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          theme: AppTheme.lightTheme,
          home: const UnifiedLoginScreen(isAdminLogin: true),
        ),
      );

      await tester.pumpAndSettle();

      // 输入用户名
      final usernameField = find.byType(TextFormField).first;
      await tester.enterText(usernameField, 'testuser');

      // 输入密码
      final passwordField = find.byType(TextFormField).at(1);
      await tester.enterText(passwordField, 'password123');

      await tester.pumpAndSettle();

      // 验证输入的文本
      expect(find.text('testuser'), findsOneWidget);
      // 密码字段应该被遮蔽，所以不会直接显示文本
    });
  });
}
