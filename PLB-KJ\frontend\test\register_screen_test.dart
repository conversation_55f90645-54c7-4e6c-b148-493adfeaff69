import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import '../lib/features/authentication/screens/register_screen.dart';
import '../lib/shared/theme/app_theme.dart';

void main() {
  group('RegisterScreen Tests', () {
    testWidgets('应该显示注册界面的基本元素', (WidgetTester tester) async {
      // 构建注册界面
      await tester.pumpWidget(
        MaterialApp(
          theme: AppTheme.lightTheme,
          home: const RegisterScreen(isAdminRegister: true),
        ),
      );

      // 等待动画完成
      await tester.pumpAndSettle();

      // 验证基本元素是否存在
      expect(find.text('跨境电商管理系统'), findsOneWidget);
      expect(find.text('管理员注册'), findsOneWidget);
      expect(find.text('创建新账户'), findsOneWidget);
      expect(find.text('请填写以下信息完成注册'), findsOneWidget);
      
      // 验证输入框
      expect(find.byType(TextFormField), findsNWidgets(4)); // 用户名、邮箱、密码、确认密码
      
      // 验证注册按钮
      expect(find.text('注册'), findsOneWidget);
      
      // 验证登录链接
      expect(find.text('已有账户？'), findsOneWidget);
      expect(find.text('立即登录'), findsOneWidget);
    });

    testWidgets('应该在用户注册模式下显示正确的标题', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          theme: AppTheme.lightTheme,
          home: const RegisterScreen(isAdminRegister: false),
        ),
      );

      await tester.pumpAndSettle();

      expect(find.text('用户注册'), findsOneWidget);
      expect(find.byIcon(Icons.person_add), findsOneWidget);
    });

    testWidgets('应该验证表单输入', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          theme: AppTheme.lightTheme,
          home: const RegisterScreen(isAdminRegister: true),
        ),
      );

      await tester.pumpAndSettle();

      // 尝试在没有输入的情况下注册
      final registerButton = find.text('注册');
      await tester.tap(registerButton);
      await tester.pumpAndSettle();

      // 应该显示验证错误
      expect(find.text('请输入用户名'), findsOneWidget);
      expect(find.text('请输入邮箱地址'), findsOneWidget);
      expect(find.text('请输入密码'), findsOneWidget);
      expect(find.text('请确认密码'), findsOneWidget);
    });

    testWidgets('应该验证密码匹配', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          theme: AppTheme.lightTheme,
          home: const RegisterScreen(isAdminRegister: true),
        ),
      );

      await tester.pumpAndSettle();

      // 输入不匹配的密码
      final passwordField = find.byType(TextFormField).at(2);
      final confirmPasswordField = find.byType(TextFormField).at(3);
      
      await tester.enterText(passwordField, 'password123');
      await tester.enterText(confirmPasswordField, 'password456');

      final registerButton = find.text('注册');
      await tester.tap(registerButton);
      await tester.pumpAndSettle();

      // 应该显示密码不匹配错误
      expect(find.text('密码和确认密码不匹配'), findsOneWidget);
    });

    testWidgets('应该验证邮箱格式', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          theme: AppTheme.lightTheme,
          home: const RegisterScreen(isAdminRegister: true),
        ),
      );

      await tester.pumpAndSettle();

      // 输入无效邮箱
      final emailField = find.byType(TextFormField).at(1);
      await tester.enterText(emailField, 'invalid-email');

      final registerButton = find.text('注册');
      await tester.tap(registerButton);
      await tester.pumpAndSettle();

      // 应该显示邮箱格式错误
      expect(find.text('请输入有效的邮箱地址'), findsOneWidget);
    });

    testWidgets('应该能够切换密码可见性', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          theme: AppTheme.lightTheme,
          home: const RegisterScreen(isAdminRegister: true),
        ),
      );

      await tester.pumpAndSettle();

      // 查找密码可见性切换按钮
      final visibilityButtons = find.byIcon(Icons.visibility_off);
      expect(visibilityButtons, findsNWidgets(2)); // 密码和确认密码

      // 点击第一个切换按钮
      await tester.tap(visibilityButtons.first);
      await tester.pumpAndSettle();

      // 验证图标已更改
      expect(find.byIcon(Icons.visibility), findsAtLeastNWidgets(1));
    });

    testWidgets('应该能够输入注册信息', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          theme: AppTheme.lightTheme,
          home: const RegisterScreen(isAdminRegister: true),
        ),
      );

      await tester.pumpAndSettle();

      // 输入注册信息
      final usernameField = find.byType(TextFormField).at(0);
      final emailField = find.byType(TextFormField).at(1);
      final passwordField = find.byType(TextFormField).at(2);
      final confirmPasswordField = find.byType(TextFormField).at(3);

      await tester.enterText(usernameField, 'testuser');
      await tester.enterText(emailField, '<EMAIL>');
      await tester.enterText(passwordField, 'password123');
      await tester.enterText(confirmPasswordField, 'password123');

      await tester.pumpAndSettle();

      // 验证输入的文本
      expect(find.text('testuser'), findsOneWidget);
      expect(find.text('<EMAIL>'), findsOneWidget);
    });

    testWidgets('应该能够返回登录页面', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          theme: AppTheme.lightTheme,
          home: const RegisterScreen(isAdminRegister: true),
        ),
      );

      await tester.pumpAndSettle();

      // 点击登录链接
      final loginLink = find.text('立即登录');
      expect(loginLink, findsOneWidget);
      
      // 注意：在测试环境中，Navigator.pop 不会真正导航
      // 这里只是验证按钮存在且可点击
      await tester.tap(loginLink);
      await tester.pumpAndSettle();
    });
  });
}
