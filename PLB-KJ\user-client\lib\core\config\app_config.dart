class AppConfig {
  // 应用信息
  static const String userAppName = '跨境电商管理系统 - 用户端';
  static const String appVersion = '1.0.0';
  
  // API配置
  static const String apiBaseUrl = 'http://127.0.0.1:8000/api';
  static const int apiTimeout = 30000; // 30秒
  
  // 用户认证端点
  static const String userLoginEndpoint = '/user/login';
  static const String userRegisterEndpoint = '/user/register';
  static const String userLogoutEndpoint = '/user/logout';
  static const String userProfileEndpoint = '/user/profile';
  static const String forgotPasswordEndpoint = '/user/forgot-password';
  static const String resetPasswordEndpoint = '/user/reset-password';
  static const String verifyEmailEndpoint = '/user/verify-email';
  
  // 用户功能端点
  static const String ordersEndpoint = '/user/orders';
  static const String addressesEndpoint = '/user/addresses';
  static const String favoritesEndpoint = '/user/favorites';
  static const String reviewsEndpoint = '/user/reviews';
  static const String walletEndpoint = '/user/wallet';
  
  // 本地存储键
  static const String tokenKey = 'user_auth_token';
  static const String userDataKey = 'user_data';
  static const String settingsKey = 'user_settings';
  
  // 界面配置
  static const double sidebarWidth = 280.0;
  static const double topBarHeight = 70.0;
  
  // 分页配置
  static const int defaultPageSize = 20;
  static const int maxPageSize = 100;
  
  // 密码规则
  static const int minPasswordLength = 6;
  static const int maxPasswordLength = 20;
}
