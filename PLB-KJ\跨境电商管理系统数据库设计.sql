-- 跨境电商管理系统数据库设计

-- 1. 用户表
CREATE TABLE plb_kj_users (
  id INT PRIMARY KEY AUTO_INCREMENT,
  username VARCHAR(50) NOT NULL UNIQUE,
  email VARCHAR(100) NOT NULL UNIQUE,
  password_hash VARCHAR(255) NOT NULL,
  first_name VA<PERSON><PERSON><PERSON>(50),
  last_name <PERSON><PERSON><PERSON><PERSON>(50),
  phone VARCHAR(20),
  avatar VARCHAR(255),
  role ENU<PERSON>('admin', 'manager', 'staff') DEFAULT 'staff',
  status TINYINT DEFAULT 1,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- 2. 客户表
CREATE TABLE plb_kj_customers (
  id INT PRIMARY KEY AUTO_INCREMENT,
  first_name <PERSON><PERSON><PERSON><PERSON>(50) NOT NULL,
  last_name VA<PERSON><PERSON><PERSON>(50) NOT NULL,
  email VARCHAR(100) NOT NULL UNIQUE,
  phone VARCHAR(20),
  country VARCHAR(50),
  city VARCHAR(50),
  address TEXT,
  postal_code VARCHAR(20),
  status TINYINT DEFAULT 1,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- 3. 商品分类表
CREATE TABLE plb_kj_categories (
  id INT PRIMARY KEY AUTO_INCREMENT,
  name VARCHAR(100) NOT NULL,
  description TEXT,
  parent_id INT DEFAULT 0,
  sort_order INT DEFAULT 0,
  status TINYINT DEFAULT 1,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- 4. 商品表
CREATE TABLE plb_kj_products (
  id INT PRIMARY KEY AUTO_INCREMENT,
  name VARCHAR(200) NOT NULL,
  description TEXT,
  short_description TEXT,
  sku VARCHAR(100) NOT NULL UNIQUE,
  category_id INT,
  price DECIMAL(10, 2) NOT NULL,
  cost DECIMAL(10, 2) DEFAULT 0,
  weight DECIMAL(8, 2),
  dimensions VARCHAR(50),
  status TINYINT DEFAULT 1,
  is_featured TINYINT DEFAULT 0,
  meta_title VARCHAR(200),
  meta_description TEXT,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  FOREIGN KEY (category_id) REFERENCES plb_kj_categories(id)
);

-- 5. 商品图片表
CREATE TABLE plb_kj_product_images (
  id INT PRIMARY KEY AUTO_INCREMENT,
  product_id INT NOT NULL,
  image_url VARCHAR(255) NOT NULL,
  alt_text VARCHAR(200),
  sort_order INT DEFAULT 0,
  is_primary TINYINT DEFAULT 0,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (product_id) REFERENCES plb_kj_products(id) ON DELETE CASCADE
);

-- 6. 商品库存表
CREATE TABLE plb_kj_product_inventory (
  id INT PRIMARY KEY AUTO_INCREMENT,
  product_id INT NOT NULL,
  quantity INT NOT NULL DEFAULT 0,
  reserved_quantity INT NOT NULL DEFAULT 0,
  min_stock_level INT DEFAULT 0,
  max_stock_level INT DEFAULT 0,
  location VARCHAR(100),
  last_updated TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  FOREIGN KEY (product_id) REFERENCES plb_kj_products(id) ON DELETE CASCADE
);

-- 7. 仓库表
CREATE TABLE plb_kj_warehouses (
  id INT PRIMARY KEY AUTO_INCREMENT,
  name VARCHAR(100) NOT NULL,
  code VARCHAR(20) NOT NULL UNIQUE,
  address TEXT,
  city VARCHAR(50),
  country VARCHAR(50),
  contact_person VARCHAR(100),
  phone VARCHAR(20),
  email VARCHAR(100),
  is_active TINYINT DEFAULT 1,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- 8. 仓库库存表
CREATE TABLE plb_kj_warehouse_inventory (
  id INT PRIMARY KEY AUTO_INCREMENT,
  warehouse_id INT NOT NULL,
  product_id INT NOT NULL,
  quantity INT NOT NULL DEFAULT 0,
  reserved_quantity INT NOT NULL DEFAULT 0,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  FOREIGN KEY (warehouse_id) REFERENCES plb_kj_warehouses(id),
  FOREIGN KEY (product_id) REFERENCES plb_kj_products(id),
  UNIQUE KEY unique_warehouse_product (warehouse_id, product_id)
);

-- 9. 货币表
CREATE TABLE plb_kj_currencies (
  id INT PRIMARY KEY AUTO_INCREMENT,
  code VARCHAR(3) NOT NULL UNIQUE,
  name VARCHAR(50) NOT NULL,
  symbol VARCHAR(10) NOT NULL,
  rate DECIMAL(10, 6) NOT NULL,
  is_default TINYINT DEFAULT 0,
  is_active TINYINT DEFAULT 1,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- 10. 订单表
CREATE TABLE plb_kj_orders (
  id INT PRIMARY KEY AUTO_INCREMENT,
  order_number VARCHAR(50) NOT NULL UNIQUE,
  customer_id INT NOT NULL,
  currency_id INT NOT NULL,
  subtotal DECIMAL(10, 2) NOT NULL,
  tax_amount DECIMAL(10, 2) DEFAULT 0,
  shipping_amount DECIMAL(10, 2) DEFAULT 0,
  discount_amount DECIMAL(10, 2) DEFAULT 0,
  total_amount DECIMAL(10, 2) NOT NULL,
  status ENUM('pending', 'confirmed', 'processing', 'shipped', 'delivered', 'cancelled', 'refunded') DEFAULT 'pending',
  payment_status ENUM('pending', 'paid', 'failed', 'refunded') DEFAULT 'pending',
  shipping_address TEXT,
  billing_address TEXT,
  notes TEXT,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  FOREIGN KEY (customer_id) REFERENCES plb_kj_customers(id),
  FOREIGN KEY (currency_id) REFERENCES plb_kj_currencies(id)
);

-- 11. 订单商品表
CREATE TABLE plb_kj_order_items (
  id INT PRIMARY KEY AUTO_INCREMENT,
  order_id INT NOT NULL,
  product_id INT NOT NULL,
  quantity INT NOT NULL,
  unit_price DECIMAL(10, 2) NOT NULL,
  total_price DECIMAL(10, 2) NOT NULL,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (order_id) REFERENCES plb_kj_orders(id) ON DELETE CASCADE,
  FOREIGN KEY (product_id) REFERENCES plb_kj_products(id)
);

-- 12. 物流公司表
CREATE TABLE plb_kj_shipping_carriers (
  id INT PRIMARY KEY AUTO_INCREMENT,
  name VARCHAR(100) NOT NULL,
  code VARCHAR(20) NOT NULL UNIQUE,
  tracking_url VARCHAR(255),
  is_active TINYINT DEFAULT 1,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- 13. 运费规则表
CREATE TABLE plb_kj_shipping_rates (
  id INT PRIMARY KEY AUTO_INCREMENT,
  carrier_id INT NOT NULL,
  name VARCHAR(100) NOT NULL,
  min_weight DECIMAL(8, 2) DEFAULT 0,
  max_weight DECIMAL(8, 2) DEFAULT 0,
  min_amount DECIMAL(10, 2) DEFAULT 0,
  max_amount DECIMAL(10, 2) DEFAULT 0,
  rate DECIMAL(10, 2) NOT NULL,
  country VARCHAR(50),
  region VARCHAR(100),
  is_active TINYINT DEFAULT 1,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  FOREIGN KEY (carrier_id) REFERENCES plb_kj_shipping_carriers(id)
);

-- 14. 订单物流表
CREATE TABLE plb_kj_order_shipping (
  id INT PRIMARY KEY AUTO_INCREMENT,
  order_id INT NOT NULL,
  carrier_id INT NOT NULL,
  tracking_number VARCHAR(100),
  shipping_date TIMESTAMP,
  estimated_delivery_date TIMESTAMP,
  actual_delivery_date TIMESTAMP,
  shipping_cost DECIMAL(10, 2) DEFAULT 0,
  status ENUM('pending', 'shipped', 'in_transit', 'delivered', 'returned') DEFAULT 'pending',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  FOREIGN KEY (order_id) REFERENCES plb_kj_orders(id),
  FOREIGN KEY (carrier_id) REFERENCES plb_kj_shipping_carriers(id)
);

-- 15. 支付方式表
CREATE TABLE plb_kj_payment_methods (
  id INT PRIMARY KEY AUTO_INCREMENT,
  name VARCHAR(100) NOT NULL,
  code VARCHAR(20) NOT NULL UNIQUE,
  description TEXT,
  is_active TINYINT DEFAULT 1,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- 16. 订单支付表
CREATE TABLE plb_kj_order_payments (
  id INT PRIMARY KEY AUTO_INCREMENT,
  order_id INT NOT NULL,
  payment_method_id INT NOT NULL,
  amount DECIMAL(10, 2) NOT NULL,
  transaction_id VARCHAR(255),
  payment_status ENUM('pending', 'completed', 'failed', 'refunded') DEFAULT 'pending',
  payment_date TIMESTAMP,
  notes TEXT,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  FOREIGN KEY (order_id) REFERENCES plb_kj_orders(id),
  FOREIGN KEY (payment_method_id) REFERENCES plb_kj_payment_methods(id)
);

-- 17. 购物车表
CREATE TABLE plb_kj_shopping_cart (
  id INT PRIMARY KEY AUTO_INCREMENT,
  customer_id INT NOT NULL,
  product_id INT NOT NULL,
  quantity INT NOT NULL,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  FOREIGN KEY (customer_id) REFERENCES plb_kj_customers(id) ON DELETE CASCADE,
  FOREIGN KEY (product_id) REFERENCES plb_kj_products(id) ON DELETE CASCADE,
  UNIQUE KEY unique_customer_product (customer_id, product_id)
);

-- 18. 优惠券表
CREATE TABLE plb_kj_coupons (
  id INT PRIMARY KEY AUTO_INCREMENT,
  code VARCHAR(50) NOT NULL UNIQUE,
  name VARCHAR(100) NOT NULL,
  description TEXT,
  discount_type ENUM('percentage', 'fixed_amount') NOT NULL,
  discount_value DECIMAL(10, 2) NOT NULL,
  min_order_amount DECIMAL(10, 2) DEFAULT 0,
  usage_limit INT DEFAULT 0,
  used_count INT DEFAULT 0,
  valid_from TIMESTAMP,
  valid_to TIMESTAMP,
  is_active TINYINT DEFAULT 1,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- 19. 退货表
CREATE TABLE plb_kj_returns (
  id INT PRIMARY KEY AUTO_INCREMENT,
  order_id INT NOT NULL,
  customer_id INT NOT NULL,
  reason TEXT,
  status ENUM('requested', 'approved', 'rejected', 'processing', 'completed') DEFAULT 'requested',
  refund_amount DECIMAL(10, 2),
  notes TEXT,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  FOREIGN KEY (order_id) REFERENCES plb_kj_orders(id),
  FOREIGN KEY (customer_id) REFERENCES plb_kj_customers(id)
);

-- 20. 系统设置表
CREATE TABLE plb_kj_system_settings (
  id INT PRIMARY KEY AUTO_INCREMENT,
  setting_key VARCHAR(100) NOT NULL UNIQUE,
  setting_value TEXT,
  description VARCHAR(255),
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- 插入默认货币
INSERT INTO plb_kj_currencies (code, name, symbol, rate, is_default, is_active) VALUES 
('USD', 'US Dollar', '$', 1.000000, 1, 1),
('EUR', 'Euro', '€', 0.850000, 0, 1),
('GBP', 'British Pound', '£', 0.750000, 0, 1);

-- 插入默认支付方式
INSERT INTO plb_kj_payment_methods (name, code, description, is_active) VALUES 
('Credit Card', 'credit_card', 'Credit card payment', 1),
('PayPal', 'paypal', 'PayPal payment', 1),
('Bank Transfer', 'bank_transfer', 'Bank transfer payment', 1);

-- 插入默认物流公司
INSERT INTO plb_kj_shipping_carriers (name, code, tracking_url, is_active) VALUES 
('DHL', 'dhl', 'https://www.dhl.com/en/express/tracking.html?AWB={tracking_number}', 1),
('FedEx', 'fedex', 'https://www.fedex.com/apps/fedextrack/?tracknumbers={tracking_number}', 1),
('UPS', 'ups', 'https://www.ups.com/track?loc=en_US&tracknum={tracking_number}', 1);

-- 插入默认系统设置
INSERT INTO plb_kj_system_settings (setting_key, setting_value, description) VALUES 
('site_name', '跨境电商管理系统', '系统名称'),
('site_email', '<EMAIL>', '系统邮箱'),
('currency_default', 'USD', '默认货币'),
('tax_rate', '0.00', '默认税率');