<?php
/**
 * 缓存配置文件
 * 支持Redis和文件缓存
 */

return [
    // 默认缓存驱动（auto会自动选择最佳方案）
    'default' => 'auto',
    
    // 缓存配置
    'stores' => [
        // Redis缓存配置
        'redis' => [
            'host' => '127.0.0.1',
            'port' => 6379,
            'database' => 0,
            'password' => '', // 如果Redis设置了密码
            'prefix' => 'plb_cache:',
            'timeout' => 3, // 连接超时时间（秒）
        ],
        
        // 文件缓存配置
        'file' => [
            'path' => dirname(__DIR__) . '/cache',
        ],
        
        // APCu缓存配置（可选）
        'apcu' => [
            'prefix' => 'plb_cache:',
        ]
    ],
    
    // 缓存TTL配置（秒）
    'ttl' => [
        'default' => 3600,      // 1小时
        'short' => 300,         // 5分钟
        'medium' => 1800,       // 30分钟
        'long' => 86400,        // 24小时
        'very_long' => 604800,  // 7天
    ],
    
    // 开发环境配置
    'development' => [
        'enabled' => true,
        'debug' => true,
        'auto_cleanup' => true, // 自动清理过期缓存
    ],
    
    // 生产环境配置
    'production' => [
        'enabled' => true,
        'debug' => false,
        'auto_cleanup' => false,
    ]
];
