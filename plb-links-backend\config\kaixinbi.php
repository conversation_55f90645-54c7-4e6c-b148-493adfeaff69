<?php
/**
 * 开心币支付配置文件
 */

return [
    // 基础配置
    'enabled' => true,
    'name' => '开心币',
    'description' => '站内虚拟货币支付系统',
    
    // 汇率配置
    'exchange_rate' => [
        'cny_to_kxb' => 1.0,  // 1元人民币 = 1开心币
        'kxb_to_cny' => 1.0,  // 1开心币 = 1元人民币
    ],
    
    // 支付限制
    'limits' => [
        'min_amount' => 1,      // 最小支付金额（开心币）
        'max_amount' => 10000,  // 最大支付金额（开心币）
        'daily_limit' => 50000, // 每日支付限额（开心币）
    ],
    
    // 手续费配置
    'fees' => [
        'rate' => 0.0,         // 手续费率（0%）
        'min_fee' => 0,        // 最小手续费
        'max_fee' => 0,        // 最大手续费
    ],
    
    // API配置
    'api' => [
        'version' => 'v1.0',
        'timeout' => 30,       // 超时时间（秒）
        'retry_times' => 3,    // 重试次数
    ],
    
    // 安全配置
    'security' => [
        'encrypt_key' => 'kaixinbi_encrypt_key_2024',
        'sign_key' => 'kaixinbi_sign_key_2024',
        'token_expire' => 3600, // Token过期时间（秒）
    ],
    
    // 回调配置
    'callback' => [
        'url' => 'https://api.51kxg.com/payments/kaixinbi/callback',
        'return_url' => 'https://api.51kxg.com/payments/kaixinbi/return',
        'notify_url' => 'https://api.51kxg.com/payments/kaixinbi/notify',
    ],
    
    // 日志配置
    'log' => [
        'enabled' => true,
        'level' => 'info',     // debug, info, warning, error
        'file' => 'logs/kaixinbi.log',
    ],
    
    // 开心币获取方式配置
    'earn_methods' => [
        'daily_signin' => [
            'enabled' => true,
            'amount' => 10,    // 每日签到获得开心币
        ],
        'invite_user' => [
            'enabled' => true,
            'amount' => 100,   // 邀请用户获得开心币
        ],
        'complete_task' => [
            'enabled' => true,
            'amount' => 50,    // 完成任务获得开心币
        ],
        'purchase' => [
            'enabled' => true,
            'rate' => 1.0,     // 充值汇率
        ],
    ],
    
    // 开心币消费场景
    'spend_scenarios' => [
        'vip_membership' => [
            'enabled' => true,
            'description' => 'VIP会员购买',
        ],
        'premium_content' => [
            'enabled' => true,
            'description' => '付费内容购买',
        ],
        'virtual_gifts' => [
            'enabled' => true,
            'description' => '虚拟礼品购买',
        ],
        'service_upgrade' => [
            'enabled' => true,
            'description' => '服务升级',
        ],
    ],
    
    // 状态码定义
    'status_codes' => [
        'success' => 200,
        'insufficient_balance' => 1001,
        'account_frozen' => 1002,
        'invalid_amount' => 1003,
        'system_maintenance' => 1004,
        'user_not_found' => 1005,
    ],
    
    // 错误消息
    'error_messages' => [
        1001 => '开心币余额不足',
        1002 => '账户已被冻结',
        1003 => '支付金额无效',
        1004 => '系统维护中，请稍后再试',
        1005 => '用户不存在',
    ],
];
?>
