<?php
/**
 * 演示插件主文件
 * 
 * @package     PLB-Links Demo Plugin
 * <AUTHOR> Team
 * @copyright   Copyright (c) 2023-2024
 */

// 防止直接访问
if (!defined('PLB_LINKS')) {
    die('禁止直接访问插件文件');
}

/**
 * 插件初始化函数
 */
function plb_demo_plugin_init() {
    // 获取插件管理器实例
    $pluginManager = \App\Helpers\PluginManager::getInstance();
    
    // 注册钩子
    $pluginManager->addHook('admin_header', 'plb_demo_plugin_admin_header');
    $pluginManager->addHook('admin_footer', 'plb_demo_plugin_admin_footer');
    $pluginManager->addHook('content_filter', 'plb_demo_plugin_content_filter');
}

/**
 * 管理员页面头部钩子
 */
function plb_demo_plugin_admin_header() {
    // 获取插件配置
    $plugin = new \App\Models\Plugin();
    $demoPlugin = $plugin->getPluginBySlug('demo-plugin');
    
    if ($demoPlugin) {
        $options = json_decode($demoPlugin['options'], true);
        
        // 如果启用了功能1，添加自定义CSS
        if (isset($options['enable_feature_1']) && $options['enable_feature_1']) {
            echo '<style>' . htmlspecialchars($options['custom_css'] ?? '') . '</style>';
        }
    }
}

/**
 * 管理员页面底部钩子
 */
function plb_demo_plugin_admin_footer() {
    echo '<div class="text-center mt-3 demo-plugin">
        <small>由演示插件提供支持</small>
    </div>';
}

/**
 * 内容过滤钩子
 */
function plb_demo_plugin_content_filter($content) {
    // 获取插件配置
    $plugin = new \App\Models\Plugin();
    $demoPlugin = $plugin->getPluginBySlug('demo-plugin');
    
    if ($demoPlugin) {
        $options = json_decode($demoPlugin['options'], true);
        
        // 如果启用了功能1，添加功能1文本
        if (isset($options['enable_feature_1']) && $options['enable_feature_1']) {
            $content .= '<div class="demo-plugin-feature">' . htmlspecialchars($options['feature_1_text'] ?? '') . '</div>';
        }
    }
    
    return $content;
} 