# 数理化工具箱插件

此插件为PLB-Links系统提供了数学公式渲染、物理单位转换和化学元素周期表等功能。

## 功能特点

- **数学公式渲染**：使用MathJax库渲染LaTeX数学公式
- **物理单位转换**：支持长度、质量、温度等各种单位之间的转换
- **物理公式计算**：内置常用物理公式计算器
- **化学元素信息**：提供元素周期表和详细的元素信息
- **化学方程式渲染**：支持化学方程式的美观显示

## 安装说明

1. 将插件文件夹上传到 `plugins` 目录
2. 在管理后台的"插件管理"页面启用该插件
3. 在插件设置页面配置相关选项

## 使用方法

### 数学公式

在内容中使用MathJax语法插入数学公式：

- 行内公式：`$E=mc^2$`
- 行间公式：`$$\int_{a}^{b} f(x) \, dx = F(b) - F(a)$$`

### 物理工具

使用短代码插入物理工具：

- 单位转换：`[unit_convert value="100" from="cm" to="m"]`
- 物理公式：`[physics_formula name="velocity" s="100" t="10"/]`

支持的物理公式：
- `velocity`：速度计算公式
- `force`：力计算公式
- `energy`：能量计算公式

### 化学工具

使用短代码插入化学工具：

- 元素信息：`[element symbol="H"]`
- 化学方程式：`[chemical_equation]2H_2 + O_2 \rightarrow 2H_2O[/chemical_equation]`

## 插件配置

在插件设置页面，您可以配置以下选项：

- 启用/禁用数学公式渲染
- 启用/禁用物理工具
- 启用/禁用化学工具
- 选择公式显示主题
- 自定义MathJax CDN地址

## 开发者信息

- 插件名称：数理化工具箱
- 版本：1.0.0
- 作者：PLB-Links Team
- 插件主页：https://www.51kxg.com/plugins/math-science

## 系统要求

- PLB-Links 1.0.0 或更高版本
- PHP 7.2 或更高版本
- 现代浏览器（支持ES6）

## 更新历史

### 1.0.0 (2024-07-03)
- 初始版本发布 