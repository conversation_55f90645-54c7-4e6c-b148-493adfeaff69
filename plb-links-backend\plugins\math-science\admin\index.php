<?php
/**
 * 数理化工具箱插件管理页面
 */

// 防止直接访问
if (!defined('PLB_LINKS')) {
    exit('不允许直接访问');
}

// 获取插件管理器和插件信息
$pluginManager = \App\Helpers\PluginManager::getInstance();
$plugin = null;

// 获取所有插件
$allPlugins = $pluginManager->getLoadedPlugins();
foreach ($allPlugins as $p) {
    if ($p['slug'] === 'math-science') {
        $plugin = $p;
        break;
    }
}

if (!$plugin) {
    echo '<div class="alert alert-danger">插件信息不可用</div>';
    return;
}

// 解析选项
$options = $plugin['options'] ? json_decode($plugin['options'], true) : [];

// 处理表单提交
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['save_settings'])) {
    $newOptions = [
        'enable_math_formula' => isset($_POST['enable_math_formula']),
        'enable_physics_tools' => isset($_POST['enable_physics_tools']),
        'enable_chemistry_tools' => isset($_POST['enable_chemistry_tools']),
        'formula_theme' => $_POST['formula_theme'] ?? 'default',
        'mathjax_cdn' => $_POST['mathjax_cdn'] ?? 'https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js',
        'lanthanide_actinide_layout' => $_POST['lanthanide_actinide_layout'] ?? 'horizontal'
    ];
    
    // 更新插件选项
    $pluginModel = new \App\Models\Plugin();
    $result = $pluginModel->updatePlugin($plugin['id'], ['options' => $newOptions]);
    
    if ($result) {
        echo '<div class="alert alert-success">设置已保存</div>';
        $options = $newOptions;
    } else {
        echo '<div class="alert alert-danger">保存设置失败</div>';
    }
}

// 主题选项
$themes = [
    'default' => '默认主题',
    'dark' => '深色主题',
    'light' => '浅色主题',
    'blue' => '蓝色主题'
];

?>

<div class="container-fluid">
    <h1 class="h3 mb-4 text-gray-800">数理化工具箱</h1>
    
    <div class="row">
        <div class="col-lg-8">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">插件设置</h6>
                </div>
                <div class="card-body">
                    <form method="post" action="">
                        <div class="mb-3">
                            <label class="form-label">启用数学公式渲染</label>
                            <div class="form-check form-switch">
                                <input class="form-check-input" type="checkbox" id="enable_math_formula" name="enable_math_formula" <?php echo isset($options['enable_math_formula']) && $options['enable_math_formula'] ? 'checked' : ''; ?>>
                                <label class="form-check-label" for="enable_math_formula">在页面中启用MathJax渲染数学公式</label>
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <label class="form-label">启用物理工具</label>
                            <div class="form-check form-switch">
                                <input class="form-check-input" type="checkbox" id="enable_physics_tools" name="enable_physics_tools" <?php echo isset($options['enable_physics_tools']) && $options['enable_physics_tools'] ? 'checked' : ''; ?>>
                                <label class="form-check-label" for="enable_physics_tools">启用单位转换和物理公式计算工具</label>
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <label class="form-label">启用化学工具</label>
                            <div class="form-check form-switch">
                                <input class="form-check-input" type="checkbox" id="enable_chemistry_tools" name="enable_chemistry_tools" <?php echo isset($options['enable_chemistry_tools']) && $options['enable_chemistry_tools'] ? 'checked' : ''; ?>>
                                <label class="form-check-label" for="enable_chemistry_tools">启用元素周期表和化学方程式工具</label>
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="formula_theme" class="form-label">公式主题</label>
                            <select class="form-select" id="formula_theme" name="formula_theme">
                                <?php foreach ($themes as $key => $name): ?>
                                    <option value="<?php echo $key; ?>" <?php echo (isset($options['formula_theme']) && $options['formula_theme'] === $key) ? 'selected' : ''; ?>><?php echo $name; ?></option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                        
                        <div class="mb-3">
                            <label for="mathjax_cdn" class="form-label">MathJax CDN 链接</label>
                            <input type="text" class="form-control" id="mathjax_cdn" name="mathjax_cdn" value="<?php echo $options['mathjax_cdn'] ?? 'https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js'; ?>">
                            <div class="form-text">自定义MathJax CDN地址，默认使用jsDelivr</div>
                        </div>

                        <div class="mb-3">
                            <label for="lanthanide_actinide_layout" class="form-label">镧系和锕系元素布局</label>
                            <select class="form-select" id="lanthanide_actinide_layout" name="lanthanide_actinide_layout">
                                <option value="horizontal" <?php echo (isset($options['lanthanide_actinide_layout']) && $options['lanthanide_actinide_layout'] === 'horizontal') ? 'selected' : ''; ?>>横向排列</option>
                                <option value="vertical" <?php echo (isset($options['lanthanide_actinide_layout']) && $options['lanthanide_actinide_layout'] === 'vertical') ? 'selected' : ''; ?>>纵向排列</option>
                            </select>
                            <div class="form-text">选择镧系和锕系元素在周期表中的排列方式</div>
                        </div>

                        <button type="submit" name="save_settings" class="btn btn-primary">保存设置</button>
                    </form>
                </div>
            </div>
            
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">使用说明</h6>
                </div>
                <div class="card-body">
                    <h5>数学公式</h5>
                    <p>使用MathJax语法在内容中插入数学公式，支持以下格式：</p>
                    <ul>
                        <li>行内公式：<code>$E=mc^2$</code></li>
                        <li>行间公式：<code>$$E=mc^2$$</code></li>
                    </ul>
                    
                    <h5>物理工具</h5>
                    <p>在内容中使用短代码插入物理工具：</p>
                    <ul>
                        <li>单位转换：<code>[unit_convert value="100" from="cm" to="m"]</code></li>
                        <li>物理公式：<code>[physics_formula name="velocity" s="100" t="10"/]</code></li>
                    </ul>
                    
                    <h5>化学工具</h5>
                    <p>在内容中使用短代码插入化学工具：</p>
                    <ul>
                        <li>元素信息：<code>[element symbol="H"]</code></li>
                        <li>化学方程式：<code>[chemical_equation]2H_2 + O_2 \rightarrow 2H_2O[/chemical_equation]</code></li>
                    </ul>
                </div>
            </div>
        </div>
        
        <div class="col-lg-4">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">示例</h6>
                </div>
                <div class="card-body">
                    <h5>数学公式示例</h5>
                    <div class="mathjax">
                        $E=mc^2$
                    </div>
                    <div class="mathjax">
                        $$\int_{a}^{b} f(x) \, dx = F(b) - F(a)$$
                    </div>
                    
                    <h5>物理公式示例</h5>
                    <div class="physics-formula">
                        <div class="formula-name">velocity</div>
                        <div class="formula-description">速度 = 位移 / 时间</div>
                        <div class="formula-tex mathjax">v = \frac{s}{t}</div>
                    </div>
                    
                    <h5>化学元素示例</h5>
                    <div class="chemical-element">
                        <div class="element-symbol">H</div>
                        <div class="element-name">氢</div>
                        <div class="element-atomic-number">原子序数: 1</div>
                        <div class="element-atomic-weight">原子质量: 1.008</div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div> 