<?php
/**
 * 数理化插件API路由
 */

if (!defined('PLB_LINKS')) {
    exit('Access Denied');
}

/**
 * 注册API路由
 * 
 * 使用系统的钩子机制注册API路由
 */
function math_science_register_api_routes($router) {
    // 单位转换API
    $router->get('/math-science/convert', function() {
        $value = isset($_GET['value']) ? floatval($_GET['value']) : 0;
        $from = $_GET['from'] ?? '';
        $to = $_GET['to'] ?? '';
        
        if (empty($from) || empty($to)) {
            return json_response([
                'success' => false,
                'message' => '缺少参数: 需要value, from和to参数'
            ], 400);
        }
        
        try {
            $result = plb_math_science_convert_unit($value, $from, $to);
            return json_response([
                'success' => true,
                'data' => [
                    'original' => [
                        'value' => $value,
                        'unit' => $from
                    ],
                    'converted' => [
                        'value' => $result,
                        'unit' => $to
                    ]
                ]
            ]);
        } catch (Exception $e) {
            return json_response([
                'success' => false,
                'message' => $e->getMessage()
            ], 500);
        }
    });
    
    // 物理公式API
    $router->get('/math-science/formula/{name}', function($name) {
        $params = [];
        foreach ($_GET as $key => $value) {
            if ($key !== 'name') {
                $params[$key] = floatval($value);
            }
        }
        
        try {
            $result = plb_math_science_get_physics_formula($name, $params);
            return json_response([
                'success' => true,
                'data' => [
                    'name' => $name,
                    'params' => $params,
                    'result' => $result
                ]
            ]);
        } catch (Exception $e) {
            return json_response([
                'success' => false,
                'message' => $e->getMessage()
            ], 500);
        }
    });
    
    // 化学元素API
    $router->get('/math-science/element/{symbol}', function($symbol) {
        try {
            $info = plb_math_science_get_element_info($symbol);
            return json_response([
                'success' => true,
                'data' => $info
            ]);
        } catch (Exception $e) {
            return json_response([
                'success' => false,
                'message' => $e->getMessage()
            ], 500);
        }
    });
    
    // 周期表API
    $router->get('/math-science/periodic-table', function() {
        try {
            // 这个函数需要在main.php中实现
            $table = plb_math_science_get_periodic_table();
            return json_response([
                'success' => true,
                'data' => $table
            ]);
        } catch (Exception $e) {
            return json_response([
                'success' => false,
                'message' => $e->getMessage()
            ], 500);
        }
    });
}

// 添加钩子，加载API路由
// 使用系统的钩子机制
$pluginManager = \App\Helpers\PluginManager::getInstance();
$pluginManager->addHook('register_api_routes', 'math_science_register_api_routes'); 