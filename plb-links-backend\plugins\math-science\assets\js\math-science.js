/**
 * 数理化工具箱插件前端脚本
 */
(function() {
    // 等待DOM加载完成
    document.addEventListener('DOMContentLoaded', function() {
        // 初始化插件
        initMathScience();
    });
    
    // 初始化函数
    function initMathScience() {
        // 应用主题
        applyFormulaTheme();
        
        // 初始化单位转换器
        initUnitConverter();
        
        // 初始化周期表
        initPeriodicTable();
    }
    
    // 应用公式主题
    function applyFormulaTheme() {
        // 获取主题设置
        var theme = document.body.getAttribute('data-formula-theme');
        
        if (!theme) {
            // 如果没有设置主题，则查找meta标签
            var themeMeta = document.querySelector('meta[name="formula-theme"]');
            if (themeMeta) {
                theme = themeMeta.getAttribute('content');
                document.body.setAttribute('data-formula-theme', theme);
            }
        }
    }
    
    // 初始化单位转换器
    function initUnitConverter() {
        // 查找所有单位转换器元素
        var converters = document.querySelectorAll('.unit-converter');
        
        converters.forEach(function(converter) {
            var valueInput = converter.querySelector('.unit-value');
            var fromSelect = converter.querySelector('.unit-from');
            var toSelect = converter.querySelector('.unit-to');
            var resultOutput = converter.querySelector('.unit-result');
            var convertBtn = converter.querySelector('.unit-convert-btn');
            
            if (convertBtn && valueInput && fromSelect && toSelect && resultOutput) {
                convertBtn.addEventListener('click', function() {
                    var value = parseFloat(valueInput.value);
                    var from = fromSelect.value;
                    var to = toSelect.value;
                    
                    if (isNaN(value)) {
                        resultOutput.textContent = '请输入有效数值';
                        return;
                    }
                    
                    // 发送Ajax请求进行单位转换
                    fetch('/ajax/convert-unit?value=' + value + '&from=' + from + '&to=' + to)
                        .then(function(response) {
                            return response.json();
                        })
                        .then(function(data) {
                            if (data.success) {
                                resultOutput.textContent = data.result + ' ' + to;
                            } else {
                                resultOutput.textContent = data.message || '转换失败';
                            }
                        })
                        .catch(function(error) {
                            resultOutput.textContent = '请求错误: ' + error.message;
                            console.error('单位转换错误:', error);
                        });
                });
            }
        });
    }
    
    // 初始化周期表
    function initPeriodicTable() {
        var periodicTable = document.getElementById('periodic-table');
        
        if (periodicTable) {
            // 绑定元素点击事件
            var elements = periodicTable.querySelectorAll('.element');
            
            elements.forEach(function(element) {
                element.addEventListener('click', function() {
                    var symbol = this.getAttribute('data-symbol');
                    
                    if (symbol) {
                        showElementDetails(symbol);
                    }
                });
            });
        }
    }
    
    // 显示元素详细信息
    function showElementDetails(symbol) {
        var modal = document.getElementById('element-modal');
        
        if (!modal) {
            // 创建模态框
            modal = document.createElement('div');
            modal.id = 'element-modal';
            modal.className = 'modal fade';
            modal.setAttribute('tabindex', '-1');
            modal.setAttribute('aria-hidden', 'true');
            
            modal.innerHTML = `
                <div class="modal-dialog">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">元素详情</h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                        </div>
                        <div class="modal-body" id="element-details">
                            <div class="text-center">
                                <div class="spinner-border" role="status">
                                    <span class="visually-hidden">加载中...</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            `;
            
            document.body.appendChild(modal);
        }
        
        // 显示模态框
        var bsModal = new bootstrap.Modal(modal);
        bsModal.show();
        
        // 加载元素详情
        var detailsContainer = document.getElementById('element-details');
        detailsContainer.innerHTML = '<div class="text-center"><div class="spinner-border" role="status"><span class="visually-hidden">加载中...</span></div></div>';
        
        // 发送Ajax请求获取元素详情
        fetch('/ajax/element-details?symbol=' + symbol)
            .then(function(response) {
                return response.json();
            })
            .then(function(data) {
                if (data.success) {
                    // 更新模态框内容
                    detailsContainer.innerHTML = `
                        <div class="row">
                            <div class="col-md-4">
                                <div class="chemical-element">
                                    <div class="element-symbol">${data.element.symbol}</div>
                                    <div class="element-name">${data.element.name}</div>
                                    <div class="element-atomic-number">原子序数: ${data.element.atomic_number}</div>
                                    <div class="element-atomic-weight">原子质量: ${data.element.atomic_weight}</div>
                                </div>
                            </div>
                            <div class="col-md-8">
                                <h5>基本信息</h5>
                                <ul class="list-group mb-3">
                                    <li class="list-group-item"><strong>英文名称:</strong> ${data.element.english_name}</li>
                                    <li class="list-group-item"><strong>元素分类:</strong> ${data.element.category}</li>
                                    <li class="list-group-item"><strong>电子排布:</strong> ${data.element.electron_configuration}</li>
                                    <li class="list-group-item"><strong>状态:</strong> ${data.element.state}</li>
                                </ul>
                                
                                <h5>物理性质</h5>
                                <ul class="list-group">
                                    <li class="list-group-item"><strong>密度:</strong> ${data.element.density} g/cm³</li>
                                    <li class="list-group-item"><strong>熔点:</strong> ${data.element.melting_point} K</li>
                                    <li class="list-group-item"><strong>沸点:</strong> ${data.element.boiling_point} K</li>
                                </ul>
                            </div>
                        </div>
                    `;
                } else {
                    detailsContainer.innerHTML = '<div class="alert alert-danger">无法加载元素详情</div>';
                }
            })
            .catch(function(error) {
                detailsContainer.innerHTML = '<div class="alert alert-danger">加载错误: ' + error.message + '</div>';
                console.error('元素详情加载错误:', error);
            });
    }
})(); 