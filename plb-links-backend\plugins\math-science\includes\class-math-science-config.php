<?php
/**
 * 数理化插件配置管理类
 *
 * @package     PLB-Links
 * <AUTHOR> Team
 * @version     2.0.0
 */

// 防止直接访问
if (!defined('PLB_LINKS')) {
    exit('不允许直接访问');
}

class MathScienceConfig {
    
    private static $instance = null;
    private $options = null;
    private $defaultOptions = [
        'enable_math_formula' => true,
        'enable_physics_tools' => true,
        'enable_chemistry_tools' => true,
        'formula_theme' => 'default',
        'mathjax_cdn' => 'https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js',
        'lanthanide_actinide_layout' => 'horizontal',
        'periodic_table_style' => 'modern',
        'calculator_precision' => 10,
        'unit_conversion_precision' => 6
    ];
    
    /**
     * 获取单例实例
     */
    public static function getInstance() {
        if (self::$instance === null) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    /**
     * 私有构造函数
     */
    private function __construct() {
        $this->loadOptions();
    }
    
    /**
     * 加载配置选项
     */
    private function loadOptions() {
        try {
            $pluginModel = new \App\Models\Plugin();
            $pluginData = $pluginModel->getPluginBySlug('math-science');
            
            if ($pluginData && $pluginData['options']) {
                $dbOptions = json_decode($pluginData['options'], true);
                $this->options = array_merge($this->defaultOptions, $dbOptions ?: []);
            } else {
                $this->options = $this->defaultOptions;
            }
        } catch (Exception $e) {
            $this->options = $this->defaultOptions;
        }
    }
    
    /**
     * 获取所有配置选项
     */
    public function getOptions() {
        if ($this->options === null) {
            $this->loadOptions();
        }
        return $this->options;
    }
    
    /**
     * 获取单个配置选项
     */
    public function getOption($key, $default = null) {
        $options = $this->getOptions();
        return isset($options[$key]) ? $options[$key] : $default;
    }
    
    /**
     * 设置配置选项
     */
    public function setOption($key, $value) {
        $this->options[$key] = $value;
    }
    
    /**
     * 设置多个配置选项
     */
    public function setOptions($options) {
        $this->options = array_merge($this->options, $options);
    }
    
    /**
     * 保存配置到数据库
     */
    public function saveOptions() {
        try {
            $pluginModel = new \App\Models\Plugin();
            $pluginData = $pluginModel->getPluginBySlug('math-science');
            
            if ($pluginData) {
                return $pluginModel->updatePlugin($pluginData['id'], [
                    'options' => json_encode($this->options)
                ]);
            }
        } catch (Exception $e) {
            return false;
        }
        
        return false;
    }
    
    /**
     * 重置为默认配置
     */
    public function resetToDefaults() {
        $this->options = $this->defaultOptions;
    }
    
    /**
     * 获取默认配置
     */
    public function getDefaultOptions() {
        return $this->defaultOptions;
    }
    
    /**
     * 验证配置选项
     */
    public function validateOptions($options) {
        $errors = [];
        
        // 验证布尔值选项
        $booleanOptions = ['enable_math_formula', 'enable_physics_tools', 'enable_chemistry_tools'];
        foreach ($booleanOptions as $option) {
            if (isset($options[$option]) && !is_bool($options[$option])) {
                $errors[] = "选项 {$option} 必须是布尔值";
            }
        }
        
        // 验证字符串选项
        $stringOptions = ['formula_theme', 'mathjax_cdn', 'lanthanide_actinide_layout', 'periodic_table_style'];
        foreach ($stringOptions as $option) {
            if (isset($options[$option]) && !is_string($options[$option])) {
                $errors[] = "选项 {$option} 必须是字符串";
            }
        }
        
        // 验证数值选项
        $numericOptions = ['calculator_precision', 'unit_conversion_precision'];
        foreach ($numericOptions as $option) {
            if (isset($options[$option]) && (!is_numeric($options[$option]) || $options[$option] < 0)) {
                $errors[] = "选项 {$option} 必须是非负数";
            }
        }
        
        // 验证特定值选项
        if (isset($options['lanthanide_actinide_layout']) && 
            !in_array($options['lanthanide_actinide_layout'], ['horizontal', 'vertical'])) {
            $errors[] = "镧系锕系布局选项必须是 'horizontal' 或 'vertical'";
        }
        
        if (isset($options['periodic_table_style']) && 
            !in_array($options['periodic_table_style'], ['modern', 'classic', 'compact'])) {
            $errors[] = "周期表样式选项必须是 'modern', 'classic' 或 'compact'";
        }
        
        return $errors;
    }
}
