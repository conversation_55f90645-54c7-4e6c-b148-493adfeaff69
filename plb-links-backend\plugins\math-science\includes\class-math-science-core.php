<?php
/**
 * 数理化插件核心类
 *
 * @package     PLB-Links
 * <AUTHOR> Team
 * @version     2.0.0
 */

// 防止直接访问
if (!defined('PLB_LINKS')) {
    exit('不允许直接访问');
}

class MathScienceCore {
    
    private $config;
    private $modules = [];
    
    public function __construct() {
        $this->config = MathScienceConfig::getInstance();
    }
    
    /**
     * 初始化插件
     */
    public function init() {
        $this->loadModules();
        $this->registerHooks();
    }
    
    /**
     * 加载模块
     */
    private function loadModules() {
        $options = $this->config->getOptions();
        
        // 加载数学模块
        if ($options['enable_math_formula']) {
            require_once MATH_SCIENCE_PLUGIN_DIR . '/modules/math/class-math-module.php';
            $this->modules['math'] = new MathModule();
        }
        
        // 加载物理模块
        if ($options['enable_physics_tools']) {
            require_once MATH_SCIENCE_PLUGIN_DIR . '/modules/physics/class-physics-module.php';
            $this->modules['physics'] = new PhysicsModule();
        }
        
        // 加载化学模块
        if ($options['enable_chemistry_tools']) {
            require_once MATH_SCIENCE_PLUGIN_DIR . '/modules/chemistry/class-chemistry-module.php';
            $this->modules['chemistry'] = new ChemistryModule();
        }
    }
    
    /**
     * 注册钩子
     */
    private function registerHooks() {
        $pluginManager = \App\Helpers\PluginManager::getInstance();
        
        // 注册前端资源钩子
        $pluginManager->addHook('head_end', [$this, 'addHeadScripts']);
        $pluginManager->addHook('footer_end', [$this, 'addFooterScripts']);
        
        // 注册短代码钩子
        $pluginManager->addHook('shortcode', [$this, 'processShortcodes']);
        
        // 注册管理菜单钩子
        $pluginManager->addHook('admin_menu', [$this, 'addAdminMenu']);
    }
    
    /**
     * 添加头部脚本
     */
    public function addHeadScripts() {
        $options = $this->config->getOptions();
        
        if ($options['enable_math_formula']) {
            $mathjaxCdn = $options['mathjax_cdn'] ?? 'https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js';
            echo '<script id="MathJax-script" async src="' . $mathjaxCdn . '"></script>';
            echo '<script>
            window.MathJax = {
                tex: {
                    inlineMath: [["$", "$"], ["\\\\(", "\\\\)"]],
                    displayMath: [["$$", "$$"], ["\\\\[", "\\\\]"]],
                    processEscapes: true,
                    processEnvironments: true
                },
                options: {
                    ignoreHtmlClass: "no-mathjax|ignore-mathjax",
                    processHtmlClass: "mathjax|has-mathjax"
                }
            };
            </script>';
        }
    }
    
    /**
     * 添加底部脚本
     */
    public function addFooterScripts() {
        echo '<link rel="stylesheet" href="' . MATH_SCIENCE_PLUGIN_URL . '/assets/css/math-science.css">';
        echo '<script src="' . MATH_SCIENCE_PLUGIN_URL . '/assets/js/math-science.js"></script>';
    }
    
    /**
     * 处理短代码
     */
    public function processShortcodes($content) {
        foreach ($this->modules as $module) {
            if (method_exists($module, 'processShortcodes')) {
                $content = $module->processShortcodes($content);
            }
        }
        return $content;
    }
    
    /**
     * 添加管理菜单
     */
    public function addAdminMenu($menu) {
        $menu[] = [
            'title' => '数理化工具',
            'slug' => 'math-science',
            'icon' => 'bi-calculator',
            'callback' => [$this, 'renderAdminPage']
        ];
        
        return $menu;
    }
    
    /**
     * 渲染管理页面
     */
    public function renderAdminPage() {
        include MATH_SCIENCE_PLUGIN_DIR . '/admin/index.php';
    }
    
    /**
     * 获取模块
     */
    public function getModule($name) {
        return isset($this->modules[$name]) ? $this->modules[$name] : null;
    }
    
    /**
     * 获取所有模块
     */
    public function getModules() {
        return $this->modules;
    }
}
