<?php
/**
 * 数理化工具集插件主文件
 *
 * @package     PLB-Links
 * <AUTHOR> Team
 * @copyright   Copyright (c) 2023-2024
 * @version     2.0.0
 */

// 防止直接访问
if (!defined('PLB_LINKS')) {
    exit('不允许直接访问');
}

// 定义插件常量
define('MATH_SCIENCE_PLUGIN_DIR', __DIR__);
define('MATH_SCIENCE_PLUGIN_URL', '/plugins/math-science');
define('MATH_SCIENCE_VERSION', '2.0.0');

// 加载插件核心类
require_once MATH_SCIENCE_PLUGIN_DIR . '/includes/class-math-science-config.php';
require_once MATH_SCIENCE_PLUGIN_DIR . '/includes/class-math-science-core.php';

/**
 * 插件初始化函数
 */
function plb_math_science_init() {
    // 初始化插件核心
    $core = new MathScienceCore();
    $core->init();
}

/**
 * 获取插件配置
 */
function plb_math_science_get_options() {
    return MathScienceConfig::getInstance()->getOptions();
}

/**
 * 获取插件核心实例
 */
function plb_math_science_get_core() {
    static $core = null;
    if ($core === null) {
        $core = new MathScienceCore();
    }
    return $core;
}