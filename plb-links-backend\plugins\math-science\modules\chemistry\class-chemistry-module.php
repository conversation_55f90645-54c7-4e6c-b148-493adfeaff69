<?php
/**
 * 化学工具模块
 *
 * @package     PLB-Links
 * <AUTHOR> Team
 * @version     2.0.0
 */

// 防止直接访问
if (!defined('PLB_LINKS')) {
    exit('不允许直接访问');
}

class ChemistryModule {
    
    private $config;
    private $periodicTable;
    
    public function __construct() {
        $this->config = MathScienceConfig::getInstance();
        $this->initPeriodicTable();
    }
    
    /**
     * 初始化元素周期表数据
     */
    private function initPeriodicTable() {
        require_once MATH_SCIENCE_PLUGIN_DIR . '/modules/chemistry/periodic-table-data.php';
        $this->periodicTable = getPeriodicTableData();
    }
    
    /**
     * 处理化学相关短代码
     */
    public function processShortcodes($content) {
        // 处理元素信息短代码
        $content = preg_replace_callback('/\[element symbol="([^"]+)"\]/i', 
            [$this, 'renderElementInfo'], $content);
        
        // 处理化学方程式短代码
        $content = preg_replace_callback('/\[chemical_equation\](.*?)\[\/chemical_equation\]/is', 
            [$this, 'renderChemicalEquation'], $content);
        
        // 处理周期表短代码
        $content = preg_replace_callback('/\[periodic_table style="([^"]*)"?\]/i', 
            [$this, 'renderPeriodicTable'], $content);
        
        // 处理分子式短代码
        $content = preg_replace_callback('/\[molecule formula="([^"]+)"\]/i', 
            [$this, 'renderMolecule'], $content);
        
        return $content;
    }
    
    /**
     * 渲染元素信息
     */
    public function renderElementInfo($matches) {
        $symbol = strtoupper($matches[1]);
        
        if (!isset($this->periodicTable['elements'][$symbol])) {
            return '<span class="chemistry-error">未知元素: ' . $symbol . '</span>';
        }
        
        $element = $this->periodicTable['elements'][$symbol];
        
        $output = '<div class="element-info">';
        $output .= '<div class="element-card ' . $this->getCategoryClass($element['category']) . '">';
        $output .= '<div class="element-number">' . $element['atomic_number'] . '</div>';
        $output .= '<div class="element-symbol">' . $symbol . '</div>';
        $output .= '<div class="element-name">' . $element['name'] . '</div>';
        $output .= '<div class="element-weight">' . $element['atomic_weight'] . '</div>';
        $output .= '</div>';
        $output .= '<div class="element-details">';
        $output .= '<p><strong>原子序数:</strong> ' . $element['atomic_number'] . '</p>';
        $output .= '<p><strong>原子质量:</strong> ' . $element['atomic_weight'] . '</p>';
        $output .= '<p><strong>元素类别:</strong> ' . $this->getCategoryName($element['category']) . '</p>';
        $output .= '<p><strong>周期:</strong> ' . $element['period'] . '</p>';
        $output .= '<p><strong>族:</strong> ' . $element['group'] . '</p>';
        $output .= '</div>';
        $output .= '</div>';
        
        return $output;
    }
    
    /**
     * 渲染化学方程式
     */
    public function renderChemicalEquation($matches) {
        $equation = $matches[1];
        
        // 处理化学符号和下标
        $equation = preg_replace('/([A-Z][a-z]?)(\d+)/', '$1<sub>$2</sub>', $equation);
        
        return '<div class="chemical-equation mathjax">$$\\ce{' . $equation . '}$$</div>';
    }
    
    /**
     * 渲染周期表
     */
    public function renderPeriodicTable($matches) {
        $style = isset($matches[1]) ? $matches[1] : 'modern';
        $layout = $this->config->getOption('lanthanide_actinide_layout', 'horizontal');
        
        $output = '<div class="periodic-table-container">';
        $output .= '<div class="periodic-table ' . $style . '">';
        
        // 渲染主周期表
        $output .= $this->renderMainPeriodicTable();
        
        // 渲染镧系和锕系元素
        if ($layout === 'horizontal') {
            $output .= $this->renderLanthanideActinideHorizontal();
        } else {
            $output .= $this->renderLanthanideActinideVertical();
        }
        
        $output .= '</div>';
        
        // 添加图例
        $output .= $this->renderPeriodicTableLegend();
        
        $output .= '</div>';
        
        return $output;
    }
    
    /**
     * 渲染主周期表
     */
    private function renderMainPeriodicTable() {
        $output = '<div class="main-periodic-table">';
        
        // 渲染7个周期，18个族
        for ($period = 1; $period <= 7; $period++) {
            $output .= '<div class="period-row period-' . $period . '">';
            
            for ($group = 1; $group <= 18; $group++) {
                $element = $this->findElementByPosition($period, $group);
                
                if ($element) {
                    // 跳过镧系和锕系元素在主表中的位置
                    if ($this->isLanthanideActinidePosition($period, $group)) {
                        $output .= $this->renderLanthanideActinidePlaceholder($period, $group);
                    } else {
                        $output .= $this->renderElementCard($element);
                    }
                } else {
                    $output .= '<div class="empty-cell"></div>';
                }
            }
            
            $output .= '</div>';
        }
        
        $output .= '</div>';
        
        return $output;
    }
    
    /**
     * 渲染镧系锕系元素（横向布局）
     */
    private function renderLanthanideActinideHorizontal() {
        $output = '<div class="lanthanide-actinide-section horizontal">';
        
        // 镧系元素
        $output .= '<div class="lanthanide-series">';
        $output .= '<h6>镧系元素（稀土元素）57-71</h6>';
        $output .= '<div class="element-row">';
        for ($atomicNumber = 57; $atomicNumber <= 71; $atomicNumber++) {
            $element = $this->findElementByAtomicNumber($atomicNumber);
            if ($element) {
                $output .= $this->renderElementCard($element);
            }
        }
        $output .= '</div>';
        $output .= '</div>';
        
        // 锕系元素
        $output .= '<div class="actinide-series">';
        $output .= '<h6>锕系元素（放射性元素）89-103</h6>';
        $output .= '<div class="element-row">';
        for ($atomicNumber = 89; $atomicNumber <= 103; $atomicNumber++) {
            $element = $this->findElementByAtomicNumber($atomicNumber);
            if ($element) {
                $output .= $this->renderElementCard($element);
            }
        }
        $output .= '</div>';
        $output .= '</div>';
        
        $output .= '</div>';
        
        return $output;
    }
    
    /**
     * 渲染镧系锕系元素（纵向布局）
     */
    private function renderLanthanideActinideVertical() {
        $output = '<div class="lanthanide-actinide-section vertical">';
        
        // 镧系元素
        $output .= '<div class="lanthanide-series">';
        $output .= '<h6>镧系元素</h6>';
        $output .= '<div class="element-column">';
        for ($atomicNumber = 57; $atomicNumber <= 71; $atomicNumber++) {
            $element = $this->findElementByAtomicNumber($atomicNumber);
            if ($element) {
                $output .= $this->renderElementCard($element);
            }
        }
        $output .= '</div>';
        $output .= '</div>';
        
        // 锕系元素
        $output .= '<div class="actinide-series">';
        $output .= '<h6>锕系元素</h6>';
        $output .= '<div class="element-column">';
        for ($atomicNumber = 89; $atomicNumber <= 103; $atomicNumber++) {
            $element = $this->findElementByAtomicNumber($atomicNumber);
            if ($element) {
                $output .= $this->renderElementCard($element);
            }
        }
        $output .= '</div>';
        $output .= '</div>';
        
        $output .= '</div>';
        
        return $output;
    }
    
    /**
     * 渲染元素卡片
     */
    private function renderElementCard($element) {
        $categoryClass = $this->getCategoryClass($element['category']);
        
        $output = '<div class="element-card ' . $categoryClass . '" ';
        $output .= 'onclick="showElementInfo(\'' . $element['symbol'] . '\', \'' . $element['name'] . '\', ';
        $output .= '\'' . $element['atomic_number'] . '\', \'' . $element['atomic_weight'] . '\', ';
        $output .= '\'' . $this->getCategoryName($element['category']) . '\')">';
        $output .= '<div class="element-number">' . $element['atomic_number'] . '</div>';
        $output .= '<div class="element-symbol">' . $element['symbol'] . '</div>';
        $output .= '<div class="element-name">' . $element['name'] . '</div>';
        $output .= '</div>';
        
        return $output;
    }
    
    /**
     * 渲染镧系锕系占位符
     */
    private function renderLanthanideActinidePlaceholder($period, $group) {
        if ($period === 6 && $group === 3) {
            return '<div class="lanthanide-placeholder">57-71</div>';
        } elseif ($period === 7 && $group === 3) {
            return '<div class="actinide-placeholder">89-103</div>';
        }
        return '<div class="empty-cell"></div>';
    }
    
    /**
     * 检查是否为镧系锕系位置
     */
    private function isLanthanideActinidePosition($period, $group) {
        return ($period === 6 && $group === 3) || ($period === 7 && $group === 3);
    }
    
    /**
     * 根据位置查找元素
     */
    private function findElementByPosition($period, $group) {
        foreach ($this->periodicTable['elements'] as $symbol => $element) {
            if ($element['period'] == $period && $element['group'] == $group) {
                // 排除镧系锕系元素
                if (!in_array($element['category'], ['lanthanide', 'actinide'])) {
                    return array_merge($element, ['symbol' => $symbol]);
                }
            }
        }
        return null;
    }
    
    /**
     * 根据原子序数查找元素
     */
    private function findElementByAtomicNumber($atomicNumber) {
        foreach ($this->periodicTable['elements'] as $symbol => $element) {
            if ($element['atomic_number'] == $atomicNumber) {
                return array_merge($element, ['symbol' => $symbol]);
            }
        }
        return null;
    }
    
    /**
     * 获取元素类别CSS类名
     */
    private function getCategoryClass($category) {
        $classMap = [
            'alkali metal' => 'alkali-metal',
            'alkaline earth metal' => 'alkaline-earth',
            'transition metal' => 'transition-metal',
            'post-transition metal' => 'post-transition',
            'metalloid' => 'metalloid',
            'nonmetal' => 'nonmetal',
            'halogen' => 'halogen',
            'noble gas' => 'noble-gas',
            'lanthanide' => 'lanthanide',
            'actinide' => 'actinide'
        ];
        
        return isset($classMap[$category]) ? $classMap[$category] : 'unknown';
    }
    
    /**
     * 获取元素类别中文名称
     */
    private function getCategoryName($category) {
        return isset($this->periodicTable['categories'][$category]) 
            ? $this->periodicTable['categories'][$category] 
            : $category;
    }
    
    /**
     * 渲染周期表图例
     */
    private function renderPeriodicTableLegend() {
        $output = '<div class="periodic-table-legend">';
        $output .= '<h6>元素类别</h6>';
        $output .= '<div class="legend-items">';
        
        foreach ($this->periodicTable['categories'] as $category => $name) {
            $classname = $this->getCategoryClass($category);
            $output .= '<div class="legend-item">';
            $output .= '<span class="legend-color ' . $classname . '"></span>';
            $output .= '<span class="legend-text">' . $name . '</span>';
            $output .= '</div>';
        }
        
        $output .= '</div>';
        $output .= '</div>';
        
        return $output;
    }
    
    /**
     * 渲染分子
     */
    public function renderMolecule($matches) {
        $formula = $matches[1];
        
        // 处理化学符号和下标
        $formula = preg_replace('/([A-Z][a-z]?)(\d+)/', '$1<sub>$2</sub>', $formula);
        
        return '<span class="molecule-formula">' . $formula . '</span>';
    }
}
