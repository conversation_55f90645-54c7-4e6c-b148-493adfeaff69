<?php
/**
 * 数学工具模块
 *
 * @package     PLB-Links
 * <AUTHOR> Team
 * @version     2.0.0
 */

// 防止直接访问
if (!defined('PLB_LINKS')) {
    exit('不允许直接访问');
}

class MathModule {
    
    private $config;
    
    public function __construct() {
        $this->config = MathScienceConfig::getInstance();
    }
    
    /**
     * 处理数学相关短代码
     */
    public function processShortcodes($content) {
        // 处理计算器短代码
        $content = preg_replace_callback('/\[calculator type="([^"]+)"\]/i', [$this, 'renderCalculator'], $content);
        
        // 处理数学公式短代码
        $content = preg_replace_callback('/\[math_formula\](.*?)\[\/math_formula\]/is', [$this, 'renderFormula'], $content);
        
        // 处理数学函数短代码
        $content = preg_replace_callback('/\[math_function name="([^"]+)"(.*?)\/\]/i', [$this, 'renderMathFunction'], $content);
        
        return $content;
    }
    
    /**
     * 渲染计算器
     */
    public function renderCalculator($matches) {
        $type = $matches[1];
        
        switch ($type) {
            case 'basic':
                return $this->renderBasicCalculator();
            case 'scientific':
                return $this->renderScientificCalculator();
            case 'graphing':
                return $this->renderGraphingCalculator();
            default:
                return $this->renderBasicCalculator();
        }
    }
    
    /**
     * 渲染基础计算器
     */
    private function renderBasicCalculator() {
        return '
        <div class="math-calculator basic-calculator">
            <div class="calculator-display">
                <input type="text" id="calc-display" readonly>
            </div>
            <div class="calculator-buttons">
                <div class="calc-row">
                    <button onclick="clearDisplay()">C</button>
                    <button onclick="clearEntry()">CE</button>
                    <button onclick="backspace()">⌫</button>
                    <button onclick="appendToDisplay(\'/\')">÷</button>
                </div>
                <div class="calc-row">
                    <button onclick="appendToDisplay(\'7\')">7</button>
                    <button onclick="appendToDisplay(\'8\')">8</button>
                    <button onclick="appendToDisplay(\'9\')">9</button>
                    <button onclick="appendToDisplay(\'*\')">×</button>
                </div>
                <div class="calc-row">
                    <button onclick="appendToDisplay(\'4\')">4</button>
                    <button onclick="appendToDisplay(\'5\')">5</button>
                    <button onclick="appendToDisplay(\'6\')">6</button>
                    <button onclick="appendToDisplay(\'-\')">-</button>
                </div>
                <div class="calc-row">
                    <button onclick="appendToDisplay(\'1\')">1</button>
                    <button onclick="appendToDisplay(\'2\')">2</button>
                    <button onclick="appendToDisplay(\'3\')">3</button>
                    <button onclick="appendToDisplay(\'+\')">+</button>
                </div>
                <div class="calc-row">
                    <button onclick="appendToDisplay(\'0\')" class="calc-zero">0</button>
                    <button onclick="appendToDisplay(\'.\')">.</button>
                    <button onclick="calculate()" class="calc-equals">=</button>
                </div>
            </div>
        </div>';
    }
    
    /**
     * 渲染科学计算器
     */
    private function renderScientificCalculator() {
        return '
        <div class="math-calculator scientific-calculator">
            <div class="calculator-display">
                <input type="text" id="sci-calc-display" readonly>
            </div>
            <div class="calculator-buttons scientific-buttons">
                <div class="calc-row">
                    <button onclick="clearDisplay()">C</button>
                    <button onclick="clearEntry()">CE</button>
                    <button onclick="backspace()">⌫</button>
                    <button onclick="appendFunction(\'sin(\')">sin</button>
                    <button onclick="appendFunction(\'cos(\')">cos</button>
                    <button onclick="appendFunction(\'tan(\')">tan</button>
                </div>
                <div class="calc-row">
                    <button onclick="appendToDisplay(\'7\')">7</button>
                    <button onclick="appendToDisplay(\'8\')">8</button>
                    <button onclick="appendToDisplay(\'9\')">9</button>
                    <button onclick="appendFunction(\'log(\')">log</button>
                    <button onclick="appendFunction(\'ln(\')">ln</button>
                    <button onclick="appendToDisplay(\'^\')">x^y</button>
                </div>
                <div class="calc-row">
                    <button onclick="appendToDisplay(\'4\')">4</button>
                    <button onclick="appendToDisplay(\'5\')">5</button>
                    <button onclick="appendToDisplay(\'6\')">6</button>
                    <button onclick="appendFunction(\'sqrt(\')">√</button>
                    <button onclick="appendConstant(\'π\')">π</button>
                    <button onclick="appendConstant(\'e\')">e</button>
                </div>
                <div class="calc-row">
                    <button onclick="appendToDisplay(\'1\')">1</button>
                    <button onclick="appendToDisplay(\'2\')">2</button>
                    <button onclick="appendToDisplay(\'3\')">3</button>
                    <button onclick="appendToDisplay(\'+\')">+</button>
                    <button onclick="appendToDisplay(\'-\')">-</button>
                    <button onclick="appendToDisplay(\'*\')">×</button>
                </div>
                <div class="calc-row">
                    <button onclick="appendToDisplay(\'0\')" class="calc-zero">0</button>
                    <button onclick="appendToDisplay(\'.\')">.</button>
                    <button onclick="appendToDisplay(\'/\')">÷</button>
                    <button onclick="calculate()" class="calc-equals">=</button>
                </div>
            </div>
        </div>';
    }
    
    /**
     * 渲染图形计算器
     */
    private function renderGraphingCalculator() {
        return '
        <div class="math-calculator graphing-calculator">
            <div class="calculator-display">
                <input type="text" id="graph-calc-display" placeholder="输入函数，如: x^2 + 2*x + 1">
                <button onclick="plotFunction()">绘制图形</button>
            </div>
            <div class="graph-container">
                <canvas id="function-graph" width="400" height="300"></canvas>
            </div>
            <div class="graph-controls">
                <label>X范围: 
                    <input type="number" id="x-min" value="-10" step="0.1"> 到 
                    <input type="number" id="x-max" value="10" step="0.1">
                </label>
                <label>Y范围: 
                    <input type="number" id="y-min" value="-10" step="0.1"> 到 
                    <input type="number" id="y-max" value="10" step="0.1">
                </label>
            </div>
        </div>';
    }
    
    /**
     * 渲染数学公式
     */
    public function renderFormula($matches) {
        $formula = $matches[1];
        return '<div class="math-formula mathjax">$$' . $formula . '$$</div>';
    }
    
    /**
     * 渲染数学函数
     */
    public function renderMathFunction($matches) {
        $functionName = $matches[1];
        $attributes = $matches[2];
        
        // 解析属性
        $params = [];
        preg_match_all('/([a-zA-Z0-9_]+)="([^"]+)"/', $attributes, $attrMatches, PREG_SET_ORDER);
        foreach ($attrMatches as $match) {
            $params[$match[1]] = $match[2];
        }
        
        return $this->calculateMathFunction($functionName, $params);
    }
    
    /**
     * 计算数学函数
     */
    private function calculateMathFunction($functionName, $params) {
        $precision = $this->config->getOption('calculator_precision', 10);
        
        switch ($functionName) {
            case 'factorial':
                if (isset($params['n'])) {
                    $n = intval($params['n']);
                    $result = $this->factorial($n);
                    return "<div class=\"math-result\">$n! = $result</div>";
                }
                break;
                
            case 'fibonacci':
                if (isset($params['n'])) {
                    $n = intval($params['n']);
                    $result = $this->fibonacci($n);
                    return "<div class=\"math-result\">F($n) = $result</div>";
                }
                break;
                
            case 'gcd':
                if (isset($params['a']) && isset($params['b'])) {
                    $a = intval($params['a']);
                    $b = intval($params['b']);
                    $result = $this->gcd($a, $b);
                    return "<div class=\"math-result\">gcd($a, $b) = $result</div>";
                }
                break;
                
            case 'lcm':
                if (isset($params['a']) && isset($params['b'])) {
                    $a = intval($params['a']);
                    $b = intval($params['b']);
                    $result = $this->lcm($a, $b);
                    return "<div class=\"math-result\">lcm($a, $b) = $result</div>";
                }
                break;
        }
        
        return '<div class="math-error">无效的数学函数或参数</div>';
    }
    
    /**
     * 计算阶乘
     */
    private function factorial($n) {
        if ($n < 0) return 'undefined';
        if ($n <= 1) return 1;
        
        $result = 1;
        for ($i = 2; $i <= $n; $i++) {
            $result *= $i;
        }
        return $result;
    }
    
    /**
     * 计算斐波那契数列
     */
    private function fibonacci($n) {
        if ($n < 0) return 'undefined';
        if ($n <= 1) return $n;
        
        $a = 0;
        $b = 1;
        for ($i = 2; $i <= $n; $i++) {
            $temp = $a + $b;
            $a = $b;
            $b = $temp;
        }
        return $b;
    }
    
    /**
     * 计算最大公约数
     */
    private function gcd($a, $b) {
        while ($b != 0) {
            $temp = $b;
            $b = $a % $b;
            $a = $temp;
        }
        return abs($a);
    }
    
    /**
     * 计算最小公倍数
     */
    private function lcm($a, $b) {
        return abs($a * $b) / $this->gcd($a, $b);
    }
}
