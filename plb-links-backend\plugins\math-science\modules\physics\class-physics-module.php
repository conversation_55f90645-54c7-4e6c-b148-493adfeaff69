<?php
/**
 * 物理工具模块
 *
 * @package     PLB-Links
 * <AUTHOR> Team
 * @version     2.0.0
 */

// 防止直接访问
if (!defined('PLB_LINKS')) {
    exit('不允许直接访问');
}

class PhysicsModule {
    
    private $config;
    private $conversionFactors;
    
    public function __construct() {
        $this->config = MathScienceConfig::getInstance();
        $this->initConversionFactors();
    }
    
    /**
     * 初始化转换因子
     */
    private function initConversionFactors() {
        $this->conversionFactors = [
            // 长度单位 (基准: 米)
            'length' => [
                'mm' => 0.001,
                'cm' => 0.01,
                'dm' => 0.1,
                'm' => 1.0,
                'km' => 1000.0,
                'in' => 0.0254,
                'ft' => 0.3048,
                'yd' => 0.9144,
                'mi' => 1609.344,
                'nm' => 1e-9,
                'μm' => 1e-6,
                'ly' => 9.461e15
            ],
            
            // 质量单位 (基准: 千克)
            'mass' => [
                'mg' => 1e-6,
                'g' => 0.001,
                'kg' => 1.0,
                'ton' => 1000.0,
                'lb' => 0.45359237,
                'oz' => 0.028349523125,
                'u' => 1.66054e-27
            ],
            
            // 时间单位 (基准: 秒)
            'time' => [
                'ns' => 1e-9,
                'μs' => 1e-6,
                'ms' => 0.001,
                's' => 1.0,
                'min' => 60.0,
                'h' => 3600.0,
                'day' => 86400.0,
                'week' => 604800.0,
                'year' => 31557600.0
            ],
            
            // 能量单位 (基准: 焦耳)
            'energy' => [
                'J' => 1.0,
                'kJ' => 1000.0,
                'MJ' => 1e6,
                'cal' => 4.184,
                'kcal' => 4184.0,
                'eV' => 1.602176634e-19,
                'keV' => 1.602176634e-16,
                'MeV' => 1.602176634e-13,
                'Wh' => 3600.0,
                'kWh' => 3.6e6
            ],
            
            // 功率单位 (基准: 瓦特)
            'power' => [
                'W' => 1.0,
                'kW' => 1000.0,
                'MW' => 1e6,
                'hp' => 745.7
            ],
            
            // 压强单位 (基准: 帕斯卡)
            'pressure' => [
                'Pa' => 1.0,
                'kPa' => 1000.0,
                'MPa' => 1e6,
                'bar' => 1e5,
                'atm' => 101325.0,
                'mmHg' => 133.322,
                'psi' => 6894.76
            ]
        ];
    }
    
    /**
     * 处理物理相关短代码
     */
    public function processShortcodes($content) {
        // 处理单位转换短代码
        $content = preg_replace_callback('/\[unit_convert value="([^"]+)" from="([^"]+)" to="([^"]+)"\]/i', 
            [$this, 'renderUnitConversion'], $content);
        
        // 处理物理公式短代码
        $content = preg_replace_callback('/\[physics_formula name="([^"]+)"(.*?)\/\]/i', 
            [$this, 'renderPhysicsFormula'], $content);
        
        // 处理物理常数短代码
        $content = preg_replace_callback('/\[physics_constant name="([^"]+)"\]/i', 
            [$this, 'renderPhysicsConstant'], $content);
        
        return $content;
    }
    
    /**
     * 渲染单位转换
     */
    public function renderUnitConversion($matches) {
        $value = floatval($matches[1]);
        $from = $matches[2];
        $to = $matches[3];
        
        $result = $this->convertUnit($value, $from, $to);
        
        if (is_numeric($result)) {
            $precision = $this->config->getOption('unit_conversion_precision', 6);
            $result = round($result, $precision);
            return "<span class=\"unit-conversion\">{$value} {$from} = {$result} {$to}</span>";
        } else {
            return "<span class=\"unit-conversion-error\">{$result}</span>";
        }
    }
    
    /**
     * 单位转换核心函数
     */
    public function convertUnit($value, $from, $to) {
        // 温度转换特殊处理
        if ($this->isTemperatureUnit($from) || $this->isTemperatureUnit($to)) {
            return $this->convertTemperature($value, $from, $to);
        }
        
        // 查找单位所属类别
        $fromCategory = $this->findUnitCategory($from);
        $toCategory = $this->findUnitCategory($to);
        
        if (!$fromCategory || !$toCategory) {
            return '不支持的单位';
        }
        
        if ($fromCategory !== $toCategory) {
            return '不同类别的单位无法转换';
        }
        
        $factors = $this->conversionFactors[$fromCategory];
        
        if (!isset($factors[$from]) || !isset($factors[$to])) {
            return '不支持的单位转换';
        }
        
        // 转换为基本单位，再转换为目标单位
        $baseValue = $value * $factors[$from];
        $result = $baseValue / $factors[$to];
        
        return $result;
    }
    
    /**
     * 温度转换
     */
    private function convertTemperature($value, $from, $to) {
        // 摄氏度转换
        if ($from === 'C') {
            if ($to === 'F') return ($value * 9/5) + 32;
            if ($to === 'K') return $value + 273.15;
            if ($to === 'R') return ($value + 273.15) * 9/5;
        }
        
        // 华氏度转换
        if ($from === 'F') {
            if ($to === 'C') return ($value - 32) * 5/9;
            if ($to === 'K') return ($value - 32) * 5/9 + 273.15;
            if ($to === 'R') return $value + 459.67;
        }
        
        // 开尔文转换
        if ($from === 'K') {
            if ($to === 'C') return $value - 273.15;
            if ($to === 'F') return ($value - 273.15) * 9/5 + 32;
            if ($to === 'R') return $value * 9/5;
        }
        
        // 兰金度转换
        if ($from === 'R') {
            if ($to === 'C') return ($value - 491.67) * 5/9;
            if ($to === 'F') return $value - 459.67;
            if ($to === 'K') return $value * 5/9;
        }
        
        if ($from === $to) return $value;
        
        return '不支持的温度转换';
    }
    
    /**
     * 检查是否为温度单位
     */
    private function isTemperatureUnit($unit) {
        return in_array($unit, ['C', 'F', 'K', 'R']);
    }
    
    /**
     * 查找单位所属类别
     */
    private function findUnitCategory($unit) {
        foreach ($this->conversionFactors as $category => $units) {
            if (isset($units[$unit])) {
                return $category;
            }
        }
        return null;
    }
    
    /**
     * 渲染物理公式
     */
    public function renderPhysicsFormula($matches) {
        $formulaName = $matches[1];
        $attributes = $matches[2];
        
        // 解析属性
        $params = [];
        preg_match_all('/([a-zA-Z0-9_]+)="([^"]+)"/', $attributes, $attrMatches, PREG_SET_ORDER);
        foreach ($attrMatches as $match) {
            $params[$match[1]] = $match[2];
        }
        
        return $this->calculatePhysicsFormula($formulaName, $params);
    }
    
    /**
     * 计算物理公式
     */
    private function calculatePhysicsFormula($formulaName, $params) {
        $formulas = $this->getPhysicsFormulas();
        
        if (!isset($formulas[$formulaName])) {
            return '<div class="physics-error">未知的物理公式</div>';
        }
        
        $formula = $formulas[$formulaName];
        $output = '<div class="physics-formula">';
        $output .= '<div class="formula-name">' . $formula['name'] . '</div>';
        $output .= '<div class="formula-description">' . $formula['description'] . '</div>';
        $output .= '<div class="formula-tex mathjax">$$' . $formula['latex'] . '$$</div>';
        
        // 计算结果
        if (is_callable($formula['calculate'])) {
            $result = $formula['calculate']($params);
            if ($result !== null) {
                $precision = $this->config->getOption('calculator_precision', 10);
                $result = round($result, $precision);
                $output .= '<div class="formula-result">计算结果: ' . $result . ' ' . ($formula['unit'] ?? '') . '</div>';
            }
        }
        
        $output .= '</div>';
        
        return $output;
    }
    
    /**
     * 获取物理公式定义
     */
    private function getPhysicsFormulas() {
        return [
            'velocity' => [
                'name' => '速度公式',
                'description' => '速度等于位移除以时间',
                'latex' => 'v = \\frac{s}{t}',
                'unit' => 'm/s',
                'calculate' => function($params) {
                    if (isset($params['s']) && isset($params['t']) && $params['t'] != 0) {
                        return floatval($params['s']) / floatval($params['t']);
                    }
                    return null;
                }
            ],
            
            'acceleration' => [
                'name' => '加速度公式',
                'description' => '加速度等于速度变化除以时间',
                'latex' => 'a = \\frac{\\Delta v}{\\Delta t}',
                'unit' => 'm/s²',
                'calculate' => function($params) {
                    if (isset($params['dv']) && isset($params['dt']) && $params['dt'] != 0) {
                        return floatval($params['dv']) / floatval($params['dt']);
                    }
                    return null;
                }
            ],
            
            'force' => [
                'name' => '牛顿第二定律',
                'description' => '力等于质量乘以加速度',
                'latex' => 'F = m \\cdot a',
                'unit' => 'N',
                'calculate' => function($params) {
                    if (isset($params['m']) && isset($params['a'])) {
                        return floatval($params['m']) * floatval($params['a']);
                    }
                    return null;
                }
            ],
            
            'kinetic_energy' => [
                'name' => '动能公式',
                'description' => '动能等于二分之一质量乘以速度的平方',
                'latex' => 'E_k = \\frac{1}{2}mv^2',
                'unit' => 'J',
                'calculate' => function($params) {
                    if (isset($params['m']) && isset($params['v'])) {
                        return 0.5 * floatval($params['m']) * pow(floatval($params['v']), 2);
                    }
                    return null;
                }
            ],
            
            'potential_energy' => [
                'name' => '重力势能公式',
                'description' => '重力势能等于质量乘以重力加速度乘以高度',
                'latex' => 'E_p = mgh',
                'unit' => 'J',
                'calculate' => function($params) {
                    if (isset($params['m']) && isset($params['h'])) {
                        $g = floatval($params['g'] ?? 9.8);
                        return floatval($params['m']) * $g * floatval($params['h']);
                    }
                    return null;
                }
            ]
        ];
    }
    
    /**
     * 渲染物理常数
     */
    public function renderPhysicsConstant($matches) {
        $constantName = $matches[1];
        $constants = $this->getPhysicsConstants();
        
        if (isset($constants[$constantName])) {
            $constant = $constants[$constantName];
            return "<span class=\"physics-constant\" title=\"{$constant['description']}\">" . 
                   "{$constant['symbol']} = {$constant['value']} {$constant['unit']}</span>";
        }
        
        return '<span class="physics-error">未知的物理常数</span>';
    }
    
    /**
     * 获取物理常数
     */
    private function getPhysicsConstants() {
        return [
            'c' => [
                'symbol' => 'c',
                'value' => '2.998×10⁸',
                'unit' => 'm/s',
                'description' => '光速'
            ],
            'g' => [
                'symbol' => 'g',
                'value' => '9.8',
                'unit' => 'm/s²',
                'description' => '重力加速度'
            ],
            'h' => [
                'symbol' => 'h',
                'value' => '6.626×10⁻³⁴',
                'unit' => 'J·s',
                'description' => '普朗克常数'
            ],
            'k' => [
                'symbol' => 'k',
                'value' => '1.381×10⁻²³',
                'unit' => 'J/K',
                'description' => '玻尔兹曼常数'
            ],
            'e' => [
                'symbol' => 'e',
                'value' => '1.602×10⁻¹⁹',
                'unit' => 'C',
                'description' => '基本电荷'
            ]
        ];
    }
}
