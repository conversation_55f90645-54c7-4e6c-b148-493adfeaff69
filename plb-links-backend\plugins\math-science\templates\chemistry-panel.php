<?php
/**
 * 化学工具面板模板
 */

// 获取化学模块
require_once MATH_SCIENCE_PLUGIN_DIR . '/modules/chemistry/class-chemistry-module.php';
$chemistryModule = new ChemistryModule();

// 获取配置
$config = MathScienceConfig::getInstance();
$layout = $config->getOption('lanthanide_actinide_layout', 'horizontal');
?>

<div class="chemistry-tools-panel">
    <div class="row">
        <!-- 元素周期表 -->
        <div class="col-12 mb-4">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5><i class="bi bi-diagram-3"></i> 元素周期表</h5>
                    <div class="btn-group btn-group-sm" role="group">
                        <input type="radio" class="btn-check" name="layout-options" id="layout-horizontal" <?= $layout === 'horizontal' ? 'checked' : '' ?> onchange="togglePeriodicTableLayout('horizontal')">
                        <label class="btn btn-outline-primary" for="layout-horizontal">横向布局</label>
                        
                        <input type="radio" class="btn-check" name="layout-options" id="layout-vertical" <?= $layout === 'vertical' ? 'checked' : '' ?> onchange="togglePeriodicTableLayout('vertical')">
                        <label class="btn btn-outline-primary" for="layout-vertical">纵向布局</label>
                    </div>
                </div>
                <div class="card-body">
                    <div id="periodic-table-container">
                        <?= $chemistryModule->renderPeriodicTable(['']) ?>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- 元素查询 -->
        <div class="col-lg-6 mb-4">
            <div class="card">
                <div class="card-header">
                    <h5><i class="bi bi-search"></i> 元素查询</h5>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <label class="form-label">查询方式</label>
                        <select class="form-select" id="element-search-type">
                            <option value="symbol">元素符号</option>
                            <option value="name">元素名称</option>
                            <option value="atomic_number">原子序数</option>
                        </select>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">输入查询内容</label>
                        <input type="text" class="form-control" id="element-search-input" placeholder="例如: H, 氢, 1">
                    </div>
                    <button class="btn btn-primary" onclick="searchElement()">查询元素</button>
                    <div id="element-search-result" class="mt-3"></div>
                </div>
            </div>
        </div>

        <!-- 化学方程式平衡 -->
        <div class="col-lg-6 mb-4">
            <div class="card">
                <div class="card-header">
                    <h5><i class="bi bi-balance-scale"></i> 化学方程式</h5>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <label class="form-label">输入化学方程式</label>
                        <input type="text" class="form-control" id="chemical-equation-input" placeholder="例如: H2 + O2 -> H2O">
                        <div class="form-text">使用 -> 或 = 表示反应箭头</div>
                    </div>
                    <button class="btn btn-primary" onclick="renderChemicalEquation()">渲染方程式</button>
                    <div class="mt-3">
                        <h6>渲染结果:</h6>
                        <div id="chemical-equation-result" class="border p-3 bg-light mathjax" style="min-height: 60px;">
                            <!-- 化学方程式将在这里显示 -->
                        </div>
                    </div>
                    <div class="mt-3">
                        <h6>常用方程式示例:</h6>
                        <div class="d-flex flex-wrap gap-2">
                            <button class="btn btn-outline-secondary btn-sm" onclick="insertEquationExample('2H2 + O2 -> 2H2O')">水的生成</button>
                            <button class="btn btn-outline-secondary btn-sm" onclick="insertEquationExample('CH4 + 2O2 -> CO2 + 2H2O')">甲烷燃烧</button>
                            <button class="btn btn-outline-secondary btn-sm" onclick="insertEquationExample('CaCO3 -> CaO + CO2')">碳酸钙分解</button>
                            <button class="btn btn-outline-secondary btn-sm" onclick="insertEquationExample('NaCl + AgNO3 -> AgCl + NaNO3')">沉淀反应</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- 分子量计算器 -->
        <div class="col-lg-6 mb-4">
            <div class="card">
                <div class="card-header">
                    <h5><i class="bi bi-calculator"></i> 分子量计算</h5>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <label class="form-label">输入分子式</label>
                        <input type="text" class="form-control" id="molecular-formula-input" placeholder="例如: H2SO4, C6H12O6">
                        <div class="form-text">支持常见的分子式格式</div>
                    </div>
                    <button class="btn btn-primary" onclick="calculateMolecularWeight()">计算分子量</button>
                    <div id="molecular-weight-result" class="mt-3"></div>
                    <div class="mt-3">
                        <h6>常用分子示例:</h6>
                        <div class="d-flex flex-wrap gap-2">
                            <button class="btn btn-outline-secondary btn-sm" onclick="insertMolecularExample('H2O')">水</button>
                            <button class="btn btn-outline-secondary btn-sm" onclick="insertMolecularExample('CO2')">二氧化碳</button>
                            <button class="btn btn-outline-secondary btn-sm" onclick="insertMolecularExample('H2SO4')">硫酸</button>
                            <button class="btn btn-outline-secondary btn-sm" onclick="insertMolecularExample('C6H12O6')">葡萄糖</button>
                            <button class="btn btn-outline-secondary btn-sm" onclick="insertMolecularExample('NaCl')">氯化钠</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 化学计算器 -->
        <div class="col-lg-6 mb-4">
            <div class="card">
                <div class="card-header">
                    <h5><i class="bi bi-gear-fill"></i> 化学计算</h5>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <label class="form-label">计算类型</label>
                        <select class="form-select" id="chemistry-calc-type" onchange="updateChemistryCalcInputs()">
                            <option value="molarity">摩尔浓度计算</option>
                            <option value="ph">pH值计算</option>
                            <option value="gas_law">理想气体定律</option>
                            <option value="stoichiometry">化学计量</option>
                        </select>
                    </div>
                    <div id="chemistry-calc-inputs">
                        <!-- 计算输入字段将通过JavaScript动态生成 -->
                    </div>
                    <button class="btn btn-primary" onclick="calculateChemistry()">计算</button>
                    <div id="chemistry-calc-result" class="mt-3"></div>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- 元素周期表图例 -->
        <div class="col-12 mb-4">
            <div class="card">
                <div class="card-header">
                    <h5><i class="bi bi-palette"></i> 元素类别图例</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="d-flex align-items-center mb-2">
                                <span class="legend-color alkali-metal me-2"></span>
                                <span>碱金属</span>
                            </div>
                            <div class="d-flex align-items-center mb-2">
                                <span class="legend-color alkaline-earth me-2"></span>
                                <span>碱土金属</span>
                            </div>
                            <div class="d-flex align-items-center mb-2">
                                <span class="legend-color transition-metal me-2"></span>
                                <span>过渡金属</span>
                            </div>
                            <div class="d-flex align-items-center mb-2">
                                <span class="legend-color post-transition me-2"></span>
                                <span>主族金属</span>
                            </div>
                            <div class="d-flex align-items-center mb-2">
                                <span class="legend-color metalloid me-2"></span>
                                <span>类金属</span>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="d-flex align-items-center mb-2">
                                <span class="legend-color nonmetal me-2"></span>
                                <span>非金属</span>
                            </div>
                            <div class="d-flex align-items-center mb-2">
                                <span class="legend-color halogen me-2"></span>
                                <span>卤素</span>
                            </div>
                            <div class="d-flex align-items-center mb-2">
                                <span class="legend-color noble-gas me-2"></span>
                                <span>惰性气体</span>
                            </div>
                            <div class="d-flex align-items-center mb-2">
                                <span class="legend-color lanthanide me-2"></span>
                                <span>镧系元素（稀土元素）</span>
                            </div>
                            <div class="d-flex align-items-center mb-2">
                                <span class="legend-color actinide me-2"></span>
                                <span>锕系元素（放射性元素）</span>
                            </div>
                        </div>
                    </div>
                    <div class="alert alert-info mt-3">
                        <small>
                            <strong><i class="bi bi-info-circle"></i> 提示：</strong>
                            点击任意元素可查看详细信息。镧系和锕系元素的布局可以通过上方的按钮切换。
                        </small>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
