<?php
/**
 * 数理化插件主页面模板
 *
 * @package     PLB-Links
 * <AUTHOR> Team
 * @version     2.0.0
 */

// 防止直接访问
if (!defined('PLB_LINKS')) {
    exit('不允许直接访问');
}

// 获取插件配置
$config = MathScienceConfig::getInstance();
$options = $config->getOptions();

$plugin = [
    'name' => '数理化工具集',
    'description' => '集成数学、物理、化学计算工具和元素周期表',
    'version' => '2.0.0',
    'author' => 'PLB团队',
    'options' => $options
];
?>

<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?= htmlspecialchars($plugin['name']) ?> - PLB插件中心</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <link rel="stylesheet" href="<?= MATH_SCIENCE_PLUGIN_URL ?>/assets/css/math-science.css">
    
    <?php if ($options['enable_math_formula']): ?>
    <script id="MathJax-script" async src="<?= $options['mathjax_cdn'] ?>"></script>
    <script>
    window.MathJax = {
        tex: {
            inlineMath: [["$", "$"], ["\\(", "\\)"]],
            displayMath: [["$$", "$$"], ["\\[", "\\]"]],
            processEscapes: true,
            processEnvironments: true
        },
        options: {
            ignoreHtmlClass: "no-mathjax|ignore-mathjax",
            processHtmlClass: "mathjax|has-mathjax"
        }
    };
    </script>
    <?php endif; ?>
</head>
<body>
    <div class="main-container">
        <div class="container-fluid">
            <!-- 插件头部 -->
            <div class="plugin-header text-center py-4">
                <h1 class="plugin-title">
                    <i class="bi bi-calculator"></i>
                    <?= htmlspecialchars($plugin['name']) ?>
                </h1>
                <p class="plugin-description"><?= htmlspecialchars($plugin['description']) ?></p>
                <div class="plugin-meta">
                    <span class="badge bg-primary">版本 <?= htmlspecialchars($plugin['version']) ?></span>
                    <span class="badge bg-secondary">作者: <?= htmlspecialchars($plugin['author']) ?></span>
                </div>
            </div>

            <!-- 功能导航 -->
            <div class="function-nav mb-4">
                <ul class="nav nav-pills nav-fill" id="function-tabs" role="tablist">
                    <?php if ($options['enable_math_tools']): ?>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link active" id="math-tab" data-bs-toggle="pill" data-bs-target="#math-panel" type="button" role="tab">
                            <i class="bi bi-calculator"></i> 数学工具
                        </button>
                    </li>
                    <?php endif; ?>
                    
                    <?php if ($options['enable_physics_tools']): ?>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="physics-tab" data-bs-toggle="pill" data-bs-target="#physics-panel" type="button" role="tab">
                            <i class="bi bi-speedometer2"></i> 物理工具
                        </button>
                    </li>
                    <?php endif; ?>
                    
                    <?php if ($options['enable_chemistry_tools']): ?>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="chemistry-tab" data-bs-toggle="pill" data-bs-target="#chemistry-panel" type="button" role="tab">
                            <i class="bi bi-diagram-3"></i> 化学工具
                        </button>
                    </li>
                    <?php endif; ?>
                </ul>
            </div>

            <!-- 功能面板 -->
            <div class="tab-content" id="function-panels">
                <?php if ($options['enable_math_tools']): ?>
                <div class="tab-pane fade show active" id="math-panel" role="tabpanel">
                    <?php include MATH_SCIENCE_PLUGIN_DIR . '/templates/math-panel.php'; ?>
                </div>
                <?php endif; ?>
                
                <?php if ($options['enable_physics_tools']): ?>
                <div class="tab-pane fade" id="physics-panel" role="tabpanel">
                    <?php include MATH_SCIENCE_PLUGIN_DIR . '/templates/physics-panel.php'; ?>
                </div>
                <?php endif; ?>
                
                <?php if ($options['enable_chemistry_tools']): ?>
                <div class="tab-pane fade" id="chemistry-panel" role="tabpanel">
                    <?php include MATH_SCIENCE_PLUGIN_DIR . '/templates/chemistry-panel.php'; ?>
                </div>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <!-- 元素信息模态框 -->
    <div class="modal fade" id="elementModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">元素信息</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body" id="elementModalBody">
                    <!-- 元素信息将在这里显示 -->
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="<?= MATH_SCIENCE_PLUGIN_URL ?>/assets/js/math-science.js"></script>
</body>
</html>
