<?php
/**
 * 数学工具面板模板
 */
?>

<div class="math-tools-panel">
    <div class="row">
        <!-- 基础计算器 -->
        <div class="col-lg-6 mb-4">
            <div class="card">
                <div class="card-header">
                    <h5><i class="bi bi-calculator"></i> 基础计算器</h5>
                </div>
                <div class="card-body">
                    <div class="calculator basic-calculator">
                        <div class="calculator-display">
                            <input type="text" id="calc-display" class="form-control text-end" readonly value="0">
                        </div>
                        <div class="calculator-buttons mt-3">
                            <div class="row g-2">
                                <div class="col-3"><button class="btn btn-outline-danger w-100" onclick="clearDisplay()">C</button></div>
                                <div class="col-3"><button class="btn btn-outline-warning w-100" onclick="clearEntry()">CE</button></div>
                                <div class="col-3"><button class="btn btn-outline-secondary w-100" onclick="backspace()">⌫</button></div>
                                <div class="col-3"><button class="btn btn-outline-primary w-100" onclick="appendToDisplay('/')">/</button></div>
                            </div>
                            <div class="row g-2 mt-1">
                                <div class="col-3"><button class="btn btn-outline-dark w-100" onclick="appendToDisplay('7')">7</button></div>
                                <div class="col-3"><button class="btn btn-outline-dark w-100" onclick="appendToDisplay('8')">8</button></div>
                                <div class="col-3"><button class="btn btn-outline-dark w-100" onclick="appendToDisplay('9')">9</button></div>
                                <div class="col-3"><button class="btn btn-outline-primary w-100" onclick="appendToDisplay('*')">×</button></div>
                            </div>
                            <div class="row g-2 mt-1">
                                <div class="col-3"><button class="btn btn-outline-dark w-100" onclick="appendToDisplay('4')">4</button></div>
                                <div class="col-3"><button class="btn btn-outline-dark w-100" onclick="appendToDisplay('5')">5</button></div>
                                <div class="col-3"><button class="btn btn-outline-dark w-100" onclick="appendToDisplay('6')">6</button></div>
                                <div class="col-3"><button class="btn btn-outline-primary w-100" onclick="appendToDisplay('-')">-</button></div>
                            </div>
                            <div class="row g-2 mt-1">
                                <div class="col-3"><button class="btn btn-outline-dark w-100" onclick="appendToDisplay('1')">1</button></div>
                                <div class="col-3"><button class="btn btn-outline-dark w-100" onclick="appendToDisplay('2')">2</button></div>
                                <div class="col-3"><button class="btn btn-outline-dark w-100" onclick="appendToDisplay('3')">3</button></div>
                                <div class="col-3"><button class="btn btn-outline-primary w-100" onclick="appendToDisplay('+')">+</button></div>
                            </div>
                            <div class="row g-2 mt-1">
                                <div class="col-6"><button class="btn btn-outline-dark w-100" onclick="appendToDisplay('0')">0</button></div>
                                <div class="col-3"><button class="btn btn-outline-dark w-100" onclick="appendToDisplay('.')">.</button></div>
                                <div class="col-3"><button class="btn btn-success w-100" onclick="calculate()">=</button></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 科学计算器 -->
        <div class="col-lg-6 mb-4">
            <div class="card">
                <div class="card-header">
                    <h5><i class="bi bi-gear"></i> 科学计算器</h5>
                </div>
                <div class="card-body">
                    <div class="calculator scientific-calculator">
                        <div class="calculator-display">
                            <input type="text" id="sci-calc-display" class="form-control text-end" readonly value="0">
                        </div>
                        <div class="calculator-buttons mt-3">
                            <div class="row g-1">
                                <div class="col-2"><button class="btn btn-outline-danger btn-sm w-100" onclick="clearScientificDisplay()">C</button></div>
                                <div class="col-2"><button class="btn btn-outline-warning btn-sm w-100" onclick="clearScientificEntry()">CE</button></div>
                                <div class="col-2"><button class="btn btn-outline-secondary btn-sm w-100" onclick="scientificBackspace()">⌫</button></div>
                                <div class="col-2"><button class="btn btn-outline-info btn-sm w-100" onclick="appendScientificFunction('sin(')">sin</button></div>
                                <div class="col-2"><button class="btn btn-outline-info btn-sm w-100" onclick="appendScientificFunction('cos(')">cos</button></div>
                                <div class="col-2"><button class="btn btn-outline-info btn-sm w-100" onclick="appendScientificFunction('tan(')">tan</button></div>
                            </div>
                            <div class="row g-1 mt-1">
                                <div class="col-2"><button class="btn btn-outline-dark btn-sm w-100" onclick="appendToScientificDisplay('7')">7</button></div>
                                <div class="col-2"><button class="btn btn-outline-dark btn-sm w-100" onclick="appendToScientificDisplay('8')">8</button></div>
                                <div class="col-2"><button class="btn btn-outline-dark btn-sm w-100" onclick="appendToScientificDisplay('9')">9</button></div>
                                <div class="col-2"><button class="btn btn-outline-info btn-sm w-100" onclick="appendScientificFunction('log(')">log</button></div>
                                <div class="col-2"><button class="btn btn-outline-info btn-sm w-100" onclick="appendScientificFunction('ln(')">ln</button></div>
                                <div class="col-2"><button class="btn btn-outline-primary btn-sm w-100" onclick="appendToScientificDisplay('^')">x^y</button></div>
                            </div>
                            <div class="row g-1 mt-1">
                                <div class="col-2"><button class="btn btn-outline-dark btn-sm w-100" onclick="appendToScientificDisplay('4')">4</button></div>
                                <div class="col-2"><button class="btn btn-outline-dark btn-sm w-100" onclick="appendToScientificDisplay('5')">5</button></div>
                                <div class="col-2"><button class="btn btn-outline-dark btn-sm w-100" onclick="appendToScientificDisplay('6')">6</button></div>
                                <div class="col-2"><button class="btn btn-outline-info btn-sm w-100" onclick="appendScientificFunction('sqrt(')">√</button></div>
                                <div class="col-2"><button class="btn btn-outline-info btn-sm w-100" onclick="appendScientificConstant('π')">π</button></div>
                                <div class="col-2"><button class="btn btn-outline-info btn-sm w-100" onclick="appendScientificConstant('e')">e</button></div>
                            </div>
                            <div class="row g-1 mt-1">
                                <div class="col-2"><button class="btn btn-outline-dark btn-sm w-100" onclick="appendToScientificDisplay('1')">1</button></div>
                                <div class="col-2"><button class="btn btn-outline-dark btn-sm w-100" onclick="appendToScientificDisplay('2')">2</button></div>
                                <div class="col-2"><button class="btn btn-outline-dark btn-sm w-100" onclick="appendToScientificDisplay('3')">3</button></div>
                                <div class="col-2"><button class="btn btn-outline-primary btn-sm w-100" onclick="appendToScientificDisplay('+')">+</button></div>
                                <div class="col-2"><button class="btn btn-outline-primary btn-sm w-100" onclick="appendToScientificDisplay('-')">-</button></div>
                                <div class="col-2"><button class="btn btn-outline-primary btn-sm w-100" onclick="appendToScientificDisplay('*')">×</button></div>
                            </div>
                            <div class="row g-1 mt-1">
                                <div class="col-4"><button class="btn btn-outline-dark btn-sm w-100" onclick="appendToScientificDisplay('0')">0</button></div>
                                <div class="col-2"><button class="btn btn-outline-dark btn-sm w-100" onclick="appendToScientificDisplay('.')">.</button></div>
                                <div class="col-2"><button class="btn btn-outline-primary btn-sm w-100" onclick="appendToScientificDisplay('/')">/</button></div>
                                <div class="col-4"><button class="btn btn-success btn-sm w-100" onclick="calculateScientific()">=</button></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- 数学函数计算 -->
        <div class="col-lg-6 mb-4">
            <div class="card">
                <div class="card-header">
                    <h5><i class="bi bi-graph-up"></i> 数学函数</h5>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <label class="form-label">选择函数</label>
                        <select class="form-select" id="math-function-select">
                            <option value="factorial">阶乘 (n!)</option>
                            <option value="fibonacci">斐波那契数列 F(n)</option>
                            <option value="gcd">最大公约数 gcd(a,b)</option>
                            <option value="lcm">最小公倍数 lcm(a,b)</option>
                        </select>
                    </div>
                    <div id="math-function-inputs">
                        <div class="mb-3">
                            <label class="form-label">输入值 n</label>
                            <input type="number" class="form-control" id="math-input-n" min="0">
                        </div>
                    </div>
                    <button class="btn btn-primary" onclick="calculateMathFunction()">计算</button>
                    <div id="math-function-result" class="mt-3"></div>
                </div>
            </div>
        </div>

        <!-- 数学公式渲染 -->
        <div class="col-lg-6 mb-4">
            <div class="card">
                <div class="card-header">
                    <h5><i class="bi bi-textarea-t"></i> 数学公式渲染</h5>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <label class="form-label">输入LaTeX公式</label>
                        <textarea class="form-control" id="latex-input" rows="3" placeholder="例如: E = mc^2 或 \frac{a}{b}"></textarea>
                    </div>
                    <button class="btn btn-primary" onclick="renderLatex()">渲染公式</button>
                    <div class="mt-3">
                        <h6>渲染结果:</h6>
                        <div id="latex-output" class="border p-3 bg-light mathjax" style="min-height: 60px;">
                            <!-- 公式将在这里显示 -->
                        </div>
                    </div>
                    <div class="mt-3">
                        <h6>常用公式示例:</h6>
                        <div class="d-flex flex-wrap gap-2">
                            <button class="btn btn-outline-secondary btn-sm" onclick="insertLatexExample('E = mc^2')">质能方程</button>
                            <button class="btn btn-outline-secondary btn-sm" onclick="insertLatexExample('\\frac{a}{b}')">分数</button>
                            <button class="btn btn-outline-secondary btn-sm" onclick="insertLatexExample('\\sqrt{x}')">根号</button>
                            <button class="btn btn-outline-secondary btn-sm" onclick="insertLatexExample('x^2')">平方</button>
                            <button class="btn btn-outline-secondary btn-sm" onclick="insertLatexExample('\\sum_{i=1}^{n} x_i')">求和</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
