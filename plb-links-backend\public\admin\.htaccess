# PLB-Links 管理后台访问配置

RewriteEngine On

# 设置默认文档
DirectoryIndex index.php

# 处理静态资源
RewriteCond %{REQUEST_FILENAME} -f
RewriteRule \.(css|js|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ - [L]

# 处理admin子路径，重定向到index.php
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d
RewriteRule ^(.*)$ index.php?path=$1 [L,QSA]

# 安全设置：禁止直接访问敏感文件
<Files "*.conf">
    Require all denied
</Files>

<Files "*.log">
    Require all denied
</Files>

# 禁止目录浏览
Options -Indexes

# 设置文件类型
<IfModule mod_mime.c>
    AddType application/json .json
    AddType text/css .css
    AddType application/javascript .js
</IfModule>

# 安全头部
<IfModule mod_headers.c>
    Header always set X-Content-Type-Options nosniff
    Header always set X-Frame-Options SAMEORIGIN
    Header always set X-XSS-Protection "1; mode=block"
    Header always set Referrer-Policy "strict-origin-when-cross-origin"
</IfModule>
