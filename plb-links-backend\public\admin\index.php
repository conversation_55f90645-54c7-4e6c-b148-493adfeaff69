<?php
/**
 * PLB-Links 管理后台入口文件
 * 处理所有管理后台请求
 */

// 开启错误报告
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

// 启动会话
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

// 设置管理员权限（临时，实际应该有登录验证）
$_SESSION['admin_id'] = 1;
$_SESSION['user_id'] = 1;
$_SESSION['is_admin'] = 1;
$_SESSION['username'] = 'admin';
$_SESSION['admin_logged_in'] = true;

// 引入必要的文件
require_once dirname(__DIR__, 2) . '/src/Helpers/common.php';

// 获取请求的路径
$path = $_GET['path'] ?? '';
$requestUri = $_SERVER['REQUEST_URI'] ?? '';

// 解析路径
if (empty($path)) {
    // 从REQUEST_URI中提取路径
    $parsedUrl = parse_url($requestUri);
    $pathInfo = $parsedUrl['path'] ?? '';
    
    if ($pathInfo === '/admin' || $pathInfo === '/admin/') {
        $path = 'dashboard';
    } else {
        $path = str_replace('/admin/', '', $pathInfo);
        $path = trim($path, '/');
    }
}

// 如果路径为空，默认到dashboard
if (empty($path)) {
    $path = 'dashboard';
}

// 定义路由映射
$routes = [
    'dashboard' => '/Views/admin/dashboard/index.php',
    'coins' => '/Views/admin/coins/index.php',
    'user' => '/Views/admin/user/index.php',
    'member' => '/Views/admin/member/index.php',
    'payment' => '/Views/admin/payment/complete.php',
    'tag' => '/Views/admin/tag/index.php',
    'cors' => '/Views/admin/cors/index.php',
    'tenants' => '/Views/admin/tenants/index.php',
    'logs' => '/Views/admin/logs/index.php',
    'goods' => '/Views/admin/goods/index.php',
    'ai' => '/Views/admin/ai/index.php',
    'system' => '/Views/admin/system/index.php',
];

// 处理特殊路径（如login）
if ($path === 'login') {
    // 显示登录页面或重定向到dashboard
    header('Location: /admin/index.php?path=dashboard');
    exit;
}

// 查找对应的视图文件
$viewFile = null;
if (isset($routes[$path])) {
    $viewFile = dirname(__DIR__, 2) . $routes[$path];
} else {
    // 尝试直接匹配文件
    $viewFile = dirname(__DIR__, 2) . '/Views/admin/' . $path . '/index.php';
}

// 检查文件是否存在
if ($viewFile && file_exists($viewFile)) {
    try {
        // 设置页面变量
        $title = 'PLB-Links 管理后台';
        $pageTitle = ucfirst($path);
        $activeMenu = $path;
        
        // 包含视图文件
        require_once $viewFile;
        
    } catch (Exception $e) {
        // 显示错误页面
        showErrorPage('页面加载错误', $e->getMessage(), $e->getFile(), $e->getLine());
    }
} else {
    // 显示404页面
    show404Page($path, $viewFile);
}

/**
 * 显示404错误页面
 */
function show404Page($path, $viewFile) {
    http_response_code(404);
    ?>
    <!DOCTYPE html>
    <html lang="zh-CN">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>页面未找到 - PLB-Links 管理后台</title>
        <style>
            body {
                font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
                background: #f8f9fa;
                margin: 0;
                padding: 40px 20px;
                color: #333;
            }
            .container {
                max-width: 600px;
                margin: 0 auto;
                background: white;
                padding: 40px;
                border-radius: 10px;
                box-shadow: 0 2px 10px rgba(0,0,0,0.1);
                text-align: center;
            }
            h1 {
                color: #dc3545;
                margin-bottom: 20px;
            }
            .error-code {
                font-size: 4rem;
                font-weight: bold;
                color: #6c757d;
                margin-bottom: 20px;
            }
            .btn {
                display: inline-block;
                padding: 10px 20px;
                background: #007bff;
                color: white;
                text-decoration: none;
                border-radius: 5px;
                margin: 10px;
            }
            .btn:hover {
                background: #0056b3;
            }
            .debug-info {
                background: #f8f9fa;
                padding: 20px;
                border-radius: 5px;
                margin-top: 20px;
                text-align: left;
                font-family: monospace;
                font-size: 0.9rem;
            }
        </style>
    </head>
    <body>
        <div class="container">
            <div class="error-code">404</div>
            <h1>页面未找到</h1>
            <p>抱歉，您访问的管理页面不存在。</p>
            
            <a href="/admin" class="btn">返回管理首页</a>
            <a href="/admin/index.php?path=dashboard" class="btn">控制台</a>
            <a href="/admin/index.php?path=coins" class="btn">开心币管理</a>
            
            <div class="debug-info">
                <strong>调试信息:</strong><br>
                请求路径: <?= htmlspecialchars($path) ?><br>
                查找文件: <?= htmlspecialchars($viewFile ?? 'N/A') ?><br>
                文件存在: <?= $viewFile && file_exists($viewFile) ? '是' : '否' ?>
            </div>
        </div>
    </body>
    </html>
    <?php
}

/**
 * 显示错误页面
 */
function showErrorPage($title, $message, $file = '', $line = '') {
    http_response_code(500);
    ?>
    <!DOCTYPE html>
    <html lang="zh-CN">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title><?= htmlspecialchars($title) ?> - PLB-Links 管理后台</title>
        <style>
            body {
                font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
                background: #f8f9fa;
                margin: 0;
                padding: 40px 20px;
                color: #333;
            }
            .container {
                max-width: 800px;
                margin: 0 auto;
                background: white;
                padding: 40px;
                border-radius: 10px;
                box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            }
            h1 {
                color: #dc3545;
                margin-bottom: 20px;
            }
            .btn {
                display: inline-block;
                padding: 10px 20px;
                background: #007bff;
                color: white;
                text-decoration: none;
                border-radius: 5px;
                margin: 10px 0;
            }
            .error-details {
                background: #f8f9fa;
                padding: 20px;
                border-radius: 5px;
                margin-top: 20px;
                font-family: monospace;
                font-size: 0.9rem;
            }
        </style>
    </head>
    <body>
        <div class="container">
            <h1><?= htmlspecialchars($title) ?></h1>
            <p><strong>错误信息:</strong> <?= htmlspecialchars($message) ?></p>
            
            <?php if ($file): ?>
            <div class="error-details">
                <strong>文件:</strong> <?= htmlspecialchars($file) ?><br>
                <?php if ($line): ?>
                <strong>行号:</strong> <?= htmlspecialchars($line) ?>
                <?php endif; ?>
            </div>
            <?php endif; ?>
            
            <a href="/admin" class="btn">返回管理首页</a>
        </div>
    </body>
    </html>
    <?php
}
?>
