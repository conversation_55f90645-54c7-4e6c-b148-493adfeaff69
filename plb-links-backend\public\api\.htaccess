# API路由配置
<IfModule mod_rewrite.c>
    RewriteEngine On
    RewriteBase /api/

    # 处理OPTIONS请求
    RewriteCond %{REQUEST_METHOD} OPTIONS
    RewriteRule ^(.*)$ $1 [R=200,L]

    # 如果请求的是实际文件或目录，则直接访问
    RewriteCond %{REQUEST_FILENAME} -f [OR]
    RewriteCond %{REQUEST_FILENAME} -d
    RewriteRule ^ - [L]

    # /v1/auth/* 统一转发到 v1/auth.php
    RewriteRule ^v1/auth(/.*)?$ v1/auth.php [L]

    # 对于v1/ai/generate-title的请求，直接使用PHP文件处理
    RewriteRule ^v1/ai/generate-title$ v1/ai/generate-title.php [L]

    # 其他API请求转发到index.php
    RewriteRule ^ ../index.php [L]
</IfModule>

# 设置默认字符集
AddDefaultCharset UTF-8

# 设置PHP编码
<IfModule mod_php7.c>
    php_value default_charset UTF-8
</IfModule>

<IfModule mod_php8.c>
    php_value default_charset UTF-8
</IfModule> 