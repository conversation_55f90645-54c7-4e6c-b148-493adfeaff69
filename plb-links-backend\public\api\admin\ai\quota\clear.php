<?php
/**
 * 清空所有用户的AI配额使用记录
 * 仅限管理员使用
 */

// 清除所有输出缓冲区
while (ob_get_level()) {
    ob_end_clean();
}

// 设置头信息
header('Content-Type: application/json; charset=utf-8');
header('Cache-Control: no-cache, no-store, must-revalidate');
header('Pragma: no-cache');
header('Expires: 0');

// 引入必要的文件
require_once __DIR__ . '/../../../../../config/config.php';
require_once __DIR__ . '/../../../../../src/Helpers/common.php';

try {
    // 检查是否登录
    if (!isUserLoggedIn()) {
        echo json_encode([
            'code' => 401,
            'message' => '请先登录'
        ]);
        exit;
    }
    
    // 获取当前用户ID
    $currentUserId = getCurrentUserId();
    
    // 创建数据库连接
    $config = require __DIR__ . '/../../../../../config/config.php';
    $dbConfig = $config['db'];
    $dsn = "mysql:host={$dbConfig['host']};port={$dbConfig['port']};dbname={$dbConfig['database']};charset={$dbConfig['charset']}";
    $db = new PDO($dsn, $dbConfig['username'], $dbConfig['password'], [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
        PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
        PDO::ATTR_EMULATE_PREPARES => false,
    ]);
    
    // 检查当前用户是否为管理员
    $adminQuery = "SELECT role FROM plb_links_users WHERE id = ?";
    $adminStmt = $db->prepare($adminQuery);
    $adminStmt->execute([$currentUserId]);
    $userRole = $adminStmt->fetchColumn();
    
    if ($userRole !== 'admin') {
        echo json_encode([
            'code' => 403,
            'message' => '只有管理员可以执行此操作'
        ]);
        exit;
    }
    
    // 清空AI调用统计记录
    $deleteQuery = "DELETE FROM plb_links_ai_call_stats";
    $deleteStmt = $db->prepare($deleteQuery);
    $deleteStmt->execute();
    
    $affectedRows = $deleteStmt->rowCount();
    
    // 记录操作日志
    error_log("管理员 $currentUserId 清空了所有用户的AI配额使用记录，删除了 $affectedRows 条记录");
    
    // 返回成功信息
    echo json_encode([
        'code' => 200,
        'message' => '所有用户的AI配额使用记录已清空',
        'data' => [
            'records_deleted' => $affectedRows
        ]
    ]);
    
} catch (Exception $e) {
    error_log("清空AI配额使用记录失败: " . $e->getMessage());
    echo json_encode([
        'code' => 500,
        'message' => '清空AI配额使用记录失败: ' . $e->getMessage()
    ]);
} 