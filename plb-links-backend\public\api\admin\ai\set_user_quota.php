<?php
/**
 * 设置用户自定义AI配额的API端点
 * 仅限管理员使用
 */

// 清除所有输出缓冲区
while (ob_get_level()) {
    ob_end_clean();
}

// 设置头信息
header('Content-Type: application/json; charset=utf-8');
header('Cache-Control: no-cache, no-store, must-revalidate');
header('Pragma: no-cache');
header('Expires: 0');

// 允许跨域请求
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT');
header('Access-Control-Allow-Headers: Content-Type');

// 处理预检请求
if ($_SERVER['REQUEST_METHOD'] == 'OPTIONS') {
    exit(0);
}

// 引入必要的文件
require_once __DIR__ . '/../../../../config/config.php';
require_once __DIR__ . '/../../../../src/Helpers/common.php';

// 开启session
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

try {
    // 检查请求方法
    if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
        echo json_encode([
            'success' => false,
            'message' => '不支持的请求方法'
        ]);
        exit;
    }
    
    // 获取POST数据
    $postData = json_decode(file_get_contents('php://input'), true);
    
    // 检查是否登录 - 开发模式可绕过
    $devMode = isset($_GET['dev_mode']) && $_GET['dev_mode'] == 1;
    
    if (!$devMode && !isUserLoggedIn()) {
        echo json_encode([
            'success' => false,
            'message' => '请先登录',
            'session_id' => session_id(),
            'session_status' => session_status()
        ]);
        exit;
    }
    
    // 获取当前用户ID
    $currentUserId = getCurrentUserId();
    if (!$devMode && !$currentUserId) {
        $currentUserId = 1; // 默认使用ID为1的用户（通常是管理员）
    }
    
    // 创建数据库连接
    $config = require __DIR__ . '/../../../../config/config.php';
    $dbConfig = $config['db'];
    $dsn = "mysql:host={$dbConfig['host']};port={$dbConfig['port']};dbname={$dbConfig['database']};charset={$dbConfig['charset']}";
    $db = new PDO($dsn, $dbConfig['username'], $dbConfig['password'], [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
        PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
        PDO::ATTR_EMULATE_PREPARES => false,
    ]);
    
    // 开发模式或管理员检查
    if (!$devMode) {
        $adminQuery = "SELECT role FROM plb_links_users WHERE id = ?";
        $adminStmt = $db->prepare($adminQuery);
        $adminStmt->execute([$currentUserId]);
        $userRole = $adminStmt->fetchColumn();
        
        if ($userRole !== 'admin') {
            echo json_encode([
                'success' => false,
                'message' => '只有管理员可以执行此操作'
            ]);
            exit;
        }
    }
    
    // 获取要设置配额的用户ID和配额值
    $userId = isset($postData['user_id']) ? intval($postData['user_id']) : null;
    $quotaValue = isset($postData['quota']) ? intval($postData['quota']) : null;
    
    if (!$userId || $quotaValue === null) {
        echo json_encode([
            'success' => false,
            'message' => '请提供有效的用户ID和配额值'
        ]);
        exit;
    }
    
    // 检查用户是否存在
    $userCheckQuery = "SELECT id, username, role FROM plb_links_users WHERE id = ?";
    $userCheckStmt = $db->prepare($userCheckQuery);
    $userCheckStmt->execute([$userId]);
    $user = $userCheckStmt->fetch();
    
    if (!$user) {
        echo json_encode([
            'success' => false,
            'message' => '用户不存在'
        ]);
        exit;
    }
    
    // 检查是否已存在自定义配额记录
    $checkQuery = "SELECT id FROM plb_links_user_custom_quotas WHERE user_id = ?";
    $checkStmt = $db->prepare($checkQuery);
    $checkStmt->execute([$userId]);
    $existingRecord = $checkStmt->fetch();
    
    // 创建表（如果不存在）
    $createTableQuery = "CREATE TABLE IF NOT EXISTS plb_links_user_custom_quotas (
        id INT AUTO_INCREMENT PRIMARY KEY,
        user_id INT NOT NULL UNIQUE,
        quota INT NOT NULL DEFAULT 0,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        FOREIGN KEY (user_id) REFERENCES plb_links_users(id) ON DELETE CASCADE
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;";
    $db->exec($createTableQuery);
    
    if ($existingRecord) {
        // 更新现有记录
        $updateQuery = "UPDATE plb_links_user_custom_quotas SET quota = ? WHERE user_id = ?";
        $updateStmt = $db->prepare($updateQuery);
        $updateStmt->execute([$quotaValue, $userId]);
        $message = '已更新用户自定义配额';
    } else {
        // 创建新记录
        $insertQuery = "INSERT INTO plb_links_user_custom_quotas (user_id, quota) VALUES (?, ?)";
        $insertStmt = $db->prepare($insertQuery);
        $insertStmt->execute([$userId, $quotaValue]);
        $message = '已设置用户自定义配额';
    }
    
    // 记录操作日志
    $operator = $devMode ? "开发模式" : "用户ID: $currentUserId";
    error_log("$operator 设置了用户ID: $userId 的自定义配额为: $quotaValue");
    
    // 返回成功信息
    echo json_encode([
        'success' => true,
        'message' => $message,
        'data' => [
            'user_id' => $userId,
            'username' => $user['username'],
            'role' => $user['role'],
            'quota' => $quotaValue
        ]
    ]);
    
} catch (Exception $e) {
    error_log("设置用户配额失败: " . $e->getMessage());
    echo json_encode([
        'success' => false,
        'message' => '设置用户配额失败: ' . $e->getMessage(),
        'error_trace' => $e->getTraceAsString()
    ]);
} 