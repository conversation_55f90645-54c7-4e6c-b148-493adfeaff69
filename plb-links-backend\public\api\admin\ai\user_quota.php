<?php
/**
 * 查询指定用户的AI配额使用情况
 * 仅限管理员使用
 */

// 清除所有输出缓冲区
while (ob_get_level()) {
    ob_end_clean();
}

// 设置头信息
header('Content-Type: application/json; charset=utf-8');
header('Cache-Control: no-cache, no-store, must-revalidate');
header('Pragma: no-cache');
header('Expires: 0');

// 允许跨域请求
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST');
header('Access-Control-Allow-Headers: Content-Type');

// 引入必要的文件
require_once __DIR__ . '/../../../../config/config.php';
require_once __DIR__ . '/../../../../src/Helpers/common.php';

// 开启session
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

try {
    // 检查是否登录 - 开发模式可绕过
    $devMode = isset($_GET['dev_mode']) && $_GET['dev_mode'] == 1;
    
    if (!$devMode && !isUserLoggedIn()) {
        echo json_encode([
            'success' => false,
            'message' => '请先登录',
            'session_id' => session_id(),
            'session_status' => session_status()
        ]);
        exit;
    }
    
    // 获取当前用户ID
    $currentUserId = getCurrentUserId();
    if (!$devMode && !$currentUserId) {
        $currentUserId = 1; // 默认使用ID为1的用户（通常是管理员）
    }
    
    // 创建数据库连接
    $config = require __DIR__ . '/../../../../config/config.php';
    $dbConfig = $config['db'];
    $dsn = "mysql:host={$dbConfig['host']};port={$dbConfig['port']};dbname={$dbConfig['database']};charset={$dbConfig['charset']}";
    $db = new PDO($dsn, $dbConfig['username'], $dbConfig['password'], [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
        PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
        PDO::ATTR_EMULATE_PREPARES => false,
    ]);
    
    // 开发模式或管理员检查
    if (!$devMode) {
        $adminQuery = "SELECT role FROM plb_links_users WHERE id = ?";
        $adminStmt = $db->prepare($adminQuery);
        $adminStmt->execute([$currentUserId]);
        $userRole = $adminStmt->fetchColumn();
        
        if ($userRole !== 'admin') {
            echo json_encode([
                'success' => false,
                'message' => '只有管理员可以执行此操作'
            ]);
            exit;
        }
    }
    
    // 获取要查询的用户ID
    $userId = isset($_GET['user_id']) ? intval($_GET['user_id']) : null;
    
    if (!$userId) {
        echo json_encode([
            'success' => false,
            'message' => '请提供有效的用户ID'
        ]);
        exit;
    }
    
    // 获取用户信息
    $userQuery = "SELECT id, username, email, role, created_at FROM plb_links_users WHERE id = ?";
    $userStmt = $db->prepare($userQuery);
    $userStmt->execute([$userId]);
    $user = $userStmt->fetch();
    
    if (!$user) {
        // 如果用户不存在，创建一个临时用户对象以显示基本信息
        $user = [
            'id' => $userId,
            'username' => '未知用户',
            'email' => '',
            'role' => 'user',
            'created_at' => null
        ];
    }
    
    // 获取AI设置
    $settingsQuery = "SELECT * FROM plb_links_ai_settings LIMIT 1";
    $settingsStmt = $db->prepare($settingsQuery);
    $settingsStmt->execute();
    $settings = $settingsStmt->fetch();
    
    // 检查用户是否有自定义配额
    $customQuotaQuery = "SELECT quota FROM plb_links_user_custom_quotas WHERE user_id = ?";
    $customQuotaStmt = $db->prepare($customQuotaQuery);
    $customQuotaStmt->execute([$userId]);
    $customQuota = $customQuotaStmt->fetchColumn();
    
    // 根据用户角色和自定义配额设置总配额
    if ($user['role'] === 'admin') {
        $totalQuota = $settings['admin_quota'] ?? 100;
    } else if ($customQuota !== false) {
        $totalQuota = (int)$customQuota;
    } else {
        $totalQuota = $settings['user_quota'] ?? 50;
    }
    
    // 标记是否为自定义配额
    $isCustomQuota = $customQuota !== false;
    
    // 获取今日用户的调用次数
    $today = date('Y-m-d');
    $usedQuery = "SELECT SUM(call_count) FROM plb_links_ai_call_stats WHERE stat_date = ? AND user_id = ?";
    $usedStmt = $db->prepare($usedQuery);
    $usedStmt->execute([$today, $userId]);
    $usedToday = (int)$usedStmt->fetchColumn();
    
    // 获取用户最近7天的使用记录
    $historyQuery = "SELECT stat_date as date, call_count as count 
                    FROM plb_links_ai_call_stats 
                    WHERE user_id = ? 
                    ORDER BY stat_date DESC 
                    LIMIT 7";
    $historyStmt = $db->prepare($historyQuery);
    $historyStmt->execute([$userId]);
    $history = $historyStmt->fetchAll();
    
    // 构建响应数据
    $responseData = [
        'success' => true,
        'data' => [
            'user' => $user,
            'quota' => [
                'total' => $totalQuota,
                'used_today' => $usedToday,
                'remaining' => max(0, $totalQuota - $usedToday),
                'is_custom' => $isCustomQuota,
                'custom_value' => $isCustomQuota ? (int)$customQuota : null,
                'history' => $history
            ]
        ]
    ];
    
    // 返回结果
    echo json_encode($responseData);
    
} catch (Exception $e) {
    error_log("查询用户配额失败: " . $e->getMessage());
    echo json_encode([
        'success' => false,
        'message' => '查询用户配额失败: ' . $e->getMessage(),
        'error_trace' => $e->getTraceAsString()
    ]);
} 