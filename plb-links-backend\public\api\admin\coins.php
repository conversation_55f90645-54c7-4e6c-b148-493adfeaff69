<?php
/**
 * 开心币管理API
 */

// 设置错误报告
error_reporting(E_ALL);
ini_set('display_errors', 0);

// 设置响应头
header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Origin, Content-Type, Authorization, X-Requested-With, Accept');

// 处理OPTIONS请求
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

try {
    // 加载数据库连接
    require_once dirname(__DIR__, 2) . '/src/Helpers/common.php';
    
    // 获取请求方法和路径
    $method = $_SERVER['REQUEST_METHOD'];
    $uri = $_SERVER['REQUEST_URI'];
    $path = parse_url($uri, PHP_URL_PATH);
    
    error_log("开心币API调用 - 方法: " . (string)$method . ", 路径: " . (string)$path);
    
    $db = get_db();
    
    // 确保开心币相关表存在
    $createPointsTableSql = "
    CREATE TABLE IF NOT EXISTS `plb_links_points` (
        `id` int(11) NOT NULL AUTO_INCREMENT,
        `user_id` int(11) NOT NULL,
        `balance` decimal(10,2) NOT NULL DEFAULT 0.00,
        `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
        `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        PRIMARY KEY (`id`),
        UNIQUE KEY `user_id` (`user_id`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
    ";

    $createBalanceRecordsTableSql = "
    CREATE TABLE IF NOT EXISTS `plb_links_balance_records` (
        `id` int(11) NOT NULL AUTO_INCREMENT,
        `user_id` int(11) NOT NULL,
        `amount` decimal(10,2) NOT NULL,
        `type` varchar(50) NOT NULL,
        `description` text,
        `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
        PRIMARY KEY (`id`),
        KEY `user_id` (`user_id`),
        KEY `type` (`type`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
    ";

    $db->exec($createPointsTableSql);
    $db->exec($createBalanceRecordsTableSql);
    
    // 处理GET请求 - 获取用户开心币余额
    if ($method === 'GET' && preg_match('/\/user\/(\d+)/', $path, $matches)) {
        $userId = (int)$matches[1];

        error_log("获取用户开心币余额，用户ID: " . (string)$userId);

        // 获取用户开心币余额（从plb_links_kaixinbi_accounts表）
        $stmt = $db->prepare("SELECT balance, total_earned, total_spent, status FROM plb_links_kaixinbi_accounts WHERE user_id = ?");
        $stmt->execute([$userId]);
        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        $balance = $result ? floatval($result['balance']) : 0;

        // 获取用户总获得开心币
        $stmt = $db->prepare("SELECT COALESCE(SUM(CASE WHEN amount > 0 THEN amount ELSE 0 END), 0) as total_earned FROM plb_links_balance_records WHERE user_id = ? AND type LIKE '%coin%'");
        $stmt->execute([$userId]);
        $totalEarned = floatval($stmt->fetchColumn() ?: 0);

        // 获取用户总消费开心币
        $stmt = $db->prepare("SELECT COALESCE(SUM(CASE WHEN amount < 0 THEN ABS(amount) ELSE 0 END), 0) as total_spent FROM plb_links_balance_records WHERE user_id = ? AND type LIKE '%coin%'");
        $stmt->execute([$userId]);
        $totalSpent = floatval($stmt->fetchColumn() ?: 0);

        // 获取最近的交易记录
        $stmt = $db->prepare("
            SELECT amount, type, description, created_at
            FROM plb_links_balance_records
            WHERE user_id = ? AND type LIKE '%coin%'
            ORDER BY created_at DESC
            LIMIT 10
        ");
        $stmt->execute([$userId]);
        $transactions = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        $response = [
            'success' => true,
            'data' => [
                'user_id' => $userId,
                'balance' => $balance,
                'total_earned' => $totalEarned,
                'total_spent' => $totalSpent,
                'transactions' => $transactions
            ],
            'message' => '获取开心币余额成功'
        ];
        
        echo json_encode($response, JSON_UNESCAPED_UNICODE);
        exit;
    }
    
    // 处理POST请求 - 调整开心币
    if ($method === 'POST' && strpos($path, '/adjust') !== false) {
        $input = json_decode(file_get_contents('php://input'), true);
        
        if (!$input) {
            throw new Exception('无效的请求数据');
        }
        
        $userId = (int)($input['user_id'] ?? 0);
        $type = trim($input['type'] ?? '');
        $amount = (float)($input['amount'] ?? 0);
        $reason = trim($input['reason'] ?? '');
        
        error_log("调整开心币 - 用户ID: " . (string)$userId . ", 类型: " . (string)$type . ", 数量: " . (string)$amount . ", 原因: " . (string)$reason);
        
        // 验证参数
        if (!$userId) {
            throw new Exception('用户ID无效');
        }
        
        if (!in_array($type, ['add', 'subtract', 'set'])) {
            throw new Exception('调整类型无效');
        }
        
        if ($amount <= 0) {
            throw new Exception('调整数量必须大于0');
        }
        
        if (empty($reason)) {
            throw new Exception('调整原因不能为空');
        }
        
        // 检查用户是否存在
        $stmt = $db->prepare("SELECT COUNT(*) FROM plb_links_users WHERE id = ?");
        $stmt->execute([$userId]);
        if ($stmt->fetchColumn() == 0) {
            throw new Exception('用户不存在');
        }
        
        // 获取当前开心币余额
        $stmt = $db->prepare("SELECT balance, total_earned, total_spent, status FROM plb_links_kaixinbi_accounts WHERE user_id = ?");
        $stmt->execute([$userId]);
        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        $currentBalance = $result ? floatval($result['balance']) : 0;
        
        // 计算新余额
        $newBalance = 0;

        switch ($type) {
            case 'add':
                $newBalance = $currentBalance + $amount;
                break;

            case 'subtract':
                if ($currentBalance < $amount) {
                    throw new Exception('余额不足，当前余额: ' . $currentBalance);
                }
                $newBalance = $currentBalance - $amount;
                break;

            case 'set':
                $newBalance = $amount;
                break;
        }

        // 开始事务
        $db->beginTransaction();

        try {
            // 更新或插入用户开心币账户余额
            $stmt = $db->prepare("
                INSERT INTO plb_links_kaixinbi_accounts (user_id, balance, created_at, updated_at)
                VALUES (?, ?, NOW(), NOW())
                ON DUPLICATE KEY UPDATE balance = ?, updated_at = NOW()
            ");
            $stmt->execute([$userId, $newBalance, $newBalance]);

            // 记录开心币交易
            $changeAmount = $newBalance - $currentBalance;
            $transactionNo = 'KXB' . date('YmdHis') . rand(1000, 9999);

            // 确定交易类型
            $transactionType = '';
            if ($type === 'add') {
                $transactionType = 'earn';
            } elseif ($type === 'subtract') {
                $transactionType = 'spend';
            } elseif ($type === 'set') {
                $transactionType = $changeAmount >= 0 ? 'earn' : 'spend';
            }

            $description = "管理员调整: {$reason} (操作类型: {$type}, 从 {$currentBalance} 调整到 {$newBalance})";

            // 插入开心币交易记录
            $stmt = $db->prepare("
                INSERT INTO plb_links_kaixinbi_transactions
                (user_id, transaction_no, type, amount, balance_before, balance_after, source, description, status, created_at)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, 'success', NOW())
            ");
            $stmt->execute([
                $userId,
                $transactionNo,
                $transactionType,
                abs($changeAmount), // 使用绝对值，类型字段已经表明增减
                $currentBalance,
                $newBalance,
                'admin_adjust',
                $description
            ]);

            // 更新账户统计信息
            if ($changeAmount > 0) {
                // 增加时更新total_earned
                $stmt = $db->prepare("UPDATE plb_links_kaixinbi_accounts SET total_earned = total_earned + ? WHERE user_id = ?");
                $stmt->execute([$changeAmount, $userId]);
            } elseif ($changeAmount < 0) {
                // 减少时更新total_spent
                $stmt = $db->prepare("UPDATE plb_links_kaixinbi_accounts SET total_spent = total_spent + ? WHERE user_id = ?");
                $stmt->execute([abs($changeAmount), $userId]);
            }

            // 提交事务
            $db->commit();

        } catch (Exception $e) {
            // 回滚事务
            $db->rollback();
            throw $e;
        }
        
        $response = [
            'success' => true,
            'data' => [
                'user_id' => $userId,
                'old_balance' => $currentBalance,
                'new_balance' => $newBalance,
                'change' => $newBalance - $currentBalance,
                'type' => $type,
                'reason' => $reason
            ],
            'message' => '开心币调整成功'
        ];
        
        error_log("开心币调整成功: " . json_encode($response['data'], JSON_UNESCAPED_UNICODE));
        
        echo json_encode($response, JSON_UNESCAPED_UNICODE);
        exit;
    }
    
    // 处理GET请求 - 获取开心币交易记录
    if ($method === 'GET' && strpos($path, '/transactions') !== false) {
        $userId = (int)($_GET['user_id'] ?? 0);
        $page = max(1, (int)($_GET['page'] ?? 1));
        $pageSize = max(1, min(100, (int)($_GET['pageSize'] ?? 20)));
        $offset = ($page - 1) * $pageSize;
        
        if (!$userId) {
            throw new Exception('用户ID无效');
        }
        
        // 获取总数
        $stmt = $db->prepare("SELECT COUNT(*) FROM plb_links_points WHERE user_id = ?");
        $stmt->execute([$userId]);
        $total = (int)$stmt->fetchColumn();
        
        // 获取交易记录
        $stmt = $db->prepare("
            SELECT id, points, type, description, created_at
            FROM plb_links_points 
            WHERE user_id = ?
            ORDER BY created_at DESC
            LIMIT $pageSize OFFSET $offset
        ");
        $stmt->execute([$userId]);
        $transactions = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        $response = [
            'success' => true,
            'data' => $transactions,
            'pagination' => [
                'page' => $page,
                'pageSize' => $pageSize,
                'total' => $total,
                'pageCount' => ceil($total / $pageSize)
            ]
        ];
        
        echo json_encode($response, JSON_UNESCAPED_UNICODE);
        exit;
    }
    
    // 未匹配的请求
    http_response_code(404);
    echo json_encode([
        'success' => false,
        'message' => '未找到请求的资源'
    ], JSON_UNESCAPED_UNICODE);
    
} catch (Exception $e) {
    error_log("开心币API错误: " . $e->getMessage() . " 文件: " . $e->getFile() . " 行号: " . $e->getLine());

    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => '服务器内部错误: ' . $e->getMessage()
    ], JSON_UNESCAPED_UNICODE);
}
?>
