<?php
/**
 * PLB-Links 仪表盘API
 * 提供仪表盘所需的数据统计
 */

// 引入必要文件
require_once '../../../src/Helpers/common.php';
require_once '../../../src/Models/Setting.php';
require_once '../../../src/Helpers/visits.php';

// 检查管理员登录状态
session_start();
if (!isset($_SESSION['admin_logged_in']) || $_SESSION['admin_logged_in'] !== true) {
    header('Content-Type: text/html; charset=utf-8');
    echo json_encode([
        'success' => false,
        'message' => '未登录或会话已过期',
        'code' => 403
    ]);
    exit;
}

// 设置响应头
header('Content-Type: text/html; charset=utf-8');

// 确定请求的API类型
$requestPath = $_SERVER['PATH_INFO'] ?? $_SERVER['REQUEST_URI'] ?? '';
$requestType = '';

// 记录请求信息以便调试
error_log("Dashboard API请求路径: " . $requestPath);

// 兼容两种URL模式
if (strpos($requestPath, '/stats') !== false || strpos($requestPath, 'dashboard/stats') !== false) {
    $requestType = 'stats';
} elseif (strpos($requestPath, '/live-stats') !== false || strpos($requestPath, 'dashboard/live-stats') !== false) {
    $requestType = 'live-stats';
} else {
    $requestType = 'stats'; // 默认类型
}

error_log("API请求类型: " . $requestType);

// 初始化模型
$settingModel = new App\Models\Setting();
$db = get_db();

// 处理统计数据API
if ($requestType == 'stats') {
    try {
        error_log("开始处理统计数据API请求");
        
        // 获取基本统计数据
        $dailyViews = (int)$settingModel->getSetting('daily_views', 0);
        error_log("今日访问量设置值：" . $dailyViews);
        
        // 如果今日访问量为0，尝试从表中计算
        if ($dailyViews == 0) {
            try {
                $today = date('Y-m-d');
                $stmt = $db->prepare("
                    SELECT COUNT(*) FROM plb_links_visit_logs 
                    WHERE DATE(visit_time) = ?
                ");
                $stmt->execute([$today]);
                $dailyViews = (int)$stmt->fetchColumn();
                error_log("从表中计算的今日访问量：" . $dailyViews);
                
                // 更新设置
                $settingModel->updateSetting('daily_views', $dailyViews);
            } catch (Exception $e) {
                error_log("计算今日访问量失败: " . $e->getMessage());
            }
        }
        
        // 获取趋势数据 - 最近7天的访问统计
        $trend = getDailyVisitTrend(7);
        error_log("趋势数据：" . json_encode($trend));
        
        // 获取文件类型分布
        $fileTypes = getFileTypeDistribution();
        error_log("文件类型分布：" . json_encode($fileTypes));
        
        // 获取新增内容统计
        $newContent = getNewContentStats();
        error_log("新增内容统计：" . json_encode($newContent));
        
        // 获取访问统计
        $totalVisits = getTotalVisits();
        $uniqueVisitors = getUniqueVisitors();
        
        // 返回所有数据
        $responseData = [
            'success' => true,
            'data' => [
                'today_visits' => $dailyViews,
                'trend' => $trend,
                'file_types' => $fileTypes,
                'new_content' => $newContent,
                'visits' => [
                    'total' => $totalVisits,
                    'unique' => $uniqueVisitors
                ]
            ]
        ];
        
        error_log("返回的响应数据：" . json_encode($responseData));
        echo json_encode($responseData);
    } catch (Exception $e) {
        $errorMsg = '获取统计数据失败: ' . $e->getMessage();
        error_log($errorMsg);
        echo json_encode([
            'success' => false,
            'message' => $errorMsg,
            'code' => 500
        ]);
    }
}

// 处理实时统计数据API
elseif ($requestType == 'live-stats') {
    try {
        // 获取最近的访问记录
        $recentVisits = getRecentVisits(10);
        
        // 返回实时数据
        echo json_encode([
            'success' => true,
            'data' => [
                'recent_visits' => $recentVisits
            ]
        ]);
    } catch (Exception $e) {
        echo json_encode([
            'success' => false,
            'message' => '获取实时数据失败: ' . $e->getMessage(),
            'code' => 500
        ]);
    }
}

/**
 * 获取日访问趋势
 * @param int $days 天数
 * @return array 趋势数据
 */
function getDailyVisitTrend($days = 7) {
    $db = get_db();
    $trend = [
        'labels' => [],
        'data' => []
    ];
    
    try {
        // 记录当前日期以便调试
        $currentDate = date('Y-m-d');
        error_log("当前系统日期: " . $currentDate);
        
        // 确保表存在
        $result = $db->query("SHOW TABLES LIKE 'plb_links_visit_logs'");
        if ($result->rowCount() == 0) {
            error_log("访问日志表不存在");
            // 如果表不存在，返回空数据
            for ($i = $days - 1; $i >= 0; $i--) {
                $date = date('m-d', strtotime("-$i days"));
                $trend['labels'][] = $date;
                $trend['data'][] = 0;
            }
            return $trend;
        }
        
        // 先检查表中是否有数据
        $countStmt = $db->query("SELECT COUNT(*) FROM plb_links_visit_logs");
        $totalRecords = (int)$countStmt->fetchColumn();
        error_log("访问日志表总记录数: " . $totalRecords);
        
        if ($totalRecords == 0) {
            error_log("访问日志表为空");
            // 如果表为空，返回空数据
            for ($i = $days - 1; $i >= 0; $i--) {
                $date = date('m-d', strtotime("-$i days"));
                $trend['labels'][] = $date;
                $trend['data'][] = 0;
            }
            return $trend;
        }
        
        // 查询记录的日期范围
        $rangeStmt = $db->query("SELECT MIN(visit_time) as min_date, MAX(visit_time) as max_date FROM plb_links_visit_logs");
        $dateRange = $rangeStmt->fetch(PDO::FETCH_ASSOC);
        error_log("访问记录日期范围: " . json_encode($dateRange));
        
        // 查询最近几天的访问量 - 不考虑具体日期，直接取最近的记录
        $stmt = $db->prepare("
            SELECT 
                DATE(visit_time) as visit_date,
                COUNT(*) as visit_count
            FROM plb_links_visit_logs
            GROUP BY DATE(visit_time)
            ORDER BY visit_date DESC
            LIMIT ?
        ");
        $stmt->execute([$days]);
        $results = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        error_log("访问趋势查询结果: " . json_encode($results));
        
        // 如果没有结果，返回空数据
        if (empty($results)) {
            error_log("没有查询到访问记录");
            for ($i = $days - 1; $i >= 0; $i--) {
                $date = date('m-d', strtotime("-$i days"));
                $trend['labels'][] = $date;
                $trend['data'][] = 0;
            }
            return $trend;
        }
        
        // 将结果转换为日期->数量的映射
        $visitMap = [];
        foreach ($results as $row) {
            $visitMap[$row['visit_date']] = (int)$row['visit_count'];
        }
        
        error_log("访问记录映射: " . json_encode($visitMap));
        
        // 获取最近的日期并基于此生成图表数据
        $datesInDb = array_keys($visitMap);
        sort($datesInDb); // 按日期升序排序
        
        // 填充最近N天的数据
        for ($i = 0; $i < min($days, count($datesInDb)); $i++) {
            $date = $datesInDb[$i] ?? date('Y-m-d', strtotime("-$i days"));
            $shortDate = date('m-d', strtotime($date));
            
            $trend['labels'][] = $shortDate;
            $trend['data'][] = isset($visitMap[$date]) ? (int)$visitMap[$date] : 0;
        }
        
        return $trend;
    } catch (Exception $e) {
        error_log("获取访问趋势失败: " . $e->getMessage());
        
        // 返回空数据
        for ($i = $days - 1; $i >= 0; $i--) {
            $date = date('m-d', strtotime("-$i days"));
            $trend['labels'][] = $date;
            $trend['data'][] = 0;
        }
        return $trend;
    }
}

/**
 * 获取文件类型分布
 * @return array 文件类型分布数据
 */
function getFileTypeDistribution() {
    $db = get_db();
    
    try {
        // 获取各文件类型数量
        $fileTypes = [
            'video' => 0,
            'image' => 0,
            'audio' => 0,
            'article' => 0,
            'goods' => 0
        ];
        
        // 检查并查询媒体文件数量
        $result = $db->query("SHOW TABLES LIKE 'plb_links_media'");
        if ($result->rowCount() > 0) {
            $stmt = $db->prepare("SELECT COUNT(*) FROM plb_links_media WHERE media_type = 'video'");
            $stmt->execute();
            $fileTypes['video'] = (int)$stmt->fetchColumn();

            $stmt = $db->prepare("SELECT COUNT(*) FROM plb_links_media WHERE media_type = 'image'");
            $stmt->execute();
            $fileTypes['image'] = (int)$stmt->fetchColumn();

            $stmt = $db->prepare("SELECT COUNT(*) FROM plb_links_media WHERE media_type = 'audio'");
            $stmt->execute();
            $fileTypes['audio'] = (int)$stmt->fetchColumn();
        }
        
        // 检查并查询文章数量
        $result = $db->query("SHOW TABLES LIKE 'plb_links_articles'");
        if ($result->rowCount() > 0) {
            $stmt = $db->prepare("SELECT COUNT(*) FROM plb_links_articles");
            $stmt->execute();
            $fileTypes['article'] = (int)$stmt->fetchColumn();
        }
        
        // 检查并查询商品数量
        $result = $db->query("SHOW TABLES LIKE 'plb_links_goods'");
        if ($result->rowCount() > 0) {
            $stmt = $db->prepare("SELECT COUNT(*) FROM plb_links_goods");
            $stmt->execute();
            $fileTypes['goods'] = (int)$stmt->fetchColumn();
        }
        
        return $fileTypes;
    } catch (Exception $e) {
        error_log("获取文件类型分布失败: " . $e->getMessage());
        return [
            'video' => 0,
            'image' => 0,
            'audio' => 0,
            'article' => 0,
            'goods' => 0
        ];
    }
}

/**
 * 获取新增内容统计
 * @return array 新增内容统计
 */
function getNewContentStats() {
    $db = get_db();
    
    try {
        $stats = [
            'files' => 0,
            'users' => 0,
            'articles' => 0,
            'goods' => 0
        ];
        
        // 最近30天
        $lastMonth = date('Y-m-d', strtotime('-30 days'));
        
        // 新增媒体文件
        $result = $db->query("SHOW TABLES LIKE 'plb_links_media'");
        if ($result->rowCount() > 0) {
            $stmt = $db->prepare("SELECT COUNT(*) FROM plb_links_media WHERE created_at >= ?");
            $stmt->execute([$lastMonth]);
            $stats['files'] = (int)$stmt->fetchColumn();
        }
        
        // 新增用户
        $result = $db->query("SHOW TABLES LIKE 'plb_links_users'");
        if ($result->rowCount() > 0) {
            $stmt = $db->prepare("SELECT COUNT(*) FROM plb_links_users WHERE created_at >= ?");
            $stmt->execute([$lastMonth]);
            $stats['users'] = (int)$stmt->fetchColumn();
        }
        
        // 新增文章
        $result = $db->query("SHOW TABLES LIKE 'plb_links_articles'");
        if ($result->rowCount() > 0) {
            $stmt = $db->prepare("SELECT COUNT(*) FROM plb_links_articles WHERE created_at >= ?");
            $stmt->execute([$lastMonth]);
            $stats['articles'] = (int)$stmt->fetchColumn();
        }
        
        // 新增商品
        $result = $db->query("SHOW TABLES LIKE 'plb_links_goods'");
        if ($result->rowCount() > 0) {
            $stmt = $db->prepare("SELECT COUNT(*) FROM plb_links_goods WHERE created_at >= ?");
            $stmt->execute([$lastMonth]);
            $stats['goods'] = (int)$stmt->fetchColumn();
        }
        
        return $stats;
    } catch (Exception $e) {
        error_log("获取新增内容统计失败: " . $e->getMessage());
        return [
            'files' => 0,
            'users' => 0,
            'articles' => 0,
            'goods' => 0
        ];
    }
}

/**
 * 获取总访问量
 * @return int 总访问量
 */
function getTotalVisits() {
    $db = get_db();
    
    try {
        $result = $db->query("SHOW TABLES LIKE 'plb_links_visit_logs'");
        if ($result->rowCount() == 0) {
            error_log("获取总访问量：表不存在");
            return 0;
        }
        
        $stmt = $db->query("SELECT COUNT(*) FROM plb_links_visit_logs");
        $count = (int)$stmt->fetchColumn();
        error_log("获取总访问量：" . $count);
        return $count;
    } catch (Exception $e) {
        error_log("获取总访问量失败: " . $e->getMessage());
        return 0;
    }
}

/**
 * 获取独立访客数量
 * @return int 独立访客数量
 */
function getUniqueVisitors() {
    $db = get_db();
    
    try {
        $result = $db->query("SHOW TABLES LIKE 'plb_links_visit_logs'");
        if ($result->rowCount() == 0) {
            return 0;
        }
        
        $stmt = $db->query("SELECT COUNT(DISTINCT ip_address) FROM plb_links_visit_logs");
        return (int)$stmt->fetchColumn();
    } catch (Exception $e) {
        error_log("获取独立访客数量失败: " . $e->getMessage());
        return 0;
    }
}

/**
 * 获取最近访问记录
 * @param int $limit 限制数量
 * @return array 访问记录列表
 */
function getRecentVisits($limit = 10) {
    $db = get_db();
    
    try {
        $result = $db->query("SHOW TABLES LIKE 'plb_links_visit_logs'");
        if ($result->rowCount() == 0) {
            return [];
        }
        
        // 获取最近访问记录，包括用户名
        $stmt = $db->prepare("
            SELECT 
                v.*,
                u.username,
                CASE 
                    WHEN v.file_type = 'video' THEN '视频'
                    WHEN v.file_type = 'image' THEN '图片'
                    WHEN v.file_type = 'audio' THEN '音频'
                    WHEN v.file_type = 'article' THEN '文章'
                    WHEN v.file_type = 'goods' THEN '商品'
                    ELSE '页面'
                END as file_type_name
            FROM plb_links_visit_logs v
            LEFT JOIN plb_links_users u ON v.user_id = u.id
            ORDER BY v.visit_time DESC
            LIMIT ?
        ");
        $stmt->execute([$limit]);
        
        $visits = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        // 格式化数据
        foreach ($visits as &$visit) {
            // 格式化时间
            $visit['visit_time'] = date('m-d H:i', strtotime($visit['visit_time']));
            
            // 如果页面URL太长，截断处理
            if (strlen($visit['page_url']) > 30) {
                $visit['page_url_short'] = substr($visit['page_url'], 0, 30) . '...';
            } else {
                $visit['page_url_short'] = $visit['page_url'];
            }
        }
        
        return $visits;
    } catch (Exception $e) {
        error_log("获取最近访问记录失败: " . $e->getMessage());
        return [];
    }
} 