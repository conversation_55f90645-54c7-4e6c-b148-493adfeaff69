<?php
/**
 * PLB-Links 日志API
 * 提供系统日志查询功能
 */

// 引入必要文件
require_once '../../../src/Helpers/common.php';

// 检查管理员登录状态
session_start();
if (!isset($_SESSION['user_id']) || !isset($_SESSION['is_admin']) || $_SESSION['is_admin'] != 1) {
    header('Content-Type: application/json; charset=utf-8');
    echo json_encode([
        'success' => false,
        'message' => '未登录或会话已过期',
        'code' => 403,
        'debug' => [
            'user_id' => $_SESSION['user_id'] ?? 'not_set',
            'is_admin' => $_SESSION['is_admin'] ?? 'not_set',
            'session_id' => session_id()
        ]
    ]);
    exit;
}

// 设置响应头
header('Content-Type: application/json; charset=utf-8');

try {
    $db = get_db();
    
    // 获取请求参数
    $page = isset($_GET['page']) ? max(1, (int)$_GET['page']) : 1;
    $perPage = isset($_GET['per_page']) ? max(1, min(100, (int)$_GET['per_page'])) : 10;
    $offset = ($page - 1) * $perPage;
    
    // 检查访问日志表是否存在
    $result = $db->query("SHOW TABLES LIKE 'plb_links_visit_logs'");
    if ($result->rowCount() == 0) {
        echo json_encode([
            'success' => true,
            'data' => [
                'logs' => [],
                'pagination' => [
                    'current_page' => 1,
                    'per_page' => $perPage,
                    'total' => 0,
                    'total_pages' => 0
                ]
            ]
        ]);
        exit;
    }
    
    // 获取总记录数
    $countStmt = $db->query("SELECT COUNT(*) FROM plb_links_visit_logs");
    $total = (int)$countStmt->fetchColumn();
    $totalPages = ceil($total / $perPage);
    
    // 获取日志数据 - 使用实际存在的字段
    $stmt = $db->prepare("
        SELECT
            v.id,
            v.visit_time,
            v.ip_address,
            v.page_url,
            v.view_type,
            v.file_type,
            v.referer,
            u.username,
            CASE
                WHEN v.view_type = 'page' THEN '页面访问'
                WHEN v.view_type = 'file' THEN '文件浏览'
                ELSE v.view_type
            END as view_type_name
        FROM plb_links_visit_logs v
        LEFT JOIN plb_links_users u ON v.user_id = u.id
        ORDER BY v.visit_time DESC
        LIMIT " . (int)$perPage . " OFFSET " . (int)$offset . "
    ");
    $stmt->execute();
    $logs = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // 初始化IP地址解析器
    $searcher = null;
    $ipDbPath = __DIR__ . '/../../src/Helpers/ip/ip2region.xdb';
    if (file_exists($ipDbPath)) {
        try {
            require_once __DIR__ . '/../../src/Helpers/ip/XdbSearcher.class.php';
            $searcher = \XdbSearcher::newWithFileOnly($ipDbPath);
        } catch (Exception $e) {
            error_log('IP解析器初始化失败: ' . $e->getMessage());
        }
    }

    // 格式化日志数据
    foreach ($logs as &$log) {
        // 确保所有字段都有值
        $log['username'] = $log['username'] ?: '游客';
        $log['details'] = $log['page_url'] ?: '-';
        $log['ip_address'] = $log['ip_address'] ?: '-';

        // 解析IP地址获取地理位置
        $location = '-';
        if (!empty($log['ip_address']) && $log['ip_address'] !== '-') {
            if ($searcher) {
                try {
                    $region = $searcher->search($log['ip_address']);
                    if ($region && $region !== '0|0|0|内网IP|内网IP') {
                        // 格式化地理位置信息
                        $parts = explode('|', $region);
                        $locationParts = array_filter($parts, function($part) {
                            return $part !== '0' && !empty($part);
                        });
                        $location = implode(' ', $locationParts);
                    }
                } catch (Exception $e) {
                    error_log('IP解析失败: ' . $e->getMessage());
                }
            }

            // 如果IP解析失败，使用简单的IP分类
            if ($location === '-') {
                $ip = $log['ip_address'];
                if ($ip === '127.0.0.1' || $ip === '::1') {
                    $location = '本地';
                } elseif (strpos($ip, '192.168.') === 0 || strpos($ip, '10.') === 0 || strpos($ip, '172.') === 0) {
                    $location = '内网';
                } else {
                    $location = '外网';
                }
            }
        }
        $log['location'] = $location;

        // 格式化时间
        $log['visit_time'] = date('Y-m-d H:i:s', strtotime($log['visit_time']));
    }
    
    // 返回数据
    echo json_encode([
        'success' => true,
        'data' => [
            'logs' => $logs,
            'pagination' => [
                'current_page' => $page,
                'per_page' => $perPage,
                'total' => $total,
                'total_pages' => $totalPages
            ]
        ]
    ]);
    
} catch (Exception $e) {
    error_log("日志API错误: " . $e->getMessage());
    echo json_encode([
        'success' => false,
        'message' => '获取日志失败: ' . $e->getMessage(),
        'code' => 500
    ]);
}
