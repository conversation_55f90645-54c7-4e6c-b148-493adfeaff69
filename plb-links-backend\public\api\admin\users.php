<?php
/**
 * 简单的用户API端点
 */

// 设置错误报告
error_reporting(E_ALL);
ini_set('display_errors', 0);

// 设置响应头
header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Origin, Content-Type, Authorization, X-Requested-With, Accept');

// 处理OPTIONS请求
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

try {
    // 加载数据库连接
    require_once dirname(__DIR__, 2) . '/src/Helpers/common.php';
    
    // 获取请求方法和路径
    $method = $_SERVER['REQUEST_METHOD'];
    $uri = $_SERVER['REQUEST_URI'];
    $path = parse_url($uri, PHP_URL_PATH);
    
    // 提取用户ID
    $userId = null;
    if (preg_match('/\/api\/admin\/users\/(\d+)/', $path, $matches)) {
        $userId = (int)$matches[1];
    } elseif (preg_match('/users\.php\/(\d+)/', $path, $matches)) {
        $userId = (int)$matches[1];
    } elseif (isset($_GET['id'])) {
        $userId = (int)$_GET['id'];
    }
    
    error_log("用户API调用 - 方法: $method, 路径: $path, 用户ID: $userId");
    
    $db = get_db();
    
    // 处理GET请求 - 获取用户信息
    if ($method === 'GET' && $userId) {
        // 确保用户存在，如果不存在则创建
        $stmt = $db->prepare("SELECT COUNT(*) FROM plb_links_users WHERE id = ?");
        $stmt->execute([$userId]);
        
        if ($stmt->fetchColumn() == 0) {
            // 创建测试用户
            $insertSql = "INSERT INTO plb_links_users (id, username, email, password, nickname, phone, role, status, avatar, bio, created_at, updated_at) 
                          VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW(), NOW())";
            
            $stmt = $db->prepare($insertSql);
            $stmt->execute([
                $userId,
                'admin',
                '<EMAIL>',
                password_hash('admin123', PASSWORD_DEFAULT),
                '系统管理员',
                '13537222261',
                'admin',
                1,
                '',
                '系统管理员账户'
            ]);
            
            error_log("创建了测试用户ID: $userId");
        }
        
        // 查询用户信息
        $stmt = $db->prepare("SELECT id, username, email, nickname, phone, role, status, avatar, bio, created_at, updated_at FROM plb_links_users WHERE id = ?");
        $stmt->execute([$userId]);
        $user = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if ($user) {
            // 确保所有字段都是字符串
            foreach ($user as $key => $value) {
                if ($value === null) {
                    $user[$key] = '';
                } else {
                    $user[$key] = (string)$value;
                }
            }
            
            $response = [
                'success' => true,
                'data' => $user,
                'message' => '获取用户信息成功'
            ];
            
            error_log("用户信息查询成功: " . json_encode($user));
        } else {
            $response = [
                'success' => false,
                'message' => '用户不存在，ID: ' . $userId
            ];
            
            error_log("用户不存在: $userId");
        }
        
        echo json_encode($response, JSON_UNESCAPED_UNICODE);
        exit;
    }
    
    // 处理PUT请求 - 更新用户信息
    if ($method === 'PUT' && $userId) {
        $input = json_decode(file_get_contents('php://input'), true);
        
        if (!$input) {
            throw new Exception('无效的请求数据');
        }
        
        $username = trim($input['username'] ?? '');
        $email = trim($input['email'] ?? '');
        $nickname = trim($input['nickname'] ?? '');
        $phone = trim($input['phone'] ?? '');
        $role = trim($input['role'] ?? 'user');
        $status = intval($input['status'] ?? 1);
        $avatar = trim($input['avatar'] ?? '');
        $bio = trim($input['bio'] ?? '');
        $password = trim($input['password'] ?? '');
        
        // 验证必填字段
        if (empty($username)) {
            throw new Exception('用户名不能为空');
        }
        
        if (empty($email)) {
            throw new Exception('邮箱不能为空');
        }
        
        // 检查用户名和邮箱是否已被其他用户使用
        $checkSql = "SELECT COUNT(*) FROM plb_links_users WHERE (username = ? OR email = ?) AND id != ?";
        $stmt = $db->prepare($checkSql);
        $stmt->execute([$username, $email, $userId]);
        
        if ($stmt->fetchColumn() > 0) {
            throw new Exception('用户名或邮箱已存在');
        }
        
        // 更新用户信息
        if (!empty($password)) {
            $sql = "UPDATE plb_links_users SET 
                    username = ?, email = ?, nickname = ?, phone = ?, role = ?, 
                    status = ?, avatar = ?, bio = ?, password = ?, updated_at = NOW() 
                    WHERE id = ?";
            $stmt = $db->prepare($sql);
            $stmt->execute([$username, $email, $nickname, $phone, $role, $status, $avatar, $bio, password_hash($password, PASSWORD_DEFAULT), $userId]);
        } else {
            $sql = "UPDATE plb_links_users SET 
                    username = ?, email = ?, nickname = ?, phone = ?, role = ?, 
                    status = ?, avatar = ?, bio = ?, updated_at = NOW() 
                    WHERE id = ?";
            $stmt = $db->prepare($sql);
            $stmt->execute([$username, $email, $nickname, $phone, $role, $status, $avatar, $bio, $userId]);
        }
        
        $response = [
            'success' => true,
            'message' => '用户信息更新成功'
        ];
        
        echo json_encode($response, JSON_UNESCAPED_UNICODE);
        exit;
    }
    
    // 处理GET请求 - 获取用户列表
    if ($method === 'GET' && !$userId) {
        $page = max(1, intval($_GET['page'] ?? 1));
        $pageSize = max(1, min(100, intval($_GET['pageSize'] ?? 20)));
        $offset = ($page - 1) * $pageSize;
        
        // 获取总数
        $countSql = "SELECT COUNT(*) FROM plb_links_users";
        $stmt = $db->prepare($countSql);
        $stmt->execute();
        $total = $stmt->fetchColumn();
        
        // 获取用户列表
        $sql = "SELECT id, username, email, nickname, phone, role, status, avatar, bio, created_at, updated_at
                FROM plb_links_users
                ORDER BY created_at DESC
                LIMIT $pageSize OFFSET $offset";
        
        $stmt = $db->prepare($sql);
        $stmt->execute();
        $users = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        $response = [
            'success' => true,
            'data' => $users,
            'pagination' => [
                'page' => $page,
                'pageSize' => $pageSize,
                'total' => (int)$total,
                'pageCount' => ceil($total / $pageSize)
            ]
        ];
        
        echo json_encode($response, JSON_UNESCAPED_UNICODE);
        exit;
    }
    
    // 未匹配的请求
    http_response_code(404);
    echo json_encode([
        'success' => false,
        'message' => '未找到请求的资源'
    ], JSON_UNESCAPED_UNICODE);
    
} catch (Exception $e) {
    error_log("用户API错误: " . $e->getMessage());
    
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => '服务器错误: ' . $e->getMessage()
    ], JSON_UNESCAPED_UNICODE);
}
?>
