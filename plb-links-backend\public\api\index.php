<?php
/**
 * API入口文件
 * 处理所有API请求
 */

// 设置错误处理
ini_set('display_errors', 0);
error_reporting(E_ALL);

// 设置异常处理器
set_exception_handler(function($exception) {
    $errorMessage = $exception->getMessage();
    error_log('API异常: ' . $errorMessage);
    
    // 记录堆栈跟踪
    error_log('堆栈跟踪: ' . $exception->getTraceAsString());
    
    // 返回JSON格式的错误
    header('Content-Type: application/json; charset=utf-8');
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => '服务器内部错误: ' . $errorMessage,
        'data' => null
    ], JSON_UNESCAPED_UNICODE);
    exit;
});

// 加载应用引导文件
$bootstrapFile = dirname(__DIR__) . '/src/bootstrap.php';
if (file_exists($bootstrapFile)) {
    require_once $bootstrapFile;
} else {
    throw new Exception('引导文件不存在');
}

// 处理API请求
$app = new \App\Core\App();
$app->run(); 