<?php
/**
 * 系统更新API - 直接文件版本
 */

// 设置响应头
header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET');
header('Access-Control-Allow-Headers: Content-Type');

// 错误处理
ini_set('display_errors', 0);
error_reporting(0);

try {
    // 使用MySQLi连接，兼容MySQL 5.7
    $configFile = dirname(__DIR__, 2) . '/config/config.php';
    $config = require $configFile;
    $dbConfig = $config['db'];

    // 创建mysqli连接
    $mysqli = new mysqli(
        $dbConfig['host'],
        $dbConfig['username'],
        $dbConfig['password'],
        $dbConfig['database'],
        $dbConfig['port']
    );

    if ($mysqli->connect_error) {
        throw new Exception('数据库连接失败: ' . $mysqli->connect_error);
    }

    // 设置字符集和SQL模式，兼容MySQL 5.7
    $mysqli->set_charset('utf8mb4');
    $mysqli->query("SET sql_mode = 'TRADITIONAL'");
    $mysqli->query("SET time_zone = '+00:00'");

    // 检查是否是获取单个记录的请求
    if (isset($_GET['id']) && is_numeric($_GET['id'])) {
        $id = intval($_GET['id']);
        $sql = "SELECT id, version, title, content, features, fixes, type, release_date, author, status
                FROM plb_links_system_updates
                WHERE id = ?
                LIMIT 1";
        $stmt = $mysqli->prepare($sql);
        $stmt->bind_param('i', $id);
        $stmt->execute();
        $result = $stmt->get_result();
        $update = $result->fetch_assoc();
        $stmt->close();
        
        if ($update) {
            $formattedUpdate = [
                'id' => intval($update['id']),
                'version' => $update['version'],
                'title' => $update['title'],
                'content' => $update['content'],
                'release_date' => $update['release_date'],
                'formatted_date' => date('Y-m-d', strtotime($update['release_date'])),
                'author' => $update['author'],
                'type' => intval($update['type']),
                'status' => intval($update['status'])
            ];
            
            // 处理类型标签
            switch ($update['type']) {
                case 0:
                    $formattedUpdate['type_label'] = '普通更新';
                    $formattedUpdate['type_class'] = 'normal';
                    break;
                case 1:
                    $formattedUpdate['type_label'] = '功能更新';
                    $formattedUpdate['type_class'] = 'feature';
                    break;
                case 2:
                    $formattedUpdate['type_label'] = '安全更新';
                    $formattedUpdate['type_class'] = 'security';
                    break;
                case 3:
                    $formattedUpdate['type_label'] = '紧急修复';
                    $formattedUpdate['type_class'] = 'hotfix';
                    break;
                default:
                    $formattedUpdate['type_label'] = '普通更新';
                    $formattedUpdate['type_class'] = 'normal';
            }
            
            // 解析功能列表
            if (!empty($update['features'])) {
                $features = json_decode($update['features'], true);
                $formattedUpdate['features'] = is_array($features) ? $features : [];
            } else {
                $formattedUpdate['features'] = [];
            }
            
            // 解析修复列表
            if (!empty($update['fixes'])) {
                $fixes = json_decode($update['fixes'], true);
                $formattedUpdate['fixes'] = is_array($fixes) ? $fixes : [];
            } else {
                $formattedUpdate['fixes'] = [];
            }
            
            // 输出JSON响应
            echo json_encode([
                'success' => true,
                'data' => $formattedUpdate,
            ], JSON_UNESCAPED_UNICODE);
        } else {
            echo json_encode([
                'success' => false,
                'message' => '未找到指定的系统更新记录',
                'data' => null
            ], JSON_UNESCAPED_UNICODE);
        }
        $mysqli->close();
        exit;
    }

    // 获取限制参数
    $limit = intval($_GET['limit'] ?? 5);
    if ($limit <= 0 || $limit > 50) {
        $limit = 5;
    }

    // 获取页码参数
    $page = intval($_GET['page'] ?? 1);
    if ($page <= 0) {
        $page = 1;
    }

    $offset = ($page - 1) * $limit;

    // 查询已发布的系统更新
    $sql = "SELECT id, version, title, content, features, fixes, type, release_date, author, status
            FROM plb_links_system_updates
            WHERE status = 1
            ORDER BY release_date DESC, id DESC
            LIMIT ? OFFSET ?";
    $stmt = $mysqli->prepare($sql);
    $stmt->bind_param('ii', $limit, $offset);
    $stmt->execute();
    $result = $stmt->get_result();
    $updates = [];
    while ($row = $result->fetch_assoc()) {
        $updates[] = $row;
    }
    $stmt->close();

    // 获取总记录数
    $countSql = "SELECT COUNT(*) as total FROM plb_links_system_updates WHERE status = 1";
    $countStmt = $mysqli->prepare($countSql);
    $countStmt->execute();
    $countResult = $countStmt->get_result();
    $countRow = $countResult->fetch_assoc();
    $totalCount = $countRow['total'];
    $countStmt->close();
    
    // 处理数据格式
    $formattedUpdates = [];
    foreach ($updates as $update) {
        $formattedUpdate = [
            'id' => intval($update['id']),
            'version' => $update['version'],
            'title' => $update['title'],
            'content' => $update['content'],
            'release_date' => $update['release_date'],
            'formatted_date' => date('Y-m-d', strtotime($update['release_date'])),
            'author' => $update['author'],
            'type' => intval($update['type']),
            'status' => intval($update['status'])
        ];
        
        // 处理类型标签
        switch ($update['type']) {
            case 0:
                $formattedUpdate['type_label'] = '普通更新';
                $formattedUpdate['type_class'] = 'normal';
                break;
            case 1:
                $formattedUpdate['type_label'] = '功能更新';
                $formattedUpdate['type_class'] = 'feature';
                break;
            case 2:
                $formattedUpdate['type_label'] = '安全更新';
                $formattedUpdate['type_class'] = 'security';
                break;
            case 3:
                $formattedUpdate['type_label'] = '紧急修复';
                $formattedUpdate['type_class'] = 'hotfix';
                break;
            default:
                $formattedUpdate['type_label'] = '普通更新';
                $formattedUpdate['type_class'] = 'normal';
        }
        
        // 解析功能列表
        if (!empty($update['features'])) {
            $features = json_decode($update['features'], true);
            $formattedUpdate['features'] = is_array($features) ? $features : [];
        } else {
            $formattedUpdate['features'] = [];
        }
        
        // 解析修复列表
        if (!empty($update['fixes'])) {
            $fixes = json_decode($update['fixes'], true);
            $formattedUpdate['fixes'] = is_array($fixes) ? $fixes : [];
        } else {
            $formattedUpdate['fixes'] = [];
        }
        
        $formattedUpdates[] = $formattedUpdate;
    }
    
    // 关闭数据库连接
    $mysqli->close();

    // 输出JSON响应
    echo json_encode([
        'success' => true,
        'data' => $formattedUpdates,
        'total' => intval($totalCount),
        'page' => $page,
        'limit' => $limit,
        'has_more' => ($offset + count($formattedUpdates)) < $totalCount
    ], JSON_UNESCAPED_UNICODE);

} catch (Exception $e) {
    // 关闭数据库连接（如果已建立）
    if (isset($mysqli) && $mysqli instanceof mysqli) {
        $mysqli->close();
    }

    // 记录错误日志
    error_log("系统更新API错误: " . $e->getMessage());

    // 输出错误响应
    echo json_encode([
        'success' => false,
        'message' => '获取系统更新失败',
        'data' => [],
        'error' => $e->getMessage()
    ], JSON_UNESCAPED_UNICODE);
}
?>