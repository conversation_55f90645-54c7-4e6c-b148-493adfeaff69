<?php
/**
 * 标题生成API独立实现
 * 作为临时修复方案
 */

// 设置错误报告
ini_set('display_errors', 0);
error_reporting(0);

// 设置UTF-8编码
header('Content-Type: application/json; charset=utf-8');
header('Cache-Control: no-cache, no-store, must-revalidate');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, X-Requested-With, Authorization, Accept');
header('Access-Control-Allow-Credentials: true');
header('Access-Control-Max-Age: 3600');

// 处理OPTIONS预检请求
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

// 清除所有输出缓冲区
while (ob_get_level()) {
    ob_end_clean();
}

// 安全获取请求头
function safe_get_headers() {
    if (function_exists('getallheaders')) {
        return getallheaders();
    }
    
    $headers = [];
    foreach ($_SERVER as $name => $value) {
        if (substr($name, 0, 5) === 'HTTP_') {
            $name = str_replace(' ', '-', ucwords(strtolower(str_replace('_', ' ', substr($name, 5)))));
            $headers[$name] = $value;
        }
    }
    return $headers;
}

// 记录请求信息到日志
$logDir = dirname(dirname(dirname(dirname(__DIR__)))) . '/logs';
if (!is_dir($logDir)) {
    mkdir($logDir, 0777, true);
}
$logFile = $logDir . '/api-debug.log';

$requestInfo = [
    'time' => date('Y-m-d H:i:s'),
    'url' => $_SERVER['REQUEST_URI'],
    'method' => $_SERVER['REQUEST_METHOD'],
    'headers' => safe_get_headers(),
    'get' => $_GET,
    'post' => $_POST,
    'raw' => file_get_contents('php://input')
];
file_put_contents($logFile, json_encode($requestInfo, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT) . "\n\n", FILE_APPEND);

try {
    // 获取请求参数
    $rawInput = file_get_contents('php://input');
    $data = json_decode($rawInput, true);
    
    // 如果JSON解析失败，尝试从POST参数获取
    if (json_last_error() !== JSON_ERROR_NONE) {
        $data = $_POST;
    }
    
    // 获取关键词参数
    $keywords = isset($data['keywords']) ? $data['keywords'] : null;
    $mediaType = isset($data['media_type']) ? $data['media_type'] : 'article';
    $maxLength = isset($data['max_length']) ? intval($data['max_length']) : 30;
    
    if (empty($keywords)) {
        echo json_encode([
            'success' => false,
            'message' => '请提供关键词'
        ], JSON_UNESCAPED_UNICODE);
        exit;
    }
    
    // 记录成功解析的关键词
    file_put_contents($logFile, "成功解析关键词: $keywords, 媒体类型: $mediaType\n\n", FILE_APPEND);
    
    // 根据媒体类型生成不同风格的标题
    $titles = [];
    
    switch ($mediaType) {
        case 'video':
            $titles = [
                "探索" . $keywords . "的视觉之旅",
                $keywords . "：从入门到精通的视频指南",
                "震撼视角：" . $keywords . "的全新解读",
                "深度剖析：" . $keywords . "背后的故事",
                $keywords . "视频精选：不容错过的精彩瞬间"
            ];
            break;
            
        case 'audio':
            $titles = [
                $keywords . "音频专辑：聆听的艺术",
                "声音之旅：探索" . $keywords . "的世界",
                $keywords . "：耳畔的美妙旋律",
                "音频解析：" . $keywords . "的深度探讨",
                "聆听" . $keywords . "：声音背后的故事"
            ];
            break;
            
        case 'image':
            $titles = [
                $keywords . "的视觉盛宴",
                "定格瞬间：" . $keywords . "的完美捕捉",
                "影像中的" . $keywords . "：视觉艺术展示",
                $keywords . "：一幅值得珍藏的画面",
                "镜头下的" . $keywords . "：独特视角"
            ];
            break;
            
        case 'article':
        default:
            $titles = [
                "深度解析：" . $keywords . "的全面指南",
                $keywords . "：你需要知道的一切",
                "探索" . $keywords . "的未知领域",
                "从零开始：" . $keywords . "完全入门",
                $keywords . "：趋势与发展前景分析"
            ];
            break;
    }
    
    // 确保标题不超过指定长度
    foreach ($titles as &$title) {
        if (mb_strlen($title, 'UTF-8') > $maxLength) {
            $title = mb_substr($title, 0, $maxLength, 'UTF-8');
        }
    }
    
    // 返回成功响应
    $response = [
        'success' => true,
        'data' => [
            'titles' => $titles,
            'quota_remaining' => 10
        ]
    ];
    
    echo json_encode($response, JSON_UNESCAPED_UNICODE);
    file_put_contents($logFile, "成功返回响应: " . json_encode($response, JSON_UNESCAPED_UNICODE) . "\n\n", FILE_APPEND);
    
} catch (Exception $e) {
    // 记录错误
    file_put_contents($logFile, "错误: " . $e->getMessage() . "\n" . $e->getTraceAsString() . "\n\n", FILE_APPEND);
    
    // 返回错误响应
    echo json_encode([
        'success' => false,
        'message' => '生成标题失败: ' . $e->getMessage()
    ], JSON_UNESCAPED_UNICODE);
} 