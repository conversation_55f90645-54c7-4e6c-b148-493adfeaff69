<?php
// 设置CORS头部
$allowedOrigins = [
    'http://localhost:3000',
    'http://localhost:8000',
    'http://localhost',
    'http://127.0.0.1:3000',
    'http://127.0.0.1:8000',
    'http://127.0.0.1',
    'https://links.51kxg.com',
    'https://server.51kxg.com'
];

// 获取请求来源
$origin = isset($_SERVER['HTTP_ORIGIN']) ? $_SERVER['HTTP_ORIGIN'] : '';

// 如果来源在允许列表中，或者是localhost
if (in_array($origin, $allowedOrigins) || strpos($origin, 'localhost') !== false) {
    header("Access-Control-Allow-Origin: $origin");
    // 允许带凭证的请求
    header('Access-Control-Allow-Credentials: true');
} else {
    // 默认使用第一个允许的源
    header("Access-Control-Allow-Origin: {$allowedOrigins[0]}");
    header('Access-Control-Allow-Credentials: true');
}

header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization, X-Requested-With, X-Client-App, X-Request-ID, Cache-Control');
header('Access-Control-Max-Age: 3600');

// 处理预检请求
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

$uri = $_SERVER['REQUEST_URI'];
$action = '';
if (preg_match('#/api/v1/auth/(register|login|forgot-password)#', $uri, $matches)) {
    $action = $matches[1];
}

$input = file_get_contents('php://input');
$data = json_decode($input, true);
if (!$data) $data = $_POST;

$response = ['success' => false, 'message' => '未知操作'];

if ($action === 'register' && $_SERVER['REQUEST_METHOD'] === 'POST') {
    if (!empty($data['username']) && !empty($data['password']) && !empty($data['email'])) {
        $response = [
            'success' => true,
            'message' => '注册成功',
            'user_id' => rand(1000,9999)
        ];
    } else {
        $response = [
            'success' => false,
            'message' => '用户名、密码、邮箱不能为空'
        ];
    }
} elseif ($action === 'login' && $_SERVER['REQUEST_METHOD'] === 'POST') {
    if (!empty($data['username']) && !empty($data['password'])) {
        $response = [
            'success' => true,
            'message' => '登录成功',
            'token' => md5($data['username'] . time()),
            'user' => [
                'id' => rand(1000,9999),
                'username' => $data['username'],
                'email' => $data['username'] . '@example.com'
            ]
        ];
    } else {
        $response = [
            'success' => false,
            'message' => '用户名或密码不能为空'
        ];
    }
} elseif ($action === 'forgot-password' && $_SERVER['REQUEST_METHOD'] === 'POST') {
    if (!empty($data['email'])) {
        $response = [
            'success' => true,
            'message' => '重置密码邮件已发送'
        ];
    } else {
        $response = [
            'success' => false,
            'message' => '邮箱不能为空'
        ];
    }
}

echo json_encode($response, JSON_UNESCAPED_UNICODE); 