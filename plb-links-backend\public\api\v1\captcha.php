<?php
/**
 * 独立验证码生成文件
 * 无需依赖框架，直接生成验证码图片
 */

// 加载CORS辅助函数
require_once dirname(__DIR__, 3) . '/src/Helpers/cors.php';

// 设置CORS头
set_cors_headers();

// 记录调试信息
error_log("验证码请求: 来源 = " . (isset($_SERVER['HTTP_ORIGIN']) ? $_SERVER['HTTP_ORIGIN'] : ''));
error_log("验证码请求头: " . json_encode(getallheaders()));

// 如果是OPTIONS请求，直接返回
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

// 确保会话已启动
if (session_status() == PHP_SESSION_NONE) {
    // 设置会话保存路径，确保存在
    $sessionDir = dirname(__DIR__, 3) . '/tmp/sessions';
    if (!is_dir($sessionDir)) {
        mkdir($sessionDir, 0777, true);
    }
    session_save_path($sessionDir);
    
    // 设置会话cookie参数
    session_set_cookie_params([
        'lifetime' => 3600,
        'path' => '/',
        'domain' => '',
        'secure' => isset($_SERVER['HTTPS']),
        'httponly' => true,
        'samesite' => 'Lax'
    ]);
    
    session_start();
}

// 记录会话信息
$sid = session_id();
error_log("验证码使用会话ID: $sid");
error_log('Captcha direct generation called');

// 获取验证码类型
$type = isset($_GET['type']) ? $_GET['type'] : 'login';

// 获取或生成验证码token
$captchaToken = isset($_GET['token']) ? $_GET['token'] : 'captcha_' . time() . '_' . mt_rand(1000, 9999);

// 验证码字符集 - 简化为更容易辨认的字符
$chars = '23456789ABCDEFGHJKLMNPQRSTUVWXYZ'; // 移除容易混淆的字符如0,1,O,I
$code = '';
for ($i = 0; $i < 4; $i++) { // 修改为4个字符的验证码
    $code .= $chars[mt_rand(0, strlen($chars) - 1)];
}

// 将验证码写入SESSION
$_SESSION['captcha'] = $code;
$_SESSION['captcha_lower'] = strtolower($code); // 添加小写版本便于不区分大小写验证
$_SESSION['captcha_time'] = time(); // 添加时间戳以便于验证是否过期
$_SESSION['captcha_token'] = $captchaToken; // 保存token与验证码的关系

// 记录日志
error_log("生成验证码: [$code], 小写: [" . strtolower($code) . "], 类型: [$type], 会话ID: [" . session_id() . "], Token: [$captchaToken]");

// 设置图片属性
$width = 120; // 设置更合适的宽度
$height = 46; // 保持与CSS高度一致
$im = imagecreatetruecolor($width, $height);

// 颜色定义 - 提高对比度
$bg_color = imagecolorallocate($im, 245, 245, 245); // 浅灰色背景
imagefilledrectangle($im, 0, 0, $width, $height, $bg_color);

// 尝试多个可能的字体路径
$font_paths = [
    dirname(__DIR__, 3) . '/public/assets/fonts/arial.ttf',
    dirname(__DIR__, 3) . '/assets/fonts/arial.ttf',
    dirname(__DIR__, 3) . '/public/assets/fonts/SourceHanSansCN-VF-2.otf'
];

$font_path = null;
foreach ($font_paths as $path) {
    if (file_exists($path)) {
        $font_path = $path;
        break;
    }
}

// 如果找不到字体，记录错误
if ($font_path === null) {
    error_log('验证码生成找不到字体文件，将使用内置字体');
}

// 绘制轻微干扰线 - 减少干扰度
for ($i = 0; $i < 2; $i++) {
    $line_color = imagecolorallocate($im, mt_rand(200, 220), mt_rand(200, 220), mt_rand(200, 220));
    imageline($im, mt_rand(0, $width), mt_rand(0, $height), mt_rand(0, $width), mt_rand(0, $height), $line_color);
}

// 绘制少量噪点
for ($i = 0; $i < 30; $i++) {
    $pixel_color = imagecolorallocate($im, mt_rand(180, 220), mt_rand(180, 220), mt_rand(180, 220));
    imagesetpixel($im, mt_rand(0, $width), mt_rand(0, $height), $pixel_color);
}

// 绘制验证码字符 - 提高可读性
$font_size = 22; // 增大字体
$text_color = imagecolorallocate($im, 30, 30, 30); // 深色文字颜色

// 每个字符单独绘制
$char_x = 15;
for ($i = 0; $i < strlen($code); $i++) {
    $char = $code[$i];
    $angle = mt_rand(-5, 5); // 减小旋转角度以提高可读性
    $char_y = $height/2 + 8; // 固定Y位置，提高可读性
    
    if ($font_path) {
        // 使用TrueType字体
        imagettftext($im, $font_size, $angle, $char_x, $char_y, $text_color, $font_path, $char);
    } else {
        // 回退到内置字体
        imagestring($im, 5, $char_x, $char_y - 15, $char, $text_color);
    }
    
    $char_x += 22; // 减小字符间距，使其更紧凑
}

// 设置验证码token在响应头中
header('X-Captcha-Token: ' . $captchaToken);
header('Access-Control-Expose-Headers: X-Captcha-Token'); // 允许前端获取此响应头

// 明确设置允许所有CORS请求头
$allowedOrigins = [
    'http://localhost:3000',
    'http://localhost:8000',
    'http://localhost',
    'http://127.0.0.1:3000',
    'http://127.0.0.1:8000',
    'https://links.51kxg.com',
];

// 获取请求来源
$origin = isset($_SERVER['HTTP_ORIGIN']) ? $_SERVER['HTTP_ORIGIN'] : '';
if (in_array($origin, $allowedOrigins)) {
    header("Access-Control-Allow-Origin: $origin");
    header('Access-Control-Allow-Credentials: true');
}

// 设置正确的HTTP头 - 确保明确指定内容类型为图片
header('Content-Type: image/png');
header('Cache-Control: no-store, no-cache, must-revalidate, max-age=0');
header('Pragma: no-cache');
header('Expires: Mon, 26 Jul 1997 05:00:00 GMT');

// 输出图像
imagepng($im);
imagedestroy($im);
exit; // 重要：确保不会输出其他内容 