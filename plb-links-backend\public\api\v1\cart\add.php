<?php
// 处理OPTIONS预检请求
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

// 启动会话以存储购物车数据
session_start();

// 获取请求体中的JSON数据
$requestBody = file_get_contents('php://input');
$requestData = json_decode($requestBody, true);

// 检查必要的参数
if (!isset($requestData['goods_id'])) {
    http_response_code(400);
    echo json_encode([
        'success' => false,
        'message' => '缺少商品ID参数',
    ]);
    exit;
}

// 获取商品数据
$goodsId = (int)$requestData['goods_id'];
$quantity = isset($requestData['quantity']) ? (int)$requestData['quantity'] : 1;
$variants = isset($requestData['variants']) ? $requestData['variants'] : [];

// 确保quantity至少为1
if ($quantity < 1) {
    $quantity = 1;
}

// 初始化购物车
if (!isset($_SESSION['cart']) || !is_array($_SESSION['cart'])) {
    $_SESSION['cart'] = [];
}

// 生成唯一的购物车项ID (商品ID + 变体组合)
$cartItemId = $goodsId;
if (!empty($variants)) {
    $variantStr = '';
    foreach ($variants as $key => $value) {
        $variantStr .= "_{$key}-{$value}";
    }
    $cartItemId = $goodsId . $variantStr;
}

// 检查购物车中是否已有此商品
$found = false;
foreach ($_SESSION['cart'] as $key => $item) {
    if (isset($item['cart_item_id']) && $item['cart_item_id'] === $cartItemId) {
        // 更新现有商品数量
        $_SESSION['cart'][$key]['quantity'] += $quantity;
        $found = true;
        break;
    }
}

// 如果没找到，添加新商品到购物车
if (!$found) {
    // 这里应该从数据库获取商品信息，这只是一个简单的模拟
    $_SESSION['cart'][] = [
        'cart_item_id' => $cartItemId,
        'goods_id' => $goodsId,
        'quantity' => $quantity,
        'variants' => $variants,
        'added_at' => time()
    ];
}

// 返回成功信息和更新后的购物车摘要
$totalItems = 0;
foreach ($_SESSION['cart'] as $item) {
    $totalItems += $item['quantity'];
}

echo json_encode([
    'success' => true,
    'message' => '成功添加到购物车',
    'data' => [
        'cart_count' => $totalItems,
        'added_quantity' => $quantity,
        'session_id' => session_id(),
    ],
    'time' => date('Y-m-d H:i:s'),
    'server' => $_SERVER['SERVER_NAME'] . ':' . $_SERVER['SERVER_PORT'],
]); 