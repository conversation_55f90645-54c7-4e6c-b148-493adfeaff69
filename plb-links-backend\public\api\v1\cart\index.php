<?php
// 使用系统的CORS助手函数
if (file_exists(dirname(__DIR__, 4) . '/src/Helpers/cors.php')) {
    require_once dirname(__DIR__, 4) . '/src/Helpers/cors.php';
    set_cors_headers();
    error_log('已使用系统CORS助手函数');
}

// 添加内容类型头
header('Content-Type: application/json; charset=utf-8');

// 记录请求信息
error_log('购物车API被访问: ' . $_SERVER['REQUEST_URI'] . ' | ' . date('Y-m-d H:i:s'));
error_log('请求头: ' . (isset($_SERVER['HTTP_ORIGIN']) ? $_SERVER['HTTP_ORIGIN'] : 'No origin') . ' | ' . 
          (isset($_SERVER['HTTP_REFERER']) ? $_SERVER['HTTP_REFERER'] : 'No referer'));

// 处理OPTIONS预检请求
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

// 启动会话以获取购物车数据
try {
  session_start();
  error_log('会话ID: ' . session_id());
} catch (Exception $e) {
  error_log('启动会话失败: ' . $e->getMessage());
  // 返回错误响应
  echo json_encode([
    'success' => false,
    'message' => '会话初始化失败',
    'debug' => $e->getMessage(),
    'time' => date('Y-m-d H:i:s')
  ]);
  exit;
}

// 输出完整的环境信息用于调试
error_log('Server info: ' . print_r($_SERVER, true));

// 初始化购物车数组
if (!isset($_SESSION['cart']) || !is_array($_SESSION['cart'])) {
    $_SESSION['cart'] = [];
    error_log('初始化新购物车');
}

// 输出购物车内容
echo json_encode([
    'success' => true,
    'data' => [
        'items' => $_SESSION['cart'],
        'session_id' => session_id(),
        'is_logged_in' => isset($_SESSION['user_id']),
        'total_items' => count($_SESSION['cart']),
        'timestamp' => time()
    ],
    'time' => date('Y-m-d H:i:s'),
    'server' => $_SERVER['SERVER_NAME'] . ':' . $_SERVER['SERVER_PORT'],
]); 