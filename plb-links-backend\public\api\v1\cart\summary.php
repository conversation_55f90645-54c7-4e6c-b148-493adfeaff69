<?php
// 设置允许所有源的CORS头
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');
header('Access-Control-Allow-Credentials: true');
header('Content-Type: application/json');

// 记录请求信息
error_log('购物车摘要API被访问: ' . $_SERVER['REQUEST_URI']);

// 处理OPTIONS预检请求
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

// 启动会话以获取购物车数据
session_start();

// 初始化购物车数量
$cartCount = 0;

// 检查会话中是否有购物车数据
if (isset($_SESSION['cart']) && is_array($_SESSION['cart'])) {
    foreach ($_SESSION['cart'] as $item) {
        $cartCount += isset($item['quantity']) ? (int)$item['quantity'] : 0;
    }
}

// 输出购物车摘要信息
echo json_encode([
    'success' => true,
    'data' => [
        'count' => $cartCount,
        'session_id' => session_id(),
        'is_logged_in' => isset($_SESSION['user_id'])
    ],
    'time' => date('Y-m-d H:i:s'),
    'server' => $_SERVER['SERVER_NAME'] . ':' . $_SERVER['SERVER_PORT'],
]); 