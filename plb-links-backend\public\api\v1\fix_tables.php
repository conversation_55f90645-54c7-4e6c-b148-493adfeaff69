<?php
// 设置执行时间限制
set_time_limit(300);

// 设置页面为纯文本输出
header('Content-Type: text/plain; charset=utf-8');

// 加载数据库配置
require_once __DIR__ . '/../../../config/config.php';
require_once __DIR__ . '/../../../src/Helpers/common.php';

// 获取数据库连接
$db = get_db();

// 显示当前表结构
function showTableStructure($db, $tableName) {
    try {
        $stmt = $db->query("DESCRIBE {$tableName}");
        $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        echo "{$tableName} 表结构:\n";
        foreach ($columns as $column) {
            echo "{$column['Field']} - {$column['Type']} - {$column['Null']} - {$column['Key']} - {$column['Default']} - {$column['Extra']}\n";
        }
        echo "\n";
        
        return $columns;
    } catch (PDOException $e) {
        echo "获取表结构失败: " . $e->getMessage() . "\n";
        return [];
    }
}

// 检查并修复表结构
function checkAndFixTable($db, $tableName) {
    echo "检查 {$tableName} 表...\n";
    
    // 获取表结构
    $columns = showTableStructure($db, $tableName);
    $columnNames = array_column($columns, 'Field');
    
    // 检查是否存在content_type和content_id列
    $hasContentType = in_array('content_type', $columnNames);
    $hasContentId = in_array('content_id', $columnNames);
    $hasResourceType = in_array('resource_type', $columnNames);
    $hasResourceId = in_array('resource_id', $columnNames);
    
    // 如果同时存在旧列和新列，则删除旧列
    if ($hasContentType && $hasResourceType) {
        echo "表 {$tableName} 同时存在 content_type 和 resource_type 列，删除 content_type 列...\n";
        try {
            $db->exec("ALTER TABLE {$tableName} DROP COLUMN content_type");
            echo "成功删除 content_type 列\n";
        } catch (PDOException $e) {
            echo "删除 content_type 列失败: " . $e->getMessage() . "\n";
        }
    }
    
    if ($hasContentId && $hasResourceId) {
        echo "表 {$tableName} 同时存在 content_id 和 resource_id 列，删除 content_id 列...\n";
        try {
            $db->exec("ALTER TABLE {$tableName} DROP COLUMN content_id");
            echo "成功删除 content_id 列\n";
        } catch (PDOException $e) {
            echo "删除 content_id 列失败: " . $e->getMessage() . "\n";
        }
    }
    
    // 如果只有旧列没有新列，则重命名
    if ($hasContentType && !$hasResourceType) {
        echo "表 {$tableName} 存在 content_type 列但没有 resource_type 列，重命名列...\n";
        try {
            $db->exec("ALTER TABLE {$tableName} CHANGE COLUMN content_type resource_type varchar(20) NOT NULL COMMENT '资源类型(video/audio/image/article)'");
            echo "成功将 content_type 重命名为 resource_type\n";
        } catch (PDOException $e) {
            echo "重命名 content_type 列失败: " . $e->getMessage() . "\n";
        }
    }
    
    if ($hasContentId && !$hasResourceId) {
        echo "表 {$tableName} 存在 content_id 列但没有 resource_id 列，重命名列...\n";
        try {
            $db->exec("ALTER TABLE {$tableName} CHANGE COLUMN content_id resource_id int(11) NOT NULL COMMENT '资源ID'");
            echo "成功将 content_id 重命名为 resource_id\n";
        } catch (PDOException $e) {
            echo "重命名 content_id 列失败: " . $e->getMessage() . "\n";
        }
    }
    
    // 如果没有新列，则添加
    if (!$hasResourceType && !$hasContentType) {
        echo "表 {$tableName} 不存在 resource_type 列，添加列...\n";
        try {
            $db->exec("ALTER TABLE {$tableName} ADD COLUMN resource_type varchar(20) NOT NULL COMMENT '资源类型(video/audio/image/article)' AFTER user_id");
            echo "成功添加 resource_type 列\n";
        } catch (PDOException $e) {
            echo "添加 resource_type 列失败: " . $e->getMessage() . "\n";
        }
    }
    
    if (!$hasResourceId && !$hasContentId) {
        echo "表 {$tableName} 不存在 resource_id 列，添加列...\n";
        try {
            $db->exec("ALTER TABLE {$tableName} ADD COLUMN resource_id int(11) NOT NULL COMMENT '资源ID' AFTER resource_type");
            echo "成功添加 resource_id 列\n";
        } catch (PDOException $e) {
            echo "添加 resource_id 列失败: " . $e->getMessage() . "\n";
        }
    }
    
    // 如果是likes或favorites表，检查是否有ip_hash列
    if (($tableName == 'plb_links_likes' || $tableName == 'plb_links_favorites') && !in_array('ip_hash', $columnNames)) {
        echo "表 {$tableName} 不存在 ip_hash 列，添加列...\n";
        try {
            $db->exec("ALTER TABLE {$tableName} ADD COLUMN ip_hash varchar(32) NULL COMMENT 'IP地址哈希' AFTER user_id");
            echo "成功添加 ip_hash 列\n";
        } catch (PDOException $e) {
            echo "添加 ip_hash 列失败: " . $e->getMessage() . "\n";
        }
    }
    
    // 显示修改后的表结构
    echo "\n修改后的表结构:\n";
    showTableStructure($db, $tableName);
}

// 修复互动表
$interactionTables = [
    'plb_links_likes',
    'plb_links_favorites',
    'plb_links_comments',
    'plb_links_views'
];

foreach ($interactionTables as $table) {
    checkAndFixTable($db, $table);
    echo "\n";
}

echo "互动表修复完成！\n"; 