<?php
// 设置内容类型为JSON
header('Content-Type: application/json; charset=utf-8');

// 允许跨域请求
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

// 如果是OPTIONS请求，直接返回200
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

// 返回API v1版本信息
echo json_encode([
    'success' => true,
    'message' => 'PLB-Links API v1',
    'version' => '1.0.0',
    'endpoints' => [
        'auth' => '/api/v1/auth',
        'user' => '/api/v1/user',
        'links' => '/api/v1/links'
    ]
]); 