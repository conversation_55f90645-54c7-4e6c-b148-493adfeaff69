<?php
require_once __DIR__ . '/../../config/database.php';
require_once __DIR__ . '/../../includes/auth.php';

// 检查管理员权限
checkAdminAuth();

// 获取日志列表
function getLogs($filters = []) {
    $logFile = __DIR__ . '/../../log.json';
    if (!file_exists($logFile)) {
        return ['error' => '日志文件不存在'];
    }

    $logs = json_decode(file_get_contents($logFile), true);
    if (!$logs) {
        return ['error' => '日志文件格式错误'];
    }

    $filteredLogs = [];
    foreach ($logs['changes'] as $change) {
        // 应用过滤器
        if (!empty($filters['version']) && $change['version'] !== $filters['version']) {
            continue;
        }
        if (!empty($filters['type']) && $change['type'] !== $filters['type']) {
            continue;
        }
        if (!empty($filters['component']) && $change['component'] !== $filters['component']) {
            continue;
        }
        if (!empty($filters['dateRange'])) {
            $dateRange = explode(' - ', $filters['dateRange']);
            $changeDate = strtotime($change['date']);
            $startDate = strtotime($dateRange[0]);
            $endDate = strtotime($dateRange[1]);
            if ($changeDate < $startDate || $changeDate > $endDate) {
                continue;
            }
        }

        $filteredLogs[] = [
            'date' => $change['date'],
            'version' => $logs['version'],
            'type' => $change['type'],
            'component' => $change['component'],
            'description' => $change['description'],
            'details' => $change['details']
        ];
    }

    return ['data' => $filteredLogs];
}

// 导出日志
function exportLogs($filters = []) {
    $logs = getLogs($filters);
    if (isset($logs['error'])) {
        return $logs;
    }

    $filename = 'system_logs_' . date('Y-m-d') . '.csv';
    header('Content-Type: text/html; charset=utf-8');
    header('Content-Disposition: attachment; filename="' . $filename . '"');

    $output = fopen('php://output', 'w');
    fputcsv($output, ['日期', '版本', '类型', '组件', '描述', '详情']);

    foreach ($logs['data'] as $log) {
        fputcsv($output, [
            $log['date'],
            $log['version'],
            $log['type'],
            $log['component'],
            $log['description'],
            implode("\n", $log['details'])
        ]);
    }

    fclose($output);
    exit;
}

// 处理API请求
$action = $_GET['action'] ?? '';

switch ($action) {
    case 'search':
        $filters = [
            'version' => $_POST['version'] ?? '',
            'type' => $_POST['type'] ?? '',
            'component' => $_POST['component'] ?? '',
            'dateRange' => $_POST['dateRange'] ?? ''
        ];
        $result = getLogs($filters);
        header('Content-Type: text/html; charset=utf-8');
        echo json_encode($result);
        break;

    case 'export':
        $filters = [
            'version' => $_GET['version'] ?? '',
            'type' => $_GET['type'] ?? '',
            'component' => $_GET['component'] ?? '',
            'dateRange' => $_GET['dateRange'] ?? ''
        ];
        exportLogs($filters);
        break;

    default:
        header('HTTP/1.1 400 Bad Request');
        echo json_encode(['error' => '无效的请求']);
        break;
} 