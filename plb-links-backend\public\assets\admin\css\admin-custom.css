/* 确保模态框底部按钮始终可见 */
.modal-footer.sticky-footer {
    position: sticky;
    bottom: 0;
    background-color: #fff;
    z-index: 1020;
    padding: 1rem;
    border-top: 1px solid #dee2e6;
    box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
}

/* 增加模态框内容区域的最大高度 */
.modal-dialog-scrollable .modal-body {
    max-height: calc(100vh - 210px);
    overflow-y: auto;
}

/* 确保模态框不会超出屏幕 */
.modal-dialog-scrollable {
    display: flex;
    max-height: calc(100% - 40px);
}

/* 修复模态框显示问题 */
.modal {
    position: fixed;
    top: 0;
    left: 0;
    z-index: 1050;
    width: 100%;
    height: 100%;
    overflow: hidden;
    outline: 0;
}

/* 修复模态框对话框样式 */
.modal-dialog {
    position: relative;
    width: auto;
    margin: 0.5rem auto;
    pointer-events: none;
}

/* 标准模态框宽度 */
.modal-dialog {
    max-width: 500px;
}

/* 大号模态框宽度 */
.modal-lg {
    max-width: 800px;
}

/* 特大号模态框宽度 */
.modal-xl {
    max-width: 1140px;
}

/* 修复模态框内容样式 */
.modal-content {
    position: relative;
    display: flex;
    flex-direction: column;
    width: 100%;
    pointer-events: auto;
    background-color: #fff;
    background-clip: padding-box;
    border: 1px solid rgba(0, 0, 0, 0.2);
    border-radius: 0.3rem;
    outline: 0;
}

/* 修复红色区域溢出问题 */
.modal-body {
    position: relative;
    flex: 1 1 auto;
    padding: 1rem;
    overflow: hidden;
}

/* 修复模态框中的表单元素溢出问题 */
.modal-body input,
.modal-body select,
.modal-body textarea {
    max-width: 100%;
}

/* 修复模态框标题样式 */
.modal-header {
    display: flex;
    flex-shrink: 0;
    align-items: center;
    justify-content: space-between;
    padding: 1rem;
    border-bottom: 1px solid #dee2e6;
    border-top-left-radius: calc(0.3rem - 1px);
    border-top-right-radius: calc(0.3rem - 1px);
}

/* 修复模态框底部样式 */
.modal-footer {
    display: flex;
    flex-wrap: wrap;
    flex-shrink: 0;
    align-items: center;
    justify-content: flex-end;
    padding: 0.75rem;
    border-top: 1px solid #dee2e6;
    border-bottom-right-radius: calc(0.3rem - 1px);
    border-bottom-left-radius: calc(0.3rem - 1px);
}

/* 修复模态框背景 */
.modal-backdrop {
    position: fixed;
    top: 0;
    left: 0;
    z-index: 1040;
    width: 100vw;
    height: 100vh;
    background-color: #000;
}

.modal-backdrop.fade {
    opacity: 0;
}

.modal-backdrop.show {
    opacity: 0.5;
}

/* 图片九宫格样式 */
.image-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    grid-gap: 10px;
}

.image-grid-item {
    position: relative;
    width: 100%;
    padding-bottom: 100%; /* 1:1 Aspect Ratio */
    background-color: #f8f9fa;
    border: 1px dashed #dee2e6;
    border-radius: 4px;
    cursor: pointer;
    overflow: hidden;
}

.image-grid-item img.grid-image {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    object-fit: cover;
    z-index: 1;
}

.upload-placeholder {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    color: #6c757d;
    cursor: pointer;
    z-index: 2;
    transition: all 0.3s ease;
}

.upload-placeholder.debug-highlight {
    background-color: rgba(255, 193, 7, 0.7);
    border: 2px solid #ff9800;
    z-index: 3;
    transform: scale(1.05);
    box-shadow: 0 0 10px rgba(255, 152, 0, 0.5);
}

.upload-placeholder i {
    font-size: 24px;
    margin-bottom: 5px;
}

.upload-placeholder span {
    font-size: 12px;
}

.image-actions {
    position: absolute;
    top: 5px;
    right: 5px;
    display: none;
    z-index: 10;
    gap: 5px;
}

.image-grid-item:hover .image-actions {
    display: flex;
}

.btn-image-action {
    width: 28px;
    height: 28px;
    padding: 0;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.9);
    border: 1px solid #dee2e6;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    display: inline-flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s;
}

.btn-image-action:hover {
    transform: scale(1.1);
    box-shadow: 0 3px 6px rgba(0, 0, 0, 0.15);
}

.btn-image-delete {
    color: #dc3545;
}

.btn-image-delete:hover {
    background: #dc3545;
    color: white;
    border-color: #dc3545;
}

.btn-image-preview {
    color: #0d6efd;
}

.btn-image-preview:hover {
    background: #0d6efd;
    color: white;
    border-color: #0d6efd;
}

/* 图片预览模态框样式 */
#imagePreviewModal .modal-body {
    padding: 0;
    text-align: center;
    background-color: #000;
}

#imagePreviewModal .modal-body img {
    max-width: 100%;
    max-height: 70vh;
    object-fit: contain;
}

/* 上传进度样式 */
.upload-progress {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(255, 255, 255, 0.8);
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    z-index: 5;
}

.upload-progress .spinner-border {
    width: 1.5rem;
    height: 1.5rem;
}

/* 增加生成结果区域的可读性 */
#aiDescContent, #aiSummaryContent {
    max-height: 300px;
    overflow-y: auto;
    border: 1px solid #dee2e6;
    border-radius: 0.25rem;
    padding: 0.5rem;
}

/* 使用此描述按钮样式增强 */
#useDescButton, #useSummaryButton {
    position: sticky;
    bottom: 0;
    background-color: #fff;
    border-top: 1px solid #dee2e6;
    margin-top: 10px;
}

#useDescButton:hover, #useSummaryButton:hover {
    background-color: #f8f9fa;
}

/* 图库选择器样式 */
.gallery-thumbnail-container {
    position: relative;
    padding-bottom: 100%; /* 1:1宽高比 */
    overflow: hidden;
    background-color: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 4px 4px 0 0;
}

.gallery-thumbnail {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.gallery-video-thumbnail {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: #000;
}

.gallery-video-thumbnail video {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.video-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: rgba(0, 0, 0, 0.4);
    color: white;
}

.video-overlay i {
    font-size: 2rem;
    opacity: 0.8;
}

.gallery-item {
    cursor: pointer;
    transition: all 0.2s ease;
    border: 2px solid transparent;
}

.gallery-item:hover {
    transform: translateY(-3px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    border-color: #dee2e6;
}

.gallery-item.selected {
    border-color: #0d6efd;
    box-shadow: 0 0 0 3px rgba(13, 110, 253, 0.25);
} 