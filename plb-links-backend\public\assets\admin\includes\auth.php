<?php
session_start();

// 检查管理员权限
function checkAdminAuth() {
    if (!isset($_SESSION['admin_id']) || !isset($_SESSION['admin_role'])) {
        header('HTTP/1.1 401 Unauthorized');
        echo json_encode(['error' => '未登录或权限不足']);
        exit;
    }

    // 检查是否是超级管理员
    if ($_SESSION['admin_role'] !== 'super_admin') {
        header('HTTP/1.1 403 Forbidden');
        echo json_encode(['error' => '权限不足']);
        exit;
    }
}

// 检查普通管理员权限
function checkNormalAdminAuth() {
    if (!isset($_SESSION['admin_id']) || !isset($_SESSION['admin_role'])) {
        header('HTTP/1.1 401 Unauthorized');
        echo json_encode(['error' => '未登录或权限不足']);
        exit;
    }
}

// 获取当前管理员信息
function getCurrentAdmin() {
    if (!isset($_SESSION['admin_id'])) {
        return null;
    }

    return [
        'id' => $_SESSION['admin_id'],
        'username' => $_SESSION['admin_username'],
        'role' => $_SESSION['admin_role'],
        'last_login' => $_SESSION['admin_last_login']
    ];
} 