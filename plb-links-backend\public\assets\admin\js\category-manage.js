// category-manage.js
$(function () {
    console.log('分类管理页面初始化开始');
    
    // 分类类型映射
    const typeMap = {
        video: {
            name: '视频分类',
            api: '/api/admin/category-list.php?type=video'
        },
        image: {
            name: '图片分类',
            api: '/api/admin/category-list.php?type=image'
        },
        audio: {
            name: '音频分类',
            api: '/api/admin/category-list.php?type=audio'
        },
        goods: {
            name: '商品分类',
            api: '/api/admin/category-list.php?type=goods'
        }
    };
    let currentType = 'video';
    let table = null;

    // 初始化Tab点击
    $('.category-tab').on('click', function () {
        const type = $(this).data('type');
        console.log('点击分类Tab:', type);
        
        if (type === currentType) {
            console.log('已经是当前类型，重新加载表格');
            loadTable();
            return;
        }
        
        // 确保类型存在于typeMap中
        if (!typeMap[type]) {
            console.error('未知的分类类型:', type);
            return;
        }
        
        currentType = type;
        $('.category-tab').removeClass('active');
        $(this).addClass('active');
        loadTable();
    });

    // 初始化表格
    function loadTable() {
        console.log('开始加载表格，当前类型:', currentType);
        
        // 确保当前类型有效
        if (!typeMap[currentType] || !typeMap[currentType].api) {
            console.error('无效的分类类型或API路径未定义:', currentType);
            return;
        }
        
        const apiUrl = typeMap[currentType].api;
        console.log('加载分类表格，API:', apiUrl);
        
        // 先通过AJAX直接测试API
        $.ajax({
            url: apiUrl,
            method: 'GET',
            dataType: 'json',
            cache: false,
            success: function(response) {
                console.log('API直接测试结果:', response);
                
                // 继续初始化DataTable
                initDataTable(response);
            },
            error: function(xhr, status, error) {
                console.error('API直接测试错误:', error, xhr.responseText);
                alert('加载分类数据失败，请查看控制台错误');
            }
        });
    }
    
    // 初始化DataTable
    function initDataTable(preloadedData) {
        if (table) {
            console.log('销毁现有表格');
            table.destroy();
            $('#categoryTable tbody').empty();
        }
        
        try {
            console.log('初始化DataTable');
            
            // 如果有预加载的数据，使用本地数据源
            if (preloadedData && preloadedData.data) {
                console.log('使用预加载数据初始化表格:', preloadedData.data.length, '条记录');
                table = $('#categoryTable').DataTable({
                    data: preloadedData.data,
                    columns: [
                        { data: 'id' },
                        { data: 'name' },
                        { data: 'sort' },
                        { data: 'description' },
                        {
                            data: null,
                            render: function (data, type, row) {
                                return `<button class="btn btn-sm btn-info edit-btn" data-id="${row.id}">编辑</button>
                                        <button class="btn btn-sm btn-danger delete-btn" data-id="${row.id}">删除</button>`;
                            }
                        }
                    ],
                    pageLength: 10,
                    order: [[2, 'asc']],
                    responsive: true,
                    processing: true,
                    language: {
                        processing: "加载中...",
                        emptyTable: "没有数据",
                        info: "显示 _START_ 到 _END_ 条，共 _TOTAL_ 条",
                        infoEmpty: "显示 0 到 0 条，共 0 条",
                        infoFiltered: "(从 _MAX_ 条中过滤)",
                        paginate: {
                            first: "首页",
                            last: "尾页",
                            next: "下一页",
                            previous: "上一页"
                        }
                    }
                });
            } else {
                // 使用AJAX数据源
                console.log('使用AJAX数据源初始化表格');
                table = $('#categoryTable').DataTable({
                    ajax: {
                        url: typeMap[currentType].api,
                        dataSrc: function (json) {
                            console.log('获取到数据:', json);
                            return json.data || [];
                        },
                        cache: false,
                        error: function(xhr, error, thrown) {
                            console.error('DataTables AJAX错误:', error, thrown);
                        }
                    },
                    columns: [
                        { data: 'id' },
                        { data: 'name' },
                        { data: 'sort' },
                        { data: 'description' },
                        {
                            data: null,
                            render: function (data, type, row) {
                                return `<button class="btn btn-sm btn-info edit-btn" data-id="${row.id}">编辑</button>
                                        <button class="btn btn-sm btn-danger delete-btn" data-id="${row.id}">删除</button>`;
                            }
                        }
                    ],
                    pageLength: 10,
                    order: [[2, 'asc']],
                    responsive: true,
                    processing: true,
                    language: {
                        processing: "加载中...",
                        emptyTable: "没有数据",
                        info: "显示 _START_ 到 _END_ 条，共 _TOTAL_ 条",
                        infoEmpty: "显示 0 到 0 条，共 0 条",
                        infoFiltered: "(从 _MAX_ 条中过滤)",
                        paginate: {
                            first: "首页",
                            last: "尾页",
                            next: "下一页",
                            previous: "上一页"
                        }
                    }
                });
            }
            
            console.log('表格初始化完成');
        } catch (e) {
            console.error('初始化DataTable错误:', e);
        }
    }

    // 新增分类
    $('#addCategoryBtn').on('click', function () {
        $('#editCategoryId').val('');
        $('#editCategoryName').val('');
        $('#editCategorySort').val('0');
        $('#editCategoryDesc').val('');
        $('#editCategoryModalLabel').text('新增分类');
        $('#editCategoryModal').modal('show');
    });

    // 编辑分类
    $('#categoryTable').on('click', '.edit-btn', function () {
        const id = $(this).data('id');
        console.log('编辑分类:', id, '类型:', currentType);
        
        $.get(`/api/admin/category-get.php?id=${id}&type=${currentType}`, function (res) {
            console.log('获取分类详情结果:', res);
            
            if (res.code === 0) {
                const c = res.data;
                $('#editCategoryId').val(c.id);
                $('#editCategoryName').val(c.name);
                $('#editCategorySort').val(c.sort);
                $('#editCategoryDesc').val(c.description);
                $('#editCategoryModalLabel').text('编辑分类');
                $('#editCategoryModal').modal('show');
            } else {
                Swal.fire('错误', res.message, 'error');
            }
        }).fail(function(xhr, status, error) {
            console.error('获取分类详情失败:', error, xhr.responseText);
            Swal.fire('错误', '获取分类详情失败', 'error');
        });
    });

    // 删除分类
    $('#categoryTable').on('click', '.delete-btn', function () {
        const id = $(this).data('id');
        console.log('删除分类:', id, '类型:', currentType);
        
        Swal.fire({
            title: '确认删除？',
            icon: 'warning',
            showCancelButton: true,
            confirmButtonText: '确定',
            cancelButtonText: '取消'
        }).then(result => {
            if (result.isConfirmed) {
                $.ajax({
                    url: '/api/admin/category-delete.php',
                    type: 'POST',
                    contentType: 'application/json',
                    data: JSON.stringify({ id, type: currentType }),
                    success: function (res) {
                        console.log('删除分类结果:', res);
                        
                        if (res.code === 0) {
                            Swal.fire('成功', '已删除', 'success');
                            loadTable(); // 重新加载表格
                        } else {
                            Swal.fire('错误', res.message, 'error');
                        }
                    },
                    error: function(xhr, status, error) {
                        console.error('删除分类失败:', error, xhr.responseText);
                        Swal.fire('错误', '删除分类失败', 'error');
                    }
                });
            }
        });
    });

    // 保存分类（新增/编辑）
    $('#editCategoryForm').on('submit', function (e) {
        e.preventDefault();
        const id = $('#editCategoryId').val();
        const data = {
            id,
            name: $('#editCategoryName').val(),
            sort: $('#editCategorySort').val(),
            description: $('#editCategoryDesc').val(),
            type: currentType
        };
        
        console.log('保存分类:', data);
        
        const url = id ? '/api/admin/category-edit.php' : '/api/admin/category-add.php';
        $.ajax({
            url,
            type: 'POST',
            contentType: 'application/json',
            data: JSON.stringify(data),
            success: function (res) {
                console.log('保存分类结果:', res);
                
                if (res.code === 0) {
                    $('#editCategoryModal').modal('hide');
                    Swal.fire({
                        title: '成功',
                        text: id ? '已更新' : '已添加',
                        icon: 'success',
                        timer: 1500,
                        showConfirmButton: false
                    });
                    
                    // 立即重新加载表格数据
                    setTimeout(function() {
                        loadTable(); // 完全重新加载表格
                    }, 500);
                } else {
                    Swal.fire('错误', res.message, 'error');
                }
            },
            error: function(xhr, status, error) {
                console.error("AJAX错误:", error, xhr.responseText);
                Swal.fire('错误', '保存失败，请重试', 'error');
            }
        });
    });

    // 初始化
    console.log('初始化分类管理页面');
    loadTable();
}); 