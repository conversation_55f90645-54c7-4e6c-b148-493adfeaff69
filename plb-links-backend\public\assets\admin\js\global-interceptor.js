/**
 * 全局请求拦截器
 * 确保所有fetch请求都带上凭据
 */

// 保存原始的fetch函数
const originalFetch = window.fetch;

// 重写fetch函数
window.fetch = function(url, options = {}) {
    console.log('全局拦截器: 拦截到fetch请求', url);
    
    // 确保options对象存在
    options = options || {};
    
    // 始终添加credentials选项，确保请求带上Cookie
    options.credentials = 'same-origin';
    
    // 添加调试信息
    console.log('全局拦截器: 设置credentials为same-origin');
    console.log('全局拦截器: 最终请求选项', options);
    
    // 调用原始fetch函数
    return originalFetch(url, options)
        .then(response => {
            // 记录响应状态
            console.log('全局拦截器: 请求响应状态', response.status);
            
            // 如果是401未授权，可以在这里处理重定向到登录页面
            if (response.status === 401) {
                console.error('全局拦截器: 未授权访问，请重新登录');
                // 可以在这里添加重定向到登录页面的逻辑
                // window.location.href = '/admin/login';
            }
            
            return response;
        })
        .catch(error => {
            console.error('全局拦截器: 请求失败', error);
            throw error;
        });
}; 