// 日志管理页面脚本
$(document).ready(function() {
    // 初始化日期范围选择器
    initDateRangePicker();

    // 初始化日志表格
    const logTable = $('#logTable').DataTable({
        language: {
            url: '/assets/admin/js/zh-CN.json'
        },
        order: [[0, 'desc']],
        pageLength: 25,
        ajax: {
            url: '/api/admin/logs/search',
            data: function(d) {
                return {
                    version: $('#versionSearch').val(),
                    type: $('#typeSearch').val(),
                    component: $('#componentSearch').val(),
                    dateRange: $('#dateRange').val(),
                    operator: $('#operatorSearch').val(),
                    keyword: $('#keywordSearch').val()
                };
            },
            dataSrc: function(json) {
                if (json.success) {
                    return json.data;
                }
                showError('加载日志失败：' + (json.message || '未知错误'));
                return [];
            }
        },
        columns: [
            { data: 'date', title: '日期' },
            { data: 'version', title: '版本' },
            { data: 'type', title: '类型' },
            { data: 'component', title: '组件' },
            { data: 'description', title: '描述' },
            {
                data: 'details',
                title: '详情',
                render: function(data, type, row) {
                    if (type === 'display' && Array.isArray(data)) {
                        return `<ul class="log-details-list">${data.map(detail => `<li>${detail}</li>`).join('')}</ul>`;
                    }
                    return data ? data.join(', ') : '';
                }
            },
            {
                data: null,
                title: '操作',
                orderable: false,
                render: function(data, type, row) {
                    return `
                        <button class="btn btn-sm btn-primary edit-log" data-id="${row.id || ''}">编辑</button>
                        <button class="btn btn-sm btn-danger delete-log" data-id="${row.id || ''}">删除</button>
                    `;
                }
            }
        ]
    });

    // 绑定搜索事件
    $('#logSearchForm').on('submit', function(e) {
        e.preventDefault();
        logTable.ajax.reload();
    });

    // 绑定重置事件
    $('#logSearchForm').on('reset', function() {
        setTimeout(function() {
            logTable.ajax.reload();
        }, 0);
    });

    // 绑定导出事件
    $('#exportLogs').on('click', function() {
        const searchData = {
            version: $('#versionSearch').val(),
            type: $('#typeSearch').val(),
            component: $('#componentSearch').val(),
            dateRange: $('#dateRange').val(),
            operator: $('#operatorSearch').val(),
            keyword: $('#keywordSearch').val()
        };
        const queryString = new URLSearchParams(searchData).toString();
        window.location.href = `/api/admin/logs/export?${queryString}`;
    });

    // 编辑日志
    $('#logTable').on('click', '.edit-log', function() {
        const data = logTable.row($(this).parents('tr')).data();
        $('#editLogId').val(data.id || '');
        $('#editLogDate').val(data.date);
        $('#editLogVersion').val(data.version);
        $('#editLogType').val(data.type);
        $('#editLogComponent').val(data.component);
        $('#editLogOperator').val(data.operator || '');
        $('#editLogDescription').val(data.description);
        $('#editLogDetails').val(Array.isArray(data.details) ? data.details.join('\n') : (data.details || ''));
        $('#editLogModal').modal('show');
    });

    // 保存编辑
    $('#saveLogBtn').on('click', function() {
        const logData = {
            id: $('#editLogId').val(),
            date: $('#editLogDate').val(),
            version: $('#editLogVersion').val(),
            type: $('#editLogType').val(),
            component: $('#editLogComponent').val(),
            operator: $('#editLogOperator').val(),
            description: $('#editLogDescription').val(),
            details: $('#editLogDetails').val().split('\n').filter(Boolean)
        };
        $.ajax({
            url: '/api/admin/logs/edit',
            method: 'POST',
            contentType: 'application/json',
            data: JSON.stringify(logData),
            success: function(res) {
                if (res.success) {
                    $('#editLogModal').modal('hide');
                    showSuccess('保存成功');
                    logTable.ajax.reload();
                } else {
                    showError('保存失败：' + (res.message || '未知错误'));
                }
            },
            error: function(xhr) {
                showError('保存失败：' + (xhr.responseJSON?.message || '未知错误'));
            }
        });
    });

    // 删除日志
    $('#logTable').on('click', '.delete-log', function() {
        const data = logTable.row($(this).parents('tr')).data();
        Swal.fire({
            title: '确定删除该日志？',
            icon: 'warning',
            showCancelButton: true,
            confirmButtonText: '删除',
            cancelButtonText: '取消'
        }).then((result) => {
            if (result.isConfirmed) {
                $.ajax({
                    url: '/api/admin/logs/delete',
                    method: 'POST',
                    contentType: 'application/json',
                    data: JSON.stringify({ id: data.id }),
                    success: function(res) {
                        if (res.success) {
                            showSuccess('删除成功');
                            logTable.ajax.reload();
                        } else {
                            showError('删除失败：' + (res.message || '未知错误'));
                        }
                    },
                    error: function(xhr) {
                        showError('删除失败：' + (xhr.responseJSON?.message || '未知错误'));
                    }
                });
            }
        });
    });
});

// 初始化日期范围选择器
function initDateRangePicker() {
    $('#dateRange').daterangepicker({
        locale: {
            format: 'YYYY-MM-DD',
            applyLabel: '确定',
            cancelLabel: '取消',
            fromLabel: '从',
            toLabel: '至',
            customRangeLabel: '自定义',
            daysOfWeek: ['日', '一', '二', '三', '四', '五', '六'],
            monthNames: ['一月', '二月', '三月', '四月', '五月', '六月', '七月', '八月', '九月', '十月', '十一月', '十二月']
        },
        autoUpdateInput: false
    });
    $('#dateRange').on('apply.daterangepicker', function(ev, picker) {
        $(this).val(picker.startDate.format('YYYY-MM-DD') + ' - ' + picker.endDate.format('YYYY-MM-DD'));
    });
    $('#dateRange').on('cancel.daterangepicker', function(ev, picker) {
        $(this).val('');
    });
}

// 显示错误提示
function showError(message) {
    Swal.fire({
        icon: 'error',
        title: '错误',
        text: message,
        confirmButtonText: '确定'
    });
}

// 显示成功信息
function showSuccess(message) {
    Swal.fire({
        icon: 'success',
        title: '成功',
        text: message,
        confirmButtonText: '确定'
    });
} 