/**
 * 媒体管理AI辅助函数
 */

// 辅助函数：移除Markdown符号和其他特殊标记
function removeMdSymbols(text) {
    if (!text) return '';
    return text
        .replace(/[*_`#]/g, '') // 移除*_`#等常用MD符号
        .replace(/\[([^\]]+)\]\([^)]+\)/g, '$1') // 将[链接文本](链接URL)改为链接文本
        .replace(/!\[([^\]]*)\]\([^)]+\)/g, '') // 移除图片语法
        .replace(/^>+\s*/gm, '') // 移除引用符号
        .replace(/^[-+*]\s+/gm, '') // 移除列表符号
        .replace(/^(\d+)\.?\s+/gm, '$1. ') // 保留数字但确保格式一致
        .replace(/^#+\s+/gm, '') // 移除标题符号
        .replace(/~~([^~]+)~~/g, '$1') // 移除删除线
        .replace(/^["']|["']$/g, '') // 移除首尾的引号
        .replace(/\(\d+字\)|\（\d+字\)|\(\d+字\）|\（\d+字\）/g, '') // 移除字数标记，如(100字)或（100字）
        .replace(/\(\d+个?字符\)|\（\d+个?字符\)|\(\d+个?字符\）|\（\d+个?字符\）/g, '') // 移除字符计数
        .replace(/\s*\(\d+\)\s*$|\s*（\d+）\s*$/, '') // 移除末尾的数字标记，如(99)或（99）
        .replace(/^(描述：|摘要：|简介：|内容：)/i, '') // 移除常见前缀
        .replace(/^(这个|此|本|该)\s*(视频|音频|图片|文章|内容)(\s*是|将|：)/i, '') // 移除常见开头格式
        .trim();
}

// 本地生成描述的备选方案函数
function generateLocalDescription(title, mediaType) {
    // 提取标题中的关键信息
    const titleWords = title.replace(/[^\u4e00-\u9fa5a-zA-Z0-9]/g, ' ').split(/\s+/).filter(w => w.length > 0);
    let mainTopic = titleWords.length > 0 ? titleWords[0] : '内容';
    
    // 根据不同媒体类型生成对应的描述模板
    let description = '';
    switch(mediaType) {
        case 'video':
            description = `这是一段关于${title}的精彩视频，详细展示了相关内容，包含丰富的信息和知识点。视频制作精良，画面清晰，内容生动有趣，值得观看。`;
            break;
        case 'audio':
            description = `这是一段关于${title}的音频内容，声音清晰，内容丰富。通过专业的讲解，让听众可以轻松了解相关知识，获得良好的听觉体验。`;
            break;
        case 'image':
            description = `这是一张展示${title}的图片，画面精美，内容清晰。通过视觉化的方式，直观地呈现了相关信息，让观众一目了然。`;
            break;
        case 'article':
            description = `这是一篇关于${title}的文章，内容丰富，观点独到。文章分析透彻，论述有力，为读者提供了全面深入的见解和知识。`;
            break;
        default:
            description = `这是关于${title}的内容，提供了丰富的信息和详细的讲解，帮助用户全面了解相关知识。内容经过精心整理，易于理解和掌握。`;
    }
    
    return description;
}

// 根据标题生成描述
function generateDescriptionFromTitle(title, mediaType) {
    return new Promise((resolve, reject) => {
        if (!title) {
            reject(new Error('请先输入标题'));
            return;
        }

        console.log(`开始根据标题生成描述，标题: ${title}, 媒体类型: ${mediaType}`);

        // 获取AI服务商ID
        let providerId = 0;
        try {
            // 尝试从页面中获取AI服务商选择框
            const aiProviderSelect = document.querySelector('#aiProvider');
            if (aiProviderSelect && aiProviderSelect.value) {
                providerId = parseInt(aiProviderSelect.value);
                console.log(`使用选择的AI服务商ID: ${providerId}`);
            } else {
                console.log('未找到AI服务商选择框或值为空，将使用默认服务商');
            }
        } catch (e) {
            console.error('获取AI服务商ID失败:', e);
        }

        // 显示加载提示
        const loadingToast = Swal.fire({
            title: '正在生成描述...',
            text: '请稍候 (最多需要30秒)',
            icon: 'info',
            allowOutsideClick: false,
            showConfirmButton: false,
            scrollbarPadding: false,
            heightAuto: false,
            didOpen: () => {
                Swal.showLoading();
            }
        });

        // 创建AbortController用于超时控制
        const controller = new AbortController();
        const timeoutId = setTimeout(() => controller.abort(), 65000); // 65秒超时，给AI更多时间

        // 使用当前时间戳避免缓存
        const timestamp = new Date().getTime();
        const apiUrl = `/api/v1/ai/generate-summary-from-title?_=${timestamp}`;

        // 调用API生成描述
        fetch(apiUrl, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-Requested-With': 'XMLHttpRequest',
                'Cache-Control': 'no-cache, no-store, must-revalidate'
            },
            body: JSON.stringify({
                title: title,
                media_type: mediaType,
                provider_id: providerId,
                use_default_provider: true
            }),
            signal: controller.signal,
            credentials: 'same-origin'
        })
        .then(response => {
            console.log(`API响应状态: ${response.status}`);
            clearTimeout(timeoutId); // 清除超时计时器
            if (!response.ok) {
                throw new Error(`服务器响应错误: ${response.status}`);
            }
            return response.json();
        })
        .then(data => {
            console.log('API响应数据:', data);
            loadingToast.close();
            
            if (data.success && data.data && data.data.description) {
                // 移除Markdown符号
                const cleanDescription = removeMdSymbols(data.data.description);
                console.log('生成描述成功，已清理Markdown符号');
                
                // 确保描述不超过100字
                const limitedDescription = cleanDescription.length > 100 
                    ? cleanDescription.substring(0, 100) + '...' 
                    : cleanDescription;
                
                resolve(limitedDescription);
            } else {
                const errorMsg = data.message || '生成描述失败';
                console.error(`生成描述失败: ${errorMsg}`);
                
                // 降级：使用本地模板生成描述
                console.log('API生成失败，使用本地模板生成描述');
                const localDescription = generateLocalDescription(title, mediaType);
                
                // 确保描述不超过100字
                const limitedDescription = localDescription.length > 100 
                    ? localDescription.substring(0, 100) + '...' 
                    : localDescription;
                
                // 使用本地生成的描述
                Swal.fire({
                    title: '注意',
                    text: 'AI服务暂时不可用，已使用本地模板生成描述',
                    icon: 'info',
                    confirmButtonText: '确定',
                    scrollbarPadding: false,
                    heightAuto: false
                });
                
                resolve(limitedDescription);
            }
        })
        .catch(error => {
            clearTimeout(timeoutId); // 清除超时计时器
            console.error('生成描述失败:', error);
            loadingToast.close();

            // 判断错误类型
            let errorMessage = 'AI服务暂时不可用';
            if (error.name === 'AbortError') {
                errorMessage = 'AI服务响应超时（超过30秒）';
            } else if (error.message && error.message.includes('504')) {
                errorMessage = 'AI服务响应超时，服务器处理时间过长';
            } else if (error.message && error.message.includes('timeout')) {
                errorMessage = 'AI服务连接超时';
            }

            // 降级：使用本地模板生成描述
            console.log('API请求失败，使用本地模板生成描述');
            const localDescription = generateLocalDescription(title, mediaType);

            // 确保描述不超过100字
            const limitedDescription = localDescription.length > 100
                ? localDescription.substring(0, 100) + '...'
                : localDescription;

            // 使用本地生成的描述
            Swal.fire({
                title: '连接AI服务失败',
                text: `${errorMessage}，已使用本地模板生成描述`,
                icon: 'info',
                confirmButtonText: '确定',
                scrollbarPadding: false,
                heightAuto: false
            });

            resolve(limitedDescription);
        });
    });
}

// 初始化媒体标题生成功能
function initMediaTitleGeneration(mediaType) {
    const generateTitleBtn = document.getElementById('generateTitleBtn');
    if (!generateTitleBtn) return;

    generateTitleBtn.addEventListener('click', function() {
        // 获取关键词
        const keywords = document.getElementById('titleKeywords').value.trim();
        
        if (!keywords) {
            Swal.fire({
                title: '输入错误',
                text: '请输入关键词',
                icon: 'warning',
                confirmButtonText: '确定',
                scrollbarPadding: false,
                heightAuto: false
            });
            return;
        }
        
        // 显示加载状态
        const button = this;
        const originalText = button.innerHTML;
        button.disabled = true;
        button.innerHTML = '<span class="spinner-border spinner-border-sm" role="status"></span> 生成中...';
        
        // 显示加载提示
        const loadingToast = Swal.fire({
            title: '正在生成标题...',
            text: '请稍候',
            icon: 'info',
            allowOutsideClick: false,
            showConfirmButton: false,
            scrollbarPadding: false,
            heightAuto: false,
            didOpen: () => {
                Swal.showLoading();
            }
        });
        
        // 调用API生成标题
        fetch('/api/v1/ai/generate-title', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-Requested-With': 'XMLHttpRequest'
            },
            body: JSON.stringify({
                keywords: keywords,
                media_type: mediaType,
                max_length: 30
            })
        })
        .then(response => {
            if (!response.ok) {
                throw new Error('服务器响应错误: ' + response.status);
            }
            return response.json();
        })
        .then(data => {
            // 恢复按钮状态
            button.disabled = false;
            button.innerHTML = originalText;
            loadingToast.close();
            
            if (data.success && data.data && data.data.titles && data.data.titles.length > 0) {
                // 从返回的标题数组中选择第一个并移除Markdown符号
                const cleanTitle = removeMdSymbols(data.data.titles[0]);
                document.getElementById('title').value = cleanTitle;
                
                Swal.fire({
                    title: '成功',
                    text: '标题生成成功！',
                    icon: 'success',
                    confirmButtonText: '确定',
                    scrollbarPadding: false,
                    heightAuto: false
                });
            } else {
                Swal.fire({
                    title: '生成失败',
                    text: data.message || '未知错误',
                    icon: 'error',
                    confirmButtonText: '确定',
                    scrollbarPadding: false,
                    heightAuto: false
                });
            }
        })
        .catch(error => {
            console.error('生成标题失败:', error);
            button.disabled = false;
            button.innerHTML = originalText;
            loadingToast.close();

            // 判断错误类型
            let errorMessage = '服务器连接错误';
            if (error.name === 'AbortError') {
                errorMessage = 'AI服务响应超时（超过30秒），请稍后重试';
            } else if (error.message && error.message.includes('504')) {
                errorMessage = 'AI服务响应超时，服务器处理时间过长，请稍后重试';
            } else if (error.message && error.message.includes('timeout')) {
                errorMessage = 'AI服务连接超时，请检查网络连接';
            } else if (error.message) {
                errorMessage = error.message;
            }

            Swal.fire({
                title: '生成失败',
                text: errorMessage,
                icon: 'error',
                confirmButtonText: '确定',
                scrollbarPadding: false,
                heightAuto: false
            });
        });
    });
}

// 初始化根据标题生成描述功能
function initDescriptionGeneration(mediaType) {
    const generateDescBtn = document.getElementById('generateDescBtn');
    if (!generateDescBtn) return;

    generateDescBtn.addEventListener('click', function() {
        const title = document.getElementById('title').value.trim();
        
        if (!title) {
            Swal.fire({
                title: '输入错误',
                text: '请先输入标题',
                icon: 'warning',
                confirmButtonText: '确定',
                scrollbarPadding: false,
                heightAuto: false
            });
            return;
        }
        
        // 显示加载状态
        const button = this;
        const originalText = button.innerHTML;
        button.disabled = true;
        button.innerHTML = '<span class="spinner-border spinner-border-sm" role="status"></span> 生成中...';
        
        generateDescriptionFromTitle(title, mediaType)
            .then(description => {
                // 恢复按钮状态
                button.disabled = false;
                button.innerHTML = originalText;
                
                // 设置描述
                document.getElementById('description').value = description;
                
                Swal.fire({
                    title: '成功',
                    text: '描述生成成功！',
                    icon: 'success',
                    confirmButtonText: '确定',
                    scrollbarPadding: false,
                    heightAuto: false
                });
            })
            .catch(error => {
                console.error('生成描述失败:', error);
                button.disabled = false;
                button.innerHTML = originalText;
                
                // 错误已经在generateDescriptionFromTitle函数中显示，这里不需要重复显示
            });
    });
} 